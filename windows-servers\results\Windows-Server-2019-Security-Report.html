<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows Server 2019 Security Audit Report -  </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .metadata {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        
        .metadata h2 {
            color: #1e3c72;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .metadata-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .metadata-label {
            font-weight: 600;
            color: #495057;
        }
        
        .metadata-value {
            color: #6c757d;
            text-align: right;
        }
        
        .executive-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .executive-summary h2 {
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .risk-dashboard {
            margin-bottom: 30px;
        }
        
        .risk-dashboard h2 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .risk-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .risk-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        
        .risk-critical { background: linear-gradient(135deg, #dc3545, #c82333); }
        .risk-high { background: linear-gradient(135deg, #fd7e14, #e55a00); }
        .risk-medium { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .risk-low { background: linear-gradient(135deg, #28a745, #1e7e34); }
        
        .findings-table {
            margin-bottom: 30px;
        }
        
        .findings-table h2 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 12px 10px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e9ecef;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }
        
        .status-compliant {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-non-compliant {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .risk-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .risk-badge.critical { background-color: #dc3545; }
        .risk-badge.high { background-color: #fd7e14; }
        .risk-badge.medium { background-color: #ffc107; color: #212529; }
        .risk-badge.low { background-color: #28a745; }
        
        .remediation {
            margin-bottom: 30px;
        }
        
        .remediation h2 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .remediation-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .remediation-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }
        
        .remediation-content {
            padding: 15px;
        }
        
        .cvss-score {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 4px;
            color: white;
            font-size: 0.9em;
        }
        
        .cvss-critical { background-color: #dc3545; }
        .cvss-high { background-color: #fd7e14; }
        .cvss-medium { background-color: #ffc107; color: #212529; }
        .cvss-low { background-color: #28a745; }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }
        
        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
            .header { background: #1e3c72 !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Windows Server 2019 Security Audit Report</h1>
            <div class="subtitle">  Domain Security Assessment</div>
        </div>

        <div class="metadata">
            <h2>📋 Audit Metadata</h2>
            <div class="metadata-grid">
                <div class="metadata-item">
                    <span class="metadata-label">Server Name:</span>
                    <span class="metadata-value"> </span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Domain:</span>
                    <span class="metadata-value"> </span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Operating System:</span>
                    <span class="metadata-value">Microsoft Windows Server 2019 Standard</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Windows Version:</span>
                    <span class="metadata-value">1809 (Build 17763)</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Architecture:</span>
                    <span class="metadata-value">64-bit</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Audit User:</span>
                    <span class="metadata-value"> </span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Assessment Scope:</span>
                    <span class="metadata-value">8 Critical Windows Server Security Controls</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Audit Duration:</span>
                    <span class="metadata-value">10.76 seconds</span>
                </div>
            </div>
        </div>

        <div class="executive-summary">
            <h2>🎯 Executive Summary</h2>
            <p style="font-size: 1.1em; text-align: center; margin-bottom: 20px;">
                Security assessment of Windows Server 2019 ( ) reveals mixed compliance with critical security controls. 
                Immediate attention required for Windows Update and Firewall configurations.
            </p>
            <div class="summary-stats">
                <div class="stat-card">
                    <span class="stat-number">8</span>
                    <div class="stat-label">Controls Assessed</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">75%</span>
                    <div class="stat-label">Average Compliance</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">1</span>
                    <div class="stat-label">Critical Issues</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">2</span>
                    <div class="stat-label">High Risk Issues</div>
                </div>
            </div>
        </div>

        <div class="risk-dashboard">
            <h2>🚨 Risk Assessment Dashboard</h2>
            <div class="risk-grid">
                <div class="risk-card risk-critical">
                    <div style="font-size: 2em;">1</div>
                    <div>Critical Risk</div>
                    <div style="font-size: 0.9em; margin-top: 5px;">Windows Update</div>
                </div>
                <div class="risk-card risk-high">
                    <div style="font-size: 2em;">2</div>
                    <div>High Risk</div>
                    <div style="font-size: 0.9em; margin-top: 5px;">User Accounts, Firewall</div>
                </div>
                <div class="risk-card risk-medium">
                    <div style="font-size: 2em;">3</div>
                    <div>Medium Risk</div>
                    <div style="font-size: 0.9em; margin-top: 5px;">File System, Registry, Services</div>
                </div>
                <div class="risk-card risk-low">
                    <div style="font-size: 2em;">2</div>
                    <div>Low Risk</div>
                    <div style="font-size: 0.9em; margin-top: 5px;">Audit Logging</div>
                </div>
            </div>
        </div>

        <div class="findings-table">
            <h2>📊 Detailed Security Findings</h2>
            <table>
                <thead>
                    <tr>
                        <th>Control ID</th>
                        <th>Control Name</th>
                        <th>Risk Level</th>
                        <th>CVSS Score</th>
                        <th>Compliance Status</th>
                        <th>Compliance Score</th>
                        <th>Current Value</th>
                        <th>Baseline Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>WIN-1.1</strong></td>
                        <td>Windows Update and Patch Management</td>
                        <td><span class="risk-badge critical">Critical</span></td>
                        <td><span class="cvss-score cvss-critical">9.8</span></td>
                        <td><span class="status-badge status-non-compliant">Non-Compliant</span></td>
                        <td>0%</td>
                        <td>Service: Stopped, Recent updates: 3</td>
                        <td>Windows Update service running, regular updates installed</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-2.1</strong></td>
                        <td>User Account and Password Policies</td>
                        <td><span class="risk-badge high">High</span></td>
                        <td><span class="cvss-score cvss-high">8.2</span></td>
                        <td><span class="status-badge status-compliant">Compliant</span></td>
                        <td>100%</td>
                        <td>Password policy appears adequate</td>
                        <td>Strong password policy, disabled default accounts</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-3.1</strong></td>
                        <td>Windows Firewall Configuration</td>
                        <td><span class="risk-badge high">High</span></td>
                        <td><span class="cvss-score cvss-high">7.8</span></td>
                        <td><span class="status-badge status-non-compliant">Non-Compliant</span></td>
                        <td>75%</td>
                        <td>Enabled profiles: 2/3, Active rules: 50</td>
                        <td>All firewall profiles enabled with appropriate rules</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-4.1</strong></td>
                        <td>Audit Logging and Event Management</td>
                        <td><span class="risk-badge medium">Medium</span></td>
                        <td><span class="cvss-score cvss-medium">6.5</span></td>
                        <td><span class="status-badge status-non-compliant">Non-Compliant</span></td>
                        <td>75%</td>
                        <td>Event Log service: Running, Enabled logs: /2</td>
                        <td>Event Log service running, critical logs enabled with adequate retention</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-5.1</strong></td>
                        <td>Service Configurations and Security</td>
                        <td><span class="risk-badge medium">Medium</span></td>
                        <td><span class="cvss-score cvss-medium">6.8</span></td>
                        <td><span class="status-badge status-non-compliant">Non-Compliant</span></td>
                        <td>85%</td>
                        <td>Critical services running: 6/8, Total services: 109</td>
                        <td>All critical security services running, unnecessary services disabled</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-6.1</strong></td>
                        <td>Registry Security Configuration</td>
                        <td><span class="risk-badge medium">Medium</span></td>
                        <td><span class="cvss-score cvss-medium">6.2</span></td>
                        <td><span class="status-badge status-non-compliant">Non-Compliant</span></td>
                        <td>80%</td>
                        <td>Security registry keys found: 4/5</td>
                        <td>All critical security registry settings properly configured</td>
                    </tr>
                    <tr>
                        <td><strong>WIN-7.1</strong></td>
                        <td>File System Permissions on Critical Directories</td>
                        <td><span class="risk-badge medium">Medium</span></td>
                        <td><span class="cvss-score cvss-medium">5.8</span></td>
                        <td><span class="status-badge status-compliant">Compliant</span></td>
                        <td>100%</td>
                        <td>Critical directories found: 4/4</td>
                        <td>All critical directories exist with proper permissions</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="remediation">
            <h2>🔧 Remediation Recommendations</h2>

            <div class="remediation-item">
                <div class="remediation-header">
                    🚨 CRITICAL: WIN-1.1 - Windows Update and Patch Management
                </div>
                <div class="remediation-content">
                    <p><strong>Finding:</strong> Windows Update service or recent updates missing</p>
                    <p><strong>Risk:</strong> System vulnerable to known security exploits and malware</p>
                    <p><strong>Recommendation:</strong> Ensure Windows Update service is running and system receives regular security updates</p>
                    <p><strong>Action Steps:</strong></p>
                    <ul>
                        <li>Start Windows Update service: <code>net start wuauserv</code></li>
                        <li>Configure automatic updates in Group Policy</li>
                        <li>Install all pending security updates immediately</li>
                        <li>Establish regular patching schedule</li>
                    </ul>
                </div>
            </div>

            <div class="remediation-item">
                <div class="remediation-header">
                    🟠 HIGH: WIN-3.1 - Windows Firewall Configuration
                </div>
                <div class="remediation-content">
                    <p><strong>Finding:</strong> Windows Firewall configuration issues detected</p>
                    <p><strong>Risk:</strong> Network-based attacks and unauthorized access</p>
                    <p><strong>Recommendation:</strong> Ensure Windows Firewall service is running and all profiles are enabled with appropriate rules</p>
                    <p><strong>Action Steps:</strong></p>
                    <ul>
                        <li>Enable all firewall profiles (Domain, Private, Public)</li>
                        <li>Review and configure firewall rules</li>
                        <li>Block unnecessary inbound connections</li>
                        <li>Enable logging for security monitoring</li>
                    </ul>
                </div>
            </div>

            <div class="remediation-item">
                <div class="remediation-header">
                    🟡 MEDIUM: WIN-4.1 - Audit Logging and Event Management
                </div>
                <div class="remediation-content">
                    <p><strong>Finding:</strong> Event logging configuration needs improvement</p>
                    <p><strong>Risk:</strong> Limited forensic capabilities and security monitoring</p>
                    <p><strong>Recommendation:</strong> Ensure Event Log service is running and critical event logs are enabled with appropriate retention policies</p>
                    <p><strong>Action Steps:</strong></p>
                    <ul>
                        <li>Enable all critical event logs</li>
                        <li>Configure appropriate log retention policies</li>
                        <li>Set up log forwarding to central SIEM</li>
                        <li>Monitor log file sizes and rotation</li>
                    </ul>
                </div>
            </div>

            <div class="remediation-item">
                <div class="remediation-header">
                    🟡 MEDIUM: WIN-5.1 - Service Configurations and Security
                </div>
                <div class="remediation-content">
                    <p><strong>Finding:</strong> Some critical security services are not running</p>
                    <p><strong>Risk:</strong> Reduced security posture and protection capabilities</p>
                    <p><strong>Recommendation:</strong> Ensure critical security services are running and unnecessary services are disabled</p>
                    <p><strong>Action Steps:</strong></p>
                    <ul>
                        <li>Start all critical security services</li>
                        <li>Disable unnecessary services to reduce attack surface</li>
                        <li>Configure service startup types appropriately</li>
                        <li>Monitor service status regularly</li>
                    </ul>
                </div>
            </div>

            <div class="remediation-item">
                <div class="remediation-header">
                    🟡 MEDIUM: WIN-6.1 - Registry Security Configuration
                </div>
                <div class="remediation-content">
                    <p><strong>Finding:</strong> Most critical registry security settings are present</p>
                    <p><strong>Risk:</strong> Potential security configuration gaps</p>
                    <p><strong>Recommendation:</strong> Review and configure critical security registry settings according to security baselines</p>
                    <p><strong>Action Steps:</strong></p>
                    <ul>
                        <li>Review missing registry security settings</li>
                        <li>Apply security baseline configurations</li>
                        <li>Implement registry monitoring</li>
                        <li>Regular security configuration audits</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Report generated on: <strong>August 19, 2025</strong></p>
            <p>Windows Server 2019 Security Audit -  </p>
            <p>This report contains confidential security information and should be handled according to organizational security policies.</p>
        </div>
    </div>
</body>
</html>
