#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.0.0 (Simplified)
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Mailbox Security Audit Report Generator (Simplified Version)
================================================================================
Description: Processes JSON output from Exchange-Mailbox-Security-Audit.ps1 and 
generates comprehensive, human-readable audit reports in HTML format.

This is a simplified version that avoids PowerShell here-string parsing issues
while maintaining full functionality.
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$JsonFilePath = "",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "",

    [Parameter(Mandatory = $false)]
    [switch]$ExportCsv,

    [Parameter(Mandatory = $false)]
    [switch]$ConsoleOutput,

    [Parameter(Mandatory = $false)]
    [switch]$SkipBrowser
)

# Initialize report generation session
$ReportStartTime = Get-Date
$ReportID = [System.Guid]::NewGuid()

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generator (Simplified)" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Script Version: 1.0.0 (September 11, 2025)" -ForegroundColor White
Write-Host "Created By: E.Z. Consultancy" -ForegroundColor White
Write-Host "Report ID: $ReportID" -ForegroundColor Yellow
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# ================================================================================
# HELPER FUNCTIONS
# ================================================================================

function Find-LatestJsonFile {
    param([string]$SearchPath = ".")
    
    try {
        $JsonFiles = Get-ChildItem -Path $SearchPath -Filter "*.json" -ErrorAction SilentlyContinue |
            Where-Object { $_.Name -like "*Exchange*" -or $_.Name -like "*audit*" -or $_.Name -like "*Sample*" } |
            Sort-Object LastWriteTime -Descending
        
        if ($JsonFiles -and $JsonFiles.Count -gt 0) {
            return $JsonFiles[0].FullName
        }
        return $null
    }
    catch {
        Write-Host "[ERROR] Failed to search for JSON files: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Get-RiskLevelColor {
    param([string]$RiskLevel)
    
    switch ($RiskLevel.ToLower()) {
        "critical" { return "#dc3545" }
        "high" { return "#fd7e14" }
        "medium" { return "#ffc107" }
        "low" { return "#28a745" }
        default { return "#6c757d" }
    }
}

function Get-ComplianceStatusColor {
    param([string]$Status)
    
    switch ($Status.ToLower()) {
        "compliant" { return "#28a745" }
        "review required" { return "#fd7e14" }
        "non-compliant" { return "#dc3545" }
        "no data" { return "#6c757d" }
        default { return "#6c757d" }
    }
}

function ConvertTo-SafeHtml {
    param([string]$Text)
    
    if ([string]::IsNullOrWhiteSpace($Text)) {
        return "N/A"
    }
    
    return [System.Web.HttpUtility]::HtmlEncode($Text)
}

function Format-Number {
    param([object]$Number, [int]$DecimalPlaces = 0)
    
    if ($null -eq $Number -or $Number -eq "Unknown" -or $Number -eq "") {
        return "N/A"
    }
    
    try {
        $NumericValue = [double]$Number
        return $NumericValue.ToString("N$DecimalPlaces")
    }
    catch {
        return $Number.ToString()
    }
}

# ================================================================================
# INPUT VALIDATION AND FILE PROCESSING
# ================================================================================

Write-Host "Step 1/4: Validating input parameters and locating JSON file..." -ForegroundColor Yellow

# Determine JSON file path
if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
    Write-Host "  No JSON file specified, searching for latest audit results..." -ForegroundColor Gray
    $JsonFilePath = Find-LatestJsonFile
    
    if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
        Write-Host "[ERROR] No JSON audit files found in current directory." -ForegroundColor Red
        Write-Host "Please specify a JSON file path using -JsonFilePath parameter." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "  Found JSON file: $JsonFilePath" -ForegroundColor Green
}

# Validate JSON file exists
if (-not (Test-Path -Path $JsonFilePath)) {
    Write-Host "[ERROR] JSON file not found: $JsonFilePath" -ForegroundColor Red
    exit 1
}

Write-Host "  JSON file validated: $JsonFilePath" -ForegroundColor Green

# Determine output path
if ([string]::IsNullOrWhiteSpace($OutputPath)) {
    $JsonFileName = [System.IO.Path]::GetFileNameWithoutExtension($JsonFilePath)
    $OutputPath = ".\$JsonFileName-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
}

Write-Host "  Output path: $OutputPath" -ForegroundColor Green

# ================================================================================
# JSON DATA LOADING AND VALIDATION
# ================================================================================

Write-Host "Step 2/4: Loading and validating JSON audit data..." -ForegroundColor Yellow

try {
    $JsonContent = Get-Content -Path $JsonFilePath -Raw -Encoding UTF8
    $AuditData = $JsonContent | ConvertFrom-Json -ErrorAction Stop
    
    Write-Host "  JSON data loaded successfully" -ForegroundColor Green
    
    # Extract key sections
    $AuditMetadata = if ($AuditData.AuditMetadata) { $AuditData.AuditMetadata } else { @{} }
    $ComplianceSummary = if ($AuditData.ComplianceSummary) { $AuditData.ComplianceSummary } else { @{} }
    
    Write-Host "  Audit ID: $($AuditMetadata.AuditID)" -ForegroundColor Gray
    Write-Host "  Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor Gray
    
}
catch {
    Write-Host "[ERROR] Failed to load or parse JSON file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# DATA PROCESSING AND ANALYSIS
# ================================================================================

Write-Host "Step 3/4: Processing audit data and generating analysis..." -ForegroundColor Yellow

# Process control data
$ControlSummaryData = @()

# Define control mappings
$ControlMappings = @{
    "MBX_1_1_ImpersonationRights" = @{ ID = "MBX-1.1"; Name = "Mailbox Impersonation Rights"; Category = "Access Control" }
    "MBX_2_1_FullAccessPermissions" = @{ ID = "MBX-2.1"; Name = "Full Access Permissions"; Category = "Delegation Management" }
    "MBX_3_1_AuditLogging" = @{ ID = "MBX-3.1"; Name = "Audit Logging Configuration"; Category = "Compliance & Monitoring" }
    "MBX_4_1_SendAsPermissions" = @{ ID = "MBX-4.1"; Name = "Send-As Permissions"; Category = "Email Delegation" }
    "MBX_5_1_SendOnBehalfPermissions" = @{ ID = "MBX-5.1"; Name = "Send-On-Behalf Permissions"; Category = "Email Delegation" }
}

foreach ($ControlKey in $ControlMappings.Keys) {
    $ControlData = $AuditData.$ControlKey
    $Mapping = $ControlMappings[$ControlKey]
    
    if ($ControlData) {
        $ControlSummaryData += @{
            ControlID = if ($ControlData.ControlID) { $ControlData.ControlID } else { $Mapping.ID }
            ControlName = if ($ControlData.ControlName) { $ControlData.ControlName } else { $Mapping.Name }
            Category = $Mapping.Category
            RiskLevel = if ($ControlData.RiskLevel) { $ControlData.RiskLevel } else { "Unknown" }
            ComplianceStatus = if ($ControlData.ComplianceStatus) { $ControlData.ComplianceStatus } else { "Unknown" }
            ComplianceScore = if ($ControlData.ComplianceScore) { $ControlData.ComplianceScore } else { 0 }
            CVSSScore = if ($ControlData.CVSSScore) { $ControlData.CVSSScore } else { 0 }
            Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "No findings available" }
            Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "No recommendations available" }
            HasError = if ($ControlData.Error) { $true } else { $false }
            ErrorMessage = if ($ControlData.Error) { $ControlData.Error } else { "" }
        }
    }
}

# Calculate dashboard metrics
$TotalControls = $ControlSummaryData.Count
$CompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
$ReviewRequiredControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Review Required" }).Count
$NonCompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
$ErrorControls = ($ControlSummaryData | Where-Object { $_.HasError }).Count

$OverallComplianceScore = if ($TotalControls -gt 0) { 
    [math]::Round(($ControlSummaryData | Measure-Object -Property ComplianceScore -Average).Average, 1) 
} else { 0 }

# Risk level distribution
$CriticalRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Critical" }).Count
$HighRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "High" }).Count
$MediumRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" }).Count
$LowRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Low" }).Count

Write-Host "  Dashboard metrics calculated:" -ForegroundColor Green
Write-Host "    - Total Controls: $TotalControls" -ForegroundColor Gray
Write-Host "    - Compliant: $CompliantControls" -ForegroundColor Gray
Write-Host "    - Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor Gray

# ================================================================================
# HTML REPORT GENERATION
# ================================================================================

Write-Host "Step 4/4: Generating HTML report..." -ForegroundColor Yellow

# Generate enhanced CSS styles
$CssStyles = @'
<style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px; }
    .container { max-width: 1400px; margin: 0 auto; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
    .header h1 { font-size: 2.5em; margin-bottom: 10px; font-weight: 300; }
    .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
    .dashboard-card { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
    .dashboard-card h3 { color: #667eea; margin-bottom: 15px; }
    .dashboard-value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
    .section { background: white; margin-bottom: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
    .section-header { background: #f8f9fa; padding: 20px 30px; border-bottom: 1px solid #dee2e6; border-radius: 10px 10px 0 0; }
    .section-header h2 { color: #495057; font-size: 1.5em; margin-bottom: 5px; }
    .section-content { padding: 30px; }

    /* Enhanced table styling with better column widths */
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; table-layout: fixed; }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #dee2e6; vertical-align: top; }
    th { background-color: #f8f9fa; font-weight: 600; color: #495057; }
    tr:hover { background-color: #f8f9fa; }

    /* Specific column widths for main control table */
    .control-table th:nth-child(1) { width: 18%; } /* Control Category/ID */
    .control-table th:nth-child(2) { width: 25%; } /* Control Name */
    .control-table th:nth-child(3) { width: 12%; } /* Risk Level */
    .control-table th:nth-child(4) { width: 18%; } /* Compliance Status - increased width */
    .control-table th:nth-child(5) { width: 10%; } /* Score */
    .control-table th:nth-child(6) { width: 17%; } /* Finding */

    /* Control ID styling - category primary, ID secondary */
    .control-category { font-size: 1.1em; font-weight: 600; color: #495057; line-height: 1.3; }
    .control-id { font-size: 0.85em; color: #6c757d; margin-top: 3px; }

    /* Enhanced status badge styling */
    .status-badge { display: inline-block; padding: 6px 14px; border-radius: 20px; font-size: 0.85em; font-weight: 600; color: white; white-space: nowrap; }
    .status-icon { margin-right: 6px; font-weight: bold; }

    /* Permission analysis sections */
    .permission-section { margin-bottom: 25px; }
    .permission-header { background: #f8f9fa; padding: 15px 20px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #667eea; }
    .permission-header h3 { margin: 0; color: #495057; font-size: 1.2em; }
    .permission-count { color: #6c757d; font-size: 0.9em; margin-left: 10px; }
    .permission-list { list-style: none; padding: 0; margin: 0; }
    .permission-item { padding: 12px 20px; margin-bottom: 8px; background: #fff; border: 1px solid #e9ecef; border-radius: 6px; border-left: 4px solid #28a745; }
    .permission-item.risk-high { border-left-color: #dc3545; }
    .permission-item.risk-medium { border-left-color: #ffc107; }
    .permission-item.risk-critical { border-left-color: #6f42c1; }
    .permission-text { font-size: 1em; color: #495057; line-height: 1.4; }
    .permission-details { font-size: 0.85em; color: #6c757d; margin-top: 5px; }
    .risk-indicator { float: right; padding: 3px 8px; border-radius: 12px; font-size: 0.75em; font-weight: 600; color: white; }
    .risk-indicator.critical { background-color: #6f42c1; }
    .risk-indicator.high { background-color: #dc3545; }
    .risk-indicator.medium { background-color: #ffc107; color: #212529; }
    .risk-indicator.low { background-color: #28a745; }

    /* Collapsible sections */
    .collapsible { cursor: pointer; padding: 15px 20px; background: #e9ecef; border: none; text-align: left; outline: none; font-size: 1.1em; font-weight: 600; width: 100%; border-radius: 8px; margin-bottom: 10px; transition: background-color 0.3s; }
    .collapsible:hover { background: #dee2e6; }
    .collapsible:after { content: '\002B'; color: #667eea; font-weight: bold; float: right; margin-left: 5px; }
    .collapsible.active:after { content: "\2212"; }
    .collapsible-content { padding: 0; max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background: white; border-radius: 0 0 8px 8px; }
    .collapsible-content.active { max-height: 2000px; padding: 20px; border: 1px solid #dee2e6; border-top: none; }

    /* Alert styling */
    .alert { padding: 15px 20px; margin-bottom: 20px; border-radius: 5px; border-left: 4px solid; }
    .alert-info { background-color: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
    .alert-warning { background-color: #fff3cd; border-color: #ffc107; color: #856404; }
    .alert-danger { background-color: #f8d7da; border-color: #dc3545; color: #721c24; }
    .footer { text-align: center; padding: 30px; color: #6c757d; font-size: 0.9em; border-top: 1px solid #dee2e6; margin-top: 40px; }

    /* Responsive design */
    @media (max-width: 768px) {
        .container { padding: 10px; }
        .dashboard { grid-template-columns: 1fr; }
        table { font-size: 0.9em; }
        .control-table th:nth-child(6) { display: none; }
        .control-table td:nth-child(6) { display: none; }
    }
</style>
'@

# Generate HTML content
$OrganizationName = if ($AuditMetadata.OrganizationName) { $AuditMetadata.OrganizationName } else { "Unknown Organization" }
$AuditDate = if ($AuditMetadata.AuditStartTime) { $AuditMetadata.AuditStartTime } else { "Unknown Date" }
$AuditID = if ($AuditMetadata.AuditID) { $AuditMetadata.AuditID } else { "Unknown" }

# Build control summary table
$ControlTableRows = ""
foreach ($Control in $ControlSummaryData) {
    $RiskColor = Get-RiskLevelColor -RiskLevel $Control.RiskLevel
    $ComplianceColor = Get-ComplianceStatusColor -Status $Control.ComplianceStatus
    $ComplianceScore = Format-Number -Number $Control.ComplianceScore -DecimalPlaces 0
    $CVSSScore = Format-Number -Number $Control.CVSSScore -DecimalPlaces 1

    $StatusIcon = switch ($Control.ComplianceStatus.ToLower()) {
        "compliant" { "✅" }
        "review required" { "⚠️" }
        "non-compliant" { "❌" }
        default { "❓" }
    }

    $ErrorInfo = ""
    if ($Control.HasError) {
        $ErrorMsg = ConvertTo-SafeHtml -Text $Control.ErrorMessage
        $ErrorInfo = "`n<br><small style='color: #dc3545;'>ERROR: $ErrorMsg</small>"
    }

    $SafeControlID = ConvertTo-SafeHtml -Text $Control.ControlID
    $SafeCategory = ConvertTo-SafeHtml -Text $Control.Category
    $SafeControlName = ConvertTo-SafeHtml -Text $Control.ControlName
    $SafeFinding = ConvertTo-SafeHtml -Text $Control.Finding

    # Build table row using string concatenation
    $row = "<tr>"
    $row += "<td><div class=`"control-category`">$SafeCategory</div><div class=`"control-id`">$SafeControlID</div></td>"
    $row += "<td>$SafeControlName$ErrorInfo</td>"
    $row += "<td><span class=`"status-badge`" style=`"background-color: $RiskColor;`">$($Control.RiskLevel)</span></td>"
    $row += "<td><span class=`"status-icon`">$StatusIcon</span><span class=`"status-badge`" style=`"background-color: $ComplianceColor;`">$($Control.ComplianceStatus)</span></td>"
    $row += "<td style=`"text-align: center;`"><strong>$ComplianceScore%</strong></td>"
    $row += "<td>$SafeFinding</td>"
    $row += "</tr>"
    $ControlTableRows += $row
}

# Generate comprehensive permission analysis sections
$PermissionAnalysis = ""

# Function to generate human-readable permission descriptions
function Generate-PermissionSection {
    param(
        [string]$SectionTitle,
        [string]$SectionId,
        [array]$Permissions,
        [string]$PermissionType,
        [int]$TotalCount
    )

    if (-not $Permissions -or $Permissions.Count -eq 0) {
        return ""
    }

    $permissionItems = ""
    foreach ($permission in $Permissions) {
        $riskLevel = "low"
        $riskText = "Low"

        # Determine risk level based on permission type and properties
        if ($PermissionType -eq "Impersonation") {
            $riskLevel = "critical"
            $riskText = "Critical"
        } elseif ($PermissionType -eq "FullAccess" -and $permission.IsInherited -eq $false) {
            $riskLevel = "high"
            $riskText = "High"
        } elseif ($PermissionType -eq "SendAs") {
            $riskLevel = "high"
            $riskText = "High"
        } elseif ($PermissionType -eq "SendOnBehalf") {
            $riskLevel = "medium"
            $riskText = "Medium"
        }

        # Generate human-readable description
        $description = ""
        $details = ""

        switch ($PermissionType) {
            "FullAccess" {
                $userDisplayName = if ($permission.UserDisplayName) { $permission.UserDisplayName } else { $permission.User -replace ".*\\", "" }
                $description = "$userDisplayName has full access to $($permission.MailboxDisplayName)"
                $details = "User: $($permission.User) | Mailbox: $($permission.MailboxPrimarySmtpAddress)"
                if ($permission.UserDepartment -and $permission.Department -and $permission.UserDepartment -ne $permission.Department) {
                    $details += " | Cross-Department: $($permission.UserDepartment) → $($permission.Department)"
                }
            }
            "SendAs" {
                $userDisplayName = if ($permission.UserDisplayName) { $permission.UserDisplayName } else { $permission.User -replace ".*\\", "" }
                $description = "$userDisplayName can send emails as $($permission.MailboxDisplayName)"
                $details = "User: $($permission.User) | Mailbox: $($permission.MailboxPrimarySmtpAddress)"
                if ($permission.BusinessJustification) {
                    $details += " | Justification: $($permission.BusinessJustification)"
                }
            }
            "SendOnBehalf" {
                $userDisplayName = if ($permission.UserDisplayName) { $permission.UserDisplayName } else { $permission.User -replace ".*\\", "" }
                $description = "$userDisplayName can send emails on behalf of $($permission.MailboxDisplayName)"
                $details = "User: $($permission.User) | Mailbox: $($permission.MailboxPrimarySmtpAddress)"
                if ($permission.BusinessJustification) {
                    $details += " | Justification: $($permission.BusinessJustification)"
                }
            }
            "Impersonation" {
                $userDisplayName = if ($permission.User) { $permission.User -replace ".*\\", "" } else { "Unknown User" }
                $description = "$userDisplayName has impersonation rights on $($permission.MailboxDisplayName)"
                $details = "User: $($permission.User) | Mailbox: $($permission.MailboxPrimarySmtpAddress)"
                if ($permission.BusinessJustification) {
                    $details += " | Justification: $($permission.BusinessJustification)"
                }
            }
        }

        $safeDescription = ConvertTo-SafeHtml -Text $description
        $safeDetails = ConvertTo-SafeHtml -Text $details

        # Build permission item using string concatenation
        $item = "<li class=`"permission-item risk-$riskLevel`">"
        $item += "<div class=`"permission-text`">$safeDescription</div>"
        $item += "<div class=`"permission-details`">$safeDetails</div>"
        $item += "<div class=`"risk-indicator $riskLevel`">$riskText Risk</div>"
        $item += "</li>"
        $permissionItems += $item
    }

    # Build section using string concatenation
    $result = "<div class=`"permission-section`">"
    $result += "<button class=`"collapsible`">$SectionTitle <span class=`"permission-count`">($TotalCount total)</span></button>"
    $result += "<div class=`"collapsible-content`">"
    $result += "<ul class=`"permission-list`">$permissionItems</ul>"
    $result += "</div>"
    $result += "</div>"
    return $result
}

# Generate sections for each permission type
$permissionSections = ""

# Full Access Permissions
if ($AuditData.MBX_2_1_FullAccessPermissions -and $AuditData.MBX_2_1_FullAccessPermissions.FullAccessPermissions) {
    $fullAccessData = $AuditData.MBX_2_1_FullAccessPermissions
    $permissionSections += Generate-PermissionSection -SectionTitle "Full Access Permissions" -SectionId "fullaccess" -Permissions $fullAccessData.FullAccessPermissions -PermissionType "FullAccess" -TotalCount $fullAccessData.TotalFullAccessPermissions
}

# Send-As Permissions
if ($AuditData.MBX_4_1_SendAsPermissions -and $AuditData.MBX_4_1_SendAsPermissions.SendAsPermissions) {
    $sendAsData = $AuditData.MBX_4_1_SendAsPermissions
    $permissionSections += Generate-PermissionSection -SectionTitle "Send-As Permissions" -SectionId "sendas" -Permissions $sendAsData.SendAsPermissions -PermissionType "SendAs" -TotalCount $sendAsData.TotalSendAsPermissions
}

# Send-On-Behalf Permissions
if ($AuditData.MBX_5_1_SendOnBehalfPermissions -and $AuditData.MBX_5_1_SendOnBehalfPermissions.SendOnBehalfPermissions) {
    $sendOnBehalfData = $AuditData.MBX_5_1_SendOnBehalfPermissions
    $permissionSections += Generate-PermissionSection -SectionTitle "Send-On-Behalf Permissions" -SectionId "sendonbehalf" -Permissions $sendOnBehalfData.SendOnBehalfPermissions -PermissionType "SendOnBehalf" -TotalCount $sendOnBehalfData.TotalSendOnBehalfPermissions
}

# Impersonation Rights
if ($AuditData.MBX_1_1_ImpersonationRights -and $AuditData.MBX_1_1_ImpersonationRights.ImpersonationRights) {
    $impersonationData = $AuditData.MBX_1_1_ImpersonationRights
    $permissionSections += Generate-PermissionSection -SectionTitle "Mailbox Impersonation Rights" -SectionId "impersonation" -Permissions $impersonationData.ImpersonationRights -PermissionType "Impersonation" -TotalCount $impersonationData.TotalImpersonationRights
}

if ($permissionSections -ne "") {
    # Build permission analysis using string concatenation
    $PermissionAnalysis = "<div class=`"section`">"
    $PermissionAnalysis += "<div class=`"section-header`">"
    $PermissionAnalysis += "<h2>Detailed Permission Analysis</h2>"
    $PermissionAnalysis += "<div style=`"color: #6c757d; font-size: 0.95em;`">Human-readable breakdown of all mailbox permissions and delegations</div>"
    $PermissionAnalysis += "</div>"
    $PermissionAnalysis += "<div class=`"section-content`">"
    $PermissionAnalysis += "<div class=`"alert alert-info`">"
    $PermissionAnalysis += "<strong>Permission Overview:</strong> Click on each section below to expand and view detailed permission relationships. "
    $PermissionAnalysis += "Risk levels are indicated by color coding and help prioritize review activities."
    $PermissionAnalysis += "</div>"
    $PermissionAnalysis += $permissionSections
    $PermissionAnalysis += "</div>"
    $PermissionAnalysis += "</div>"
}

# Generate recommendations
$Recommendations = ""
$HighPriorityControls = $ControlSummaryData | Where-Object { $_.RiskLevel -in @("Critical", "High") -and $_.ComplianceStatus -ne "Compliant" }
$MediumPriorityControls = $ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" -and $_.ComplianceStatus -ne "Compliant" }

if ($HighPriorityControls.Count -gt 0) {
    $HighPriorityList = ""
    foreach ($Control in $HighPriorityControls) {
        $SafeRecommendation = ConvertTo-SafeHtml -Text $Control.Recommendation
        $HighPriorityList += "<li><strong>$($Control.ControlID) - $($Control.ControlName):</strong> $SafeRecommendation</li>"
    }
    $section = "<div class=`"alert alert-danger`">"
    $section += "<h4>HIGH PRIORITY: Immediate Attention Required</h4>"
    $section += "<ul>$HighPriorityList</ul>"
    $section += "</div>"
    $Recommendations += $section
}

if ($MediumPriorityControls.Count -gt 0) {
    $MediumPriorityList = ""
    foreach ($Control in $MediumPriorityControls) {
        $SafeRecommendation = ConvertTo-SafeHtml -Text $Control.Recommendation
        $MediumPriorityList += "<li><strong>$($Control.ControlID) - $($Control.ControlName):</strong> $SafeRecommendation</li>"
    }
    $section = "<div class=`"alert alert-warning`">"
    $section += "<h4>MEDIUM PRIORITY: Review Within 30 Days</h4>"
    $section += "<ul>$MediumPriorityList</ul>"
    $section += "</div>"
    $Recommendations += $section
}

if ($Recommendations -eq "") {
    $Recommendations = "<div class=`"alert alert-info`">"
    $Recommendations += "<h4>EXCELLENT: Security Posture</h4>"
    $Recommendations += "<p>All assessed controls are compliant. Continue monitoring and maintain current security practices.</p>"
    $Recommendations += "</div>"
}

# Generate complete HTML report using StringBuilder to avoid parsing issues
$CompliancePercentage = if ($TotalControls -gt 0) { [math]::Round(($CompliantControls / $TotalControls) * 100, 1) } else { 0 }
$ReportEndTime = Get-Date
$ReportDuration = $ReportEndTime - $ReportStartTime

# Format values to avoid parsing issues
$FormattedComplianceScore = [math]::Round($OverallComplianceScore, 1).ToString() + "%"
$FormattedCompliancePercentage = $CompliancePercentage.ToString() + "%"

# Determine colors for dashboard
$ComplianceColor = if ($OverallComplianceScore -ge 80) { '#28a745' } elseif ($OverallComplianceScore -ge 60) { '#ffc107' } else { '#dc3545' }
$RiskColor = if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) { '#dc3545' } elseif ($MediumRiskControls -gt 0) { '#ffc107' } else { '#28a745' }
$RiskLevel = if ($CriticalRiskControls -gt 0) { 'CRITICAL' } elseif ($HighRiskControls -gt 0) { 'HIGH' } elseif ($MediumRiskControls -gt 0) { 'MEDIUM' } else { 'LOW' }

# Use StringBuilder for HTML generation
$Html = New-Object System.Text.StringBuilder

$null = $Html.AppendLine('<!DOCTYPE html>')
$null = $Html.AppendLine('<html lang="en">')
$null = $Html.AppendLine('<head>')
$null = $Html.AppendLine('<meta charset="UTF-8">')
$null = $Html.AppendLine('<meta name="viewport" content="width=device-width, initial-scale=1.0">')
$null = $Html.AppendLine("<title>Exchange Mailbox Security Audit Report - $OrganizationName</title>")
$null = $Html.AppendLine($CssStyles)
$null = $Html.AppendLine('</head>')
$null = $Html.AppendLine('<body>')
$null = $Html.AppendLine('<div class="container">')

# Header section
$null = $Html.AppendLine('<div class="header">')
$null = $Html.AppendLine('<h1>Exchange Mailbox Security Audit Report</h1>')
$null = $Html.AppendLine('<div style="font-size: 1.2em; opacity: 0.9; margin-bottom: 20px;">Comprehensive Security Assessment and Compliance Analysis</div>')
$null = $Html.AppendLine('<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">')
$null = $Html.AppendLine('<div style="background: rgba(255, 255, 255, 0.1); padding: 10px 15px; border-radius: 5px;">')
$null = $Html.AppendLine('<div style="font-size: 0.9em; opacity: 0.8;">Organization</div>')
$null = $Html.AppendLine("<div style='font-size: 1.1em; font-weight: 500;'>$OrganizationName</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("<div style=`"background: rgba(255, 255, 255, 0.1); padding: 10px 15px; border-radius: 5px;`">")
$null = $Html.AppendLine("<div style=`"font-size: 0.9em; opacity: 0.8;`">Audit Date</div>")
$null = $Html.AppendLine("<div style=`"font-size: 1.1em; font-weight: 500;`">$AuditDate</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("<div style=`"background: rgba(255, 255, 255, 0.1); padding: 10px 15px; border-radius: 5px;`">")
$null = $Html.AppendLine("<div style=`"font-size: 0.9em; opacity: 0.8;`">Audit ID</div>")
$null = $Html.AppendLine("<div style=`"font-size: 1.1em; font-weight: 500;`">$AuditID</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")

# Dashboard section
$null = $Html.AppendLine("<div class=`"dashboard`">")
$null = $Html.AppendLine('<div class="dashboard-card">')
$null = $Html.AppendLine('<h3>Overall Compliance Score</h3>')
$null = $Html.AppendLine("<div class='dashboard-value' style='color: $ComplianceColor'>$FormattedComplianceScore</div>")
$null = $Html.AppendLine('<div style="color: #666; font-size: 0.9em;">Average compliance across all controls</div>')
$null = $Html.AppendLine('</div>')
# Controls Assessment card
$null = $Html.AppendLine("<div class=`"dashboard-card`">")
$null = $Html.AppendLine("<h3>Controls Assessment</h3>")
$null = $Html.AppendLine("<div class=`"dashboard-value`" style=`"color: #667eea`">$TotalControls</div>")
$null = $Html.AppendLine("<div style=`"color: #666; font-size: 0.9em;`">")
$null = $Html.AppendLine("<span style=`"color: #28a745`">COMPLIANT: $CompliantControls</span><br>")
$null = $Html.AppendLine("<span style=`"color: #fd7e14`">REVIEW: $ReviewRequiredControls</span><br>")
$null = $Html.AppendLine("<span style=`"color: #dc3545`">NON-COMPLIANT: $NonCompliantControls</span>")
if ($ErrorControls -gt 0) {
    $null = $Html.AppendLine("<br><span style=`"color: #6c757d`">ERRORS: $ErrorControls</span>")
}
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")

# Risk Distribution card
$null = $Html.AppendLine("<div class=`"dashboard-card`">")
$null = $Html.AppendLine("<h3>Risk Distribution</h3>")
$null = $Html.AppendLine("<div class=`"dashboard-value`" style=`"color: $RiskColor`">$RiskLevel</div>")
$null = $Html.AppendLine("<div style=`"color: #666; font-size: 0.9em;`">")
$null = $Html.AppendLine("<span style=`"color: #dc3545`">CRITICAL: $CriticalRiskControls</span><br>")
$null = $Html.AppendLine("<span style=`"color: #fd7e14`">HIGH: $HighRiskControls</span><br>")
$null = $Html.AppendLine("<span style=`"color: #ffc107`">MEDIUM: $MediumRiskControls</span><br>")
$null = $Html.AppendLine("<span style=`"color: #28a745`">LOW: $LowRiskControls</span>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")

# Compliance Rate card
$ComplianceRateColor = if ($CompliancePercentage -ge 80) { '#28a745' } elseif ($CompliancePercentage -ge 60) { '#ffc107' } else { '#dc3545' }
$null = $Html.AppendLine("<div class=`"dashboard-card`">")
$null = $Html.AppendLine("<h3>Compliance Rate</h3>")
$null = $Html.AppendLine("<div class=`"dashboard-value`" style=`"color: $ComplianceRateColor`">$FormattedCompliancePercentage</div>")
$null = $Html.AppendLine("<div style=`"color: #666; font-size: 0.9em;`">Controls meeting compliance requirements</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")
# Alert section based on risk levels
if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) {
    $null = $Html.AppendLine("<div class=`"alert alert-danger`"><strong>IMMEDIATE ACTION REQUIRED:</strong> Critical or high-risk security controls have been identified that require immediate attention to maintain security posture.</div>")
} elseif ($ReviewRequiredControls -gt 0) {
    $null = $Html.AppendLine("<div class=`"alert alert-warning`"><strong>REVIEW RECOMMENDED:</strong> Some controls require review to ensure optimal security configuration and compliance.</div>")
} else {
    $null = $Html.AppendLine("<div class=`"alert alert-info`"><strong>GOOD SECURITY POSTURE:</strong> All assessed controls are within acceptable compliance thresholds.</div>")
}

# Security Control Analysis section
$null = $Html.AppendLine("<div class=`"section`">")
$null = $Html.AppendLine("<div class=`"section-header`">")
$null = $Html.AppendLine("<h2>Security Control Analysis</h2>")
$null = $Html.AppendLine("<div style=`"color: #6c757d; font-size: 0.95em;`">Detailed assessment of each mailbox security control</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("<div class=`"section-content`">")
$null = $Html.AppendLine("<table class=`"control-table`">")
$null = $Html.AppendLine("<thead>")
$null = $Html.AppendLine("<tr>")
$null = $Html.AppendLine("<th>Control Category</th>")
$null = $Html.AppendLine("<th>Control Name</th>")
$null = $Html.AppendLine("<th>Risk Level</th>")
$null = $Html.AppendLine("<th>Compliance Status</th>")
$null = $Html.AppendLine("<th>Score</th>")
$null = $Html.AppendLine("<th>Finding</th>")
$null = $Html.AppendLine("</tr>")
$null = $Html.AppendLine("</thead>")
$null = $Html.AppendLine("<tbody>")
$null = $Html.AppendLine($ControlTableRows)
$null = $Html.AppendLine("</tbody>")
$null = $Html.AppendLine("</table>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")
# Add permission analysis section
if ($PermissionAnalysis -ne "") {
    $null = $Html.AppendLine($PermissionAnalysis)
}

# Actionable Recommendations section
$null = $Html.AppendLine("<div class=`"section`">")
$null = $Html.AppendLine("<div class=`"section-header`">")
$null = $Html.AppendLine("<h2>Actionable Recommendations</h2>")
$null = $Html.AppendLine("<div style=`"color: #6c757d; font-size: 0.95em;`">Prioritized remediation actions based on risk assessment</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("<div class=`"section-content`">")
$null = $Html.AppendLine($Recommendations)
$null = $Html.AppendLine("<div style=`"background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;`">")
$null = $Html.AppendLine("<h4>General Security Best Practices</h4>")
$null = $Html.AppendLine("<ul>")
$null = $Html.AppendLine("<li>Implement regular access reviews for all mailbox permissions (quarterly recommended)</li>")
$null = $Html.AppendLine("<li>Enable comprehensive audit logging for all Exchange activities</li>")
$null = $Html.AppendLine("<li>Use principle of least privilege for all mailbox delegations</li>")
$null = $Html.AppendLine("<li>Document business justification for all cross-domain permissions</li>")
$null = $Html.AppendLine("<li>Implement automated monitoring for new permission assignments</li>")
$null = $Html.AppendLine("</ul>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")
# Footer section
$null = $Html.AppendLine("<div class=`"footer`">")
$null = $Html.AppendLine("<p><strong>Exchange Mailbox Security Audit Report</strong></p>")
$null = $Html.AppendLine("<p>Generated by E.Z. Consultancy Exchange Audit Report Generator v1.0.0</p>")
$null = $Html.AppendLine("<p>Report Generated: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss')) | Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds</p>")
$null = $Html.AppendLine("<p>Report ID: $ReportID | Source Audit ID: $AuditID</p>")
$null = $Html.AppendLine("<p style=`"margin-top: 15px; font-size: 0.8em; color: #999;`">")
$null = $Html.AppendLine("This report contains sensitive security information. Handle according to your organization's data classification policies.")
$null = $Html.AppendLine("</p>")
$null = $Html.AppendLine("</div>")
$null = $Html.AppendLine("</div>")

# JavaScript for collapsible functionality
$null = $Html.AppendLine("<script>")
$null = $Html.AppendLine("// Collapsible sections functionality")
$null = $Html.AppendLine("document.addEventListener(`"DOMContentLoaded`", function() {")
$null = $Html.AppendLine("    var collapsibles = document.getElementsByClassName(`"collapsible`");")
$null = $Html.AppendLine("    ")
$null = $Html.AppendLine("    for (var i = 0; i < collapsibles.length; i++) {")
$null = $Html.AppendLine("        collapsibles[i].addEventListener(`"click`", function() {")
$null = $Html.AppendLine("            this.classList.toggle(`"active`");")
$null = $Html.AppendLine("            var content = this.nextElementSibling;")
$null = $Html.AppendLine("            ")
$null = $Html.AppendLine("            if (content.classList.contains(`"active`")) {")
$null = $Html.AppendLine("                content.classList.remove(`"active`");")
$null = $Html.AppendLine("                content.style.maxHeight = null;")
$null = $Html.AppendLine("            } else {")
$null = $Html.AppendLine("                content.classList.add(`"active`");")
$null = $Html.AppendLine("                content.style.maxHeight = content.scrollHeight + `"px`";")
$null = $Html.AppendLine("            }")
$null = $Html.AppendLine("        });")
$null = $Html.AppendLine("    }")
$null = $Html.AppendLine("    ")
$null = $Html.AppendLine("    // Auto-expand first section if it exists")
$null = $Html.AppendLine("    if (collapsibles.length > 0) {")
$null = $Html.AppendLine("        collapsibles[0].click();")
$null = $Html.AppendLine("    }")
$null = $Html.AppendLine("});")
$null = $Html.AppendLine("</script>")
$null = $Html.AppendLine("</body>")
$null = $Html.AppendLine("</html>")

# Get the complete HTML content
$HtmlContent = $Html.ToString()

# Save HTML report
try {
    $HtmlContent | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "  HTML report generated successfully: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to generate HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# CSV EXPORT PROCESSING
# ================================================================================

if ($ExportCsv) {
    Write-Host "Exporting CSV files for detailed analysis..." -ForegroundColor Yellow

    try {
        $CsvDirectory = [System.IO.Path]::GetDirectoryName($OutputPath)
        $CsvFolderName = [System.IO.Path]::GetFileNameWithoutExtension($OutputPath) + "-CSV-Exports"
        $CsvExportPath = Join-Path $CsvDirectory $CsvFolderName

        if (-not (Test-Path -Path $CsvExportPath)) {
            New-Item -Path $CsvExportPath -ItemType Directory -Force | Out-Null
        }

        # Export control summary
        $CsvPath = Join-Path $CsvExportPath "Control-Summary-Export.csv"
        $ControlSummaryData | Select-Object ControlID, ControlName, Category, RiskLevel, ComplianceStatus, ComplianceScore, CVSSScore, Finding, Recommendation, HasError, ErrorMessage |
            Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8
        Write-Host "  Control summary exported: $CsvPath" -ForegroundColor Green

        # Export Full Access permissions if available
        if ($AuditData.MBX_2_1_FullAccessPermissions -and $AuditData.MBX_2_1_FullAccessPermissions.FullAccessPermissions) {
            $CsvPath = Join-Path $CsvExportPath "Full-Access-Permissions-Export.csv"
            $AuditData.MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8
            Write-Host "  Full Access permissions exported: $CsvPath" -ForegroundColor Green
        }

        Write-Host "  CSV exports completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to export CSV files: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# ================================================================================
# COMPLETION AND SUMMARY
# ================================================================================

$ReportEndTime = Get-Date
$ReportDuration = $ReportEndTime - $ReportStartTime

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generation Completed" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report ID: $ReportID" -ForegroundColor Cyan
Write-Host "Source Audit ID: $AuditID" -ForegroundColor Cyan
Write-Host "Generation Time: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds" -ForegroundColor Cyan
Write-Host "HTML Report: $OutputPath" -ForegroundColor Cyan
if ($ExportCsv) {
    Write-Host "CSV Exports: $CsvExportPath" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "[SUCCESS] Report generation completed successfully" -ForegroundColor Green

# Console output if requested
if ($ConsoleOutput) {
    Write-Host ""
    Write-Host "=== CONSOLE SUMMARY ===" -ForegroundColor Yellow
    Write-Host "Organization: $OrganizationName" -ForegroundColor White
    Write-Host "Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor $(if ($OverallComplianceScore -ge 80) { "Green" } elseif ($OverallComplianceScore -ge 60) { "Yellow" } else { "Red" })
    Write-Host "Total Controls: $TotalControls | Compliant: $CompliantControls | Review Required: $ReviewRequiredControls" -ForegroundColor White
    Write-Host "Risk Distribution: Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls" -ForegroundColor White
    Write-Host "========================" -ForegroundColor Yellow
}

# Priority recommendations summary
$HighPriorityCount = ($ControlSummaryData | Where-Object { $_.RiskLevel -in @("Critical", "High") -and $_.ComplianceStatus -ne "Compliant" }).Count
$MediumPriorityCount = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" -and $_.ComplianceStatus -ne "Compliant" }).Count

if ($HighPriorityCount -gt 0) {
    Write-Host ""
    Write-Host "⚠️  IMMEDIATE ATTENTION REQUIRED: $HighPriorityCount high-priority security issues identified" -ForegroundColor Red
} elseif ($MediumPriorityCount -gt 0) {
    Write-Host ""
    Write-Host "📋 REVIEW RECOMMENDED: $MediumPriorityCount medium-priority items require attention" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "✅ EXCELLENT SECURITY POSTURE: All controls are within acceptable compliance thresholds" -ForegroundColor Green
}

Write-Host "=================================================================================" -ForegroundColor Green

# Launch browser if not skipped
if (-not $SkipBrowser) {
    try {
        Write-Host ""
        Write-Host "Opening report in default web browser..." -ForegroundColor Yellow
        Start-Process $OutputPath
    }
    catch {
        Write-Host "[WARNING] Could not open browser automatically: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Please manually open: $OutputPath" -ForegroundColor Yellow
    }
}

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.0.0 (Simplified)
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================
