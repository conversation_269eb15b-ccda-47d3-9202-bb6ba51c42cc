﻿{
    "WIN_7_1_FileSystemSecurity":  {
                                       "Recommendation":  "Verify critical system directories exist with appropriate permissions and ownership",
                                       "ComplianceScore":  100,
                                       "CVSSScore":  5.8,
                                       "TotalCriticalDirectories":  4,
                                       "Finding":  "All critical directories exist and are accessible",
                                       "AssessmentDate":  "\/Date(*************)\/",
                                       "ComplianceStatus":  "Compliant",
                                       "CurrentValue":  "Critical directories found: 4/4",
                                       "ControlName":  "File System Permissions on Critical Directories",
                                       "RiskLevel":  "Medium",
                                       "ControlID":  "WIN-7.1",
                                       "ExistingDirectories":  4,
                                       "BaselineValue":  "All critical directories exist with proper permissions",
                                       "DirectoryPermissions":  [
                                                                    {
                                                                        "SampleAccessRules":  [
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "CREATOR OWNER"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  -1610612736,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  2032127,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "APPLICATION PACKAGE AUTHORITY\\ALL APPLICATION PACKAGES"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  }
                                                                                              ],
                                                                        "Path":  "C:\\Windows\\System32",
                                                                        "Owner":  "NT SERVICE\\TrustedInstaller",
                                                                        "AccessRulesCount":  10,
                                                                        "Exists":  true
                                                                    },
                                                                    {
                                                                        "SampleAccessRules":  [
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "CREATOR OWNER"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  -1610612736,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  2032127,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "APPLICATION PACKAGE AUTHORITY\\ALL APPLICATION PACKAGES"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  }
                                                                                              ],
                                                                        "Path":  "C:\\Windows\\SysWOW64",
                                                                        "Owner":  "NT SERVICE\\TrustedInstaller",
                                                                        "AccessRulesCount":  10,
                                                                        "Exists":  true
                                                                    },
                                                                    {
                                                                        "SampleAccessRules":  [
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "CREATOR OWNER"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  -1610612736,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  2032127,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "APPLICATION PACKAGE AUTHORITY\\ALL APPLICATION PACKAGES"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  }
                                                                                              ],
                                                                        "Path":  "C:\\Program Files",
                                                                        "Owner":  "NT SERVICE\\TrustedInstaller",
                                                                        "AccessRulesCount":  10,
                                                                        "Exists":  true
                                                                    },
                                                                    {
                                                                        "SampleAccessRules":  [
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "CREATOR OWNER"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT AUTHORITY\\SYSTEM"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Administrators"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1245631,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  -1610612736,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "BUILTIN\\Users"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  268435456,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "NT SERVICE\\TrustedInstaller"
                                                                                                                            },
                                                                                                      "FileSystemRights":  2032127,
                                                                                                      "AccessControlType":  0
                                                                                                  },
                                                                                                  {
                                                                                                      "IdentityReference":  {
                                                                                                                                "Value":  "APPLICATION PACKAGE AUTHORITY\\ALL APPLICATION PACKAGES"
                                                                                                                            },
                                                                                                      "FileSystemRights":  1179817,
                                                                                                      "AccessControlType":  0
                                                                                                  }
                                                                                              ],
                                                                        "Path":  "C:\\Program Files (x86)",
                                                                        "Owner":  "NT SERVICE\\TrustedInstaller",
                                                                        "AccessRulesCount":  10,
                                                                        "Exists":  true
                                                                    }
                                                                ]
                                   },
    "WIN_1_1_WindowsUpdate":  {
                                  "Recommendation":  "Ensure Windows Update service is running and system receives regular security updates",
                                  "CVSSScore":  9.8,
                                  "Finding":  "Windows Update service or recent updates missing",
                                  "RiskLevel":  "Critical",
                                  "CurrentValue":  "Service: Stopped, Recent updates: 3",
                                  "LastUpdateInstalled":  "\/Date(1755118800000)\/",
                                  "AssessmentDate":  "\/Date(*************)\/",
                                  "ControlID":  "WIN-1.1",
                                  "BaselineValue":  "Windows Update service running, regular updates installed",
                                  "UpdateServiceStartType":  2,
                                  "ComplianceScore":  25,
                                  "ComplianceStatus":  "Review Required",
                                  "RecentUpdates":  [
                                                        {
                                                            "HotFixID":  "KB5063877",
                                                            "Description":  "Security Update",
                                                            "InstalledBy":  "NT AUTHORITY\\SYSTEM",
                                                            "InstalledOn":  {
                                                                                "value":  "\/Date(1755118800000)\/",
                                                                                "DateTime":  "Thursday, August 14, 2025 12:00:00 AM"
                                                                            }
                                                        },
                                                        {
                                                            "HotFixID":  "KB5062800",
                                                            "Description":  "Security Update",
                                                            "InstalledBy":  "NT AUTHORITY\\SYSTEM",
                                                            "InstalledOn":  {
                                                                                "value":  "\/Date(1753218000000)\/",
                                                                                "DateTime":  "Wednesday, July 23, 2025 12:00:00 AM"
                                                                            }
                                                        },
                                                        {
                                                            "HotFixID":  "KB5062068",
                                                            "Description":  "Update",
                                                            "InstalledBy":  "NT AUTHORITY\\SYSTEM",
                                                            "InstalledOn":  {
                                                                                "value":  "\/Date(1753131600000)\/",
                                                                                "DateTime":  "Tuesday, July 22, 2025 12:00:00 AM"
                                                                            }
                                                        }
                                                    ],
                                  "UpdateServiceStatus":  1,
                                  "RecentUpdatesCount":  3,
                                  "UpdateSettings":  {
                                                         "AutoUpdateEnabled":  true,
                                                         "AutoInstallMinorUpdates":  null
                                                     },
                                  "ControlName":  "Windows Update and Patch Management"
                              },
    "WIN_6_1_RegistrySecurity":  {
                                     "TotalSecurityKeys":  5,
                                     "Recommendation":  "Review and configure critical security registry settings according to security baselines",
                                     "ComplianceScore":  80,
                                     "CVSSScore":  6.2,
                                     "Finding":  "Most critical registry security settings are present",
                                     "BaselineValue":  "All critical security registry settings properly configured",
                                     "FoundSecurityKeys":  4,
                                     "ComplianceStatus":  "Compliant",
                                     "ControlName":  "Registry Security Settings",
                                     "RiskLevel":  "Medium",
                                     "ControlID":  "WIN-6.1",
                                     "AssessmentDate":  "\/Date(*************)\/",
                                     "RegistrySettings":  [
                                                              {
                                                                  "Path":  "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Lsa",
                                                                  "Description":  "LM Authentication Level",
                                                                  "Name":  "LmCompatibilityLevel",
                                                                  "Status":  "Missing",
                                                                  "Value":  "Not Found"
                                                              },
                                                              {
                                                                  "Path":  "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Lsa",
                                                                  "Description":  "Disable LM Hash Storage",
                                                                  "Name":  "NoLMHash",
                                                                  "Status":  "Found",
                                                                  "Value":  1
                                                              },
                                                              {
                                                                  "Path":  "HKLM:\\SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters",
                                                                  "Description":  "Netlogon Signing Required",
                                                                  "Name":  "RequireSignOrSeal",
                                                                  "Status":  "Found",
                                                                  "Value":  1
                                                              },
                                                              {
                                                                  "Path":  "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System",
                                                                  "Description":  "User Account Control Enabled",
                                                                  "Name":  "EnableLUA",
                                                                  "Status":  "Found",
                                                                  "Value":  1
                                                              },
                                                              {
                                                                  "Path":  "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System",
                                                                  "Description":  "UAC Admin Consent Behavior",
                                                                  "Name":  "ConsentPromptBehaviorAdmin",
                                                                  "Status":  "Found",
                                                                  "Value":  0
                                                              }
                                                          ],
                                     "CurrentValue":  "Security registry keys found: 4/5"
                                 },
    "WIN_2_1_UserAccounts":  {
                                 "AdminAccountEnabled":  false,
                                 "ComplianceScore":  100,
                                 "GuestAccountEnabled":  false,
                                 "CVSSScore":  8.2,
                                 "Finding":  "Password policy appears adequate",
                                 "TotalLocalUsers":  4,
                                 "AssessmentDate":  "\/Date(*************)\/",
                                 "ComplianceStatus":  "Compliant",
                                 "PasswordPolicy":  {
                                                        "MinPasswordLength":  "8",
                                                        "LockoutThreshold":  "5"
                                                    },
                                 "ControlName":  "User Account and Password Policies",
                                 "RiskLevel":  "High",
                                 "Recommendation":  "Implement strong password policy, disable default Administrator and Guest accounts",
                                 "ControlID":  "WIN-2.1",
                                 "LocalUsers":  [
                                                    {
                                                        "Name":  "admlocal",
                                                        "Enabled":  true,
                                                        "LastLogon":  "\/Date(*************)\/",
                                                        "PasswordRequired":  true,
                                                        "PasswordExpires":  null
                                                    },
                                                    {
                                                        "Name":  "DefaultAccount",
                                                        "Enabled":  false,
                                                        "LastLogon":  null,
                                                        "PasswordRequired":  false,
                                                        "PasswordExpires":  null
                                                    },
                                                    {
                                                        "Name":  "Guest",
                                                        "Enabled":  false,
                                                        "LastLogon":  null,
                                                        "PasswordRequired":  false,
                                                        "PasswordExpires":  null
                                                    },
                                                    {
                                                        "Name":  "WDAGUtilityAccount",
                                                        "Enabled":  false,
                                                        "LastLogon":  null,
                                                        "PasswordRequired":  true,
                                                        "PasswordExpires":  "\/Date(*************)\/"
                                                    }
                                                ],
                                 "BaselineValue":  "Strong password policy, Administrator disabled, Guest disabled",
                                 "CurrentValue":  "Min password length: 8, Admin enabled: , Guest enabled: False"
                             },
    "ComplianceSummary":  {
                              "FrameworkVersion":  "Windows Server Security Controls v1.0",
                              "ComputerName":  "XXFinOrg01",
                              "AuditExecutedBy":  "ad11bar",
                              "TotalControlsAssessed":  7,
                              "AuditType":  "Read-Only Assessment - Production Safe",
                              "FailedAssessments":  0,
                              "PowerShellVersion":  "5.1.17763.7553",
                              "AuditStartTime":  "\/Date(*************)\/",
                              "AuditEndTime":  "\/Date(1755599173948)\/",
                              "AssessmentType":  "Windows Server 2019 Security Controls Audit",
                              "AuditDurationSeconds":  10.76,
                              "SuccessfulAssessments":  7,
                              "AuditID":  "ce5bb813-41fa-4797-941d-fc729397aaa3"
                          },
    "AuditMetadata":  {
                          "ComputerName":  "XXFinOrg01",
                          "UserDomain":  "XXFinOrg",
                          "Workgroup":  null,
                          "AuditStartTime":  "\/Date(*************)\/",
                          "Domain":  "XXFinOrg.com",
                          "OSCaption":  "Microsoft Windows Server 2019 Standard",
                          "AuditUser":  "ad11bar",
                          "AssessmentScope":  "8 Critical Windows Server Security Controls",
                          "WindowsBuildNumber":  "17763.1.amd64fre.rs5_release.180914-1434",
                          "WindowsVersion":  "1809",
                          "OSArchitecture":  "64-bit",
                          "TotalPhysicalMemory":  "Unknown",
                          "AuditID":  "ce5bb813-41fa-4797-941d-fc729397aaa3"
                      },
    "WIN_3_1_WindowsFirewall":  {
                                    "ActiveRulesCount":  50,
                                    "TotalProfiles":  3,
                                    "Recommendation":  "Ensure Windows Firewall service is running and all profiles are enabled with appropriate rules",
                                    "EnabledProfiles":  2,
                                    "CVSSScore":  7.8,
                                    "Finding":  "Windows Firewall configuration issues detected",
                                    "ComplianceScore":  75,
                                    "RiskLevel":  "High",
                                    "AssessmentDate":  "\/Date(*************)\/",
                                    "FirewallProfiles":  [
                                                             {
                                                                 "Name":  "Domain",
                                                                 "Enabled":  0,
                                                                 "DefaultInboundAction":  0,
                                                                 "DefaultOutboundAction":  0,
                                                                 "LogFileName":  "%systemroot%\\system32\\LogFiles\\Firewall\\pfirewall.log"
                                                             },
                                                             {
                                                                 "Name":  "Private",
                                                                 "Enabled":  1,
                                                                 "DefaultInboundAction":  0,
                                                                 "DefaultOutboundAction":  0,
                                                                 "LogFileName":  "%systemroot%\\system32\\LogFiles\\Firewall\\pfirewall.log"
                                                             },
                                                             {
                                                                 "Name":  "Public",
                                                                 "Enabled":  1,
                                                                 "DefaultInboundAction":  0,
                                                                 "DefaultOutboundAction":  0,
                                                                 "LogFileName":  "%systemroot%\\system32\\LogFiles\\Firewall\\pfirewall.log"
                                                             }
                                                         ],
                                    "ControlID":  "WIN-3.1",
                                    "BaselineValue":  "Windows Firewall service running, all profiles enabled",
                                    "FirewallServiceStatus":  4,
                                    "ComplianceStatus":  "Review Required",
                                    "CurrentValue":  "Service: Running, Enabled profiles: /3",
                                    "SampleRules":  [
                                                        {
                                                            "DisplayName":  "Connected User Experiences and Telemetry",
                                                            "Direction":  2,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Delivery Optimization (TCP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Delivery Optimization (UDP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "AllJoyn Router (TCP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  3
                                                        },
                                                        {
                                                            "DisplayName":  "AllJoyn Router (TCP-Out)",
                                                            "Direction":  2,
                                                            "Action":  2,
                                                            "Profile":  3
                                                        },
                                                        {
                                                            "DisplayName":  "AllJoyn Router (UDP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  3
                                                        },
                                                        {
                                                            "DisplayName":  "AllJoyn Router (UDP-Out)",
                                                            "Direction":  2,
                                                            "Action":  2,
                                                            "Profile":  3
                                                        },
                                                        {
                                                            "DisplayName":  "DIAL protocol server (HTTP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  1
                                                        },
                                                        {
                                                            "DisplayName":  "DIAL protocol server (HTTP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  2
                                                        },
                                                        {
                                                            "DisplayName":  "Windows Device Management Enrollment Service (TCP out)",
                                                            "Direction":  2,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Windows Remote Management (HTTP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  3
                                                        },
                                                        {
                                                            "DisplayName":  "Windows Remote Management (HTTP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  4
                                                        },
                                                        {
                                                            "DisplayName":  "Remote Desktop - User Mode (TCP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Remote Desktop - User Mode (UDP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Remote Desktop - Shadow (TCP-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  0
                                                        },
                                                        {
                                                            "DisplayName":  "Cast to Device streaming server (HTTP-Streaming-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  1
                                                        },
                                                        {
                                                            "DisplayName":  "Cast to Device streaming server (HTTP-Streaming-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  2
                                                        },
                                                        {
                                                            "DisplayName":  "Cast to Device streaming server (HTTP-Streaming-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  4
                                                        },
                                                        {
                                                            "DisplayName":  "Cast to Device streaming server (RTCP-Streaming-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  1
                                                        },
                                                        {
                                                            "DisplayName":  "Cast to Device streaming server (RTCP-Streaming-In)",
                                                            "Direction":  1,
                                                            "Action":  2,
                                                            "Profile":  2
                                                        }
                                                    ],
                                    "ControlName":  "Windows Firewall Configuration"
                                },
    "WIN_4_1_AuditLogging":  {
                                 "Recommendation":  "Ensure Event Log service is running and critical event logs are enabled with appropriate retention policies",
                                 "ComplianceScore":  75,
                                 "CVSSScore":  6.5,
                                 "TotalEventLogs":  2,
                                 "EventLogs":  [
                                                   {
                                                       "RecordCount":  126812,
                                                       "IsEnabled":  true,
                                                       "LogMode":  0,
                                                       "LogName":  "System",
                                                       "MaximumSizeInBytes":  41943040
                                                   },
                                                   {
                                                       "RecordCount":  38256,
                                                       "IsEnabled":  true,
                                                       "LogMode":  0,
                                                       "LogName":  "Application",
                                                       "MaximumSizeInBytes":  41943040
                                                   }
                                               ],
                                 "EventLogServiceStatus":  4,
                                 "AssessmentDate":  "\/Date(*************)\/",
                                 "ComplianceStatus":  "Review Required",
                                 "EnabledEventLogs":  2,
                                 "ControlName":  "Audit Logging and Event Log Settings",
                                 "RiskLevel":  "Medium",
                                 "ControlID":  "WIN-4.1",
                                 "Finding":  "Event logging service active",
                                 "AuditPolicy":  {
                                                     "Error":  "auditpol command failed",
                                                     "Available":  false
                                                 },
                                 "BaselineValue":  "Event Log service running, critical logs enabled with adequate retention",
                                 "CurrentValue":  "Event Log service: Running, Enabled logs: /2"
                             },
    "WIN_5_1_ServiceSecurity":  {
                                    "CriticalServicesRunning":  6,
                                    "Recommendation":  "Ensure critical security services are running and unnecessary services are disabled",
                                    "CVSSScore":  6.8,
                                    "Finding":  "Some critical security services are not running",
                                    "RiskLevel":  "Medium",
                                    "CurrentValue":  "Total services: 255, Running: 109, Critical running: Microsoft.PowerShell.Commands.GenericMeasureInfo.Count",
                                    "CriticalServices":  [
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  1,
                                                                 "Name":  "wuauserv",
                                                                 "DisplayName":  "Windows Update"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "mpssvc",
                                                                 "DisplayName":  "Windows Defender Firewall"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "EventLog",
                                                                 "DisplayName":  "Windows Event Log"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "Winmgmt",
                                                                 "DisplayName":  "Windows Management Instrumentation"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "RpcSs",
                                                                 "DisplayName":  "Remote Procedure Call (RPC)"
                                                             },
                                                             {
                                                                 "StartType":  3,
                                                                 "Status":  1,
                                                                 "Name":  "BITS",
                                                                 "DisplayName":  "Background Intelligent Transfer Service"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "CryptSvc",
                                                                 "DisplayName":  "Cryptographic Services"
                                                             },
                                                             {
                                                                 "StartType":  2,
                                                                 "Status":  4,
                                                                 "Name":  "TrustedInstaller",
                                                                 "DisplayName":  "Windows Modules Installer"
                                                             }
                                                         ],
                                    "StoppedServices":  146,
                                    "TotalServices":  255,
                                    "ControlID":  "WIN-5.1",
                                    "BaselineValue":  "Critical security services running, unnecessary services disabled",
                                    "ComplianceScore":  75,
                                    "ComplianceStatus":  "Review Required",
                                    "SystemServices":  [
                                                           {
                                                               "Name":  "AEGASTService",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "AGRtuService",
                                                               "StartName":  "NT AUTHORITY\\NetworkService",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "AGWebCacheService",
                                                               "StartName":  "NT AUTHORITY\\NetworkService",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "AJRouter",
                                                               "StartName":  "NT AUTHORITY\\LocalService",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "ALG",
                                                               "StartName":  "NT AUTHORITY\\LocalService",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AppHostSvc",
                                                               "StartName":  "localSystem",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "AppIDSvc",
                                                               "StartName":  "NT Authority\\LocalService",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "Appinfo",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "AppMgmt",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AppReadiness",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AppVClient",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AppXSvc",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "aspnet_state",
                                                               "StartName":  "NT AUTHORITY\\NetworkService",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AudioEndpointBuilder",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "Audiosrv",
                                                               "StartName":  "NT AUTHORITY\\LocalService",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "AxInstSV",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "BFE",
                                                               "StartName":  "NT AUTHORITY\\LocalService",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "BITS",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Stopped"
                                                           },
                                                           {
                                                               "Name":  "BrokerInfrastructure",
                                                               "StartName":  "LocalSystem",
                                                               "State":  "Running"
                                                           },
                                                           {
                                                               "Name":  "BTAGService",
                                                               "StartName":  "NT AUTHORITY\\LocalService",
                                                               "State":  "Stopped"
                                                           }
                                                       ],
                                    "AssessmentDate":  "\/Date(*************)\/",
                                    "ControlName":  "Service Configurations and Security",
                                    "RunningServices":  109
                                }
}
