# SQL Server 2022 Linux Connection Test Script
Write-Host "SQL Server 2022 Linux Connection Test" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Connection parameters
$ServerInstance = "***********,1433"
$Username = "testuser"
$Password = "1234"

try {
    # Test 1: Check and properly load SqlServer module
    Write-Host "Step 1: Checking SqlServer PowerShell module..." -ForegroundColor Yellow

    # First check if module is available
    $sqlModule = Get-Module -ListAvailable -Name SqlServer
    if (-not $sqlModule) {
        Write-Host "WARNING: SqlServer module not found. Installing..." -ForegroundColor Yellow
        Install-Module -Name SqlServer -Force -Scope CurrentUser -AllowClobber -Repository PSGallery
        # Refresh module list after installation
        $sqlModule = Get-Module -ListAvailable -Name SqlServer
    }

    if ($sqlModule) {
        Write-Host "Found SqlServer module (Version: $($sqlModule[0].Version))" -ForegroundColor Cyan

        # Force remove any existing module and reimport
        Remove-Module SqlServer -Force -ErrorAction SilentlyContinue
        Import-Module SqlServer -Force -ErrorAction Stop

        # Verify the cmdlet is available
        if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) {
            Write-Host "SUCCESS: SqlServer module loaded and Invoke-Sqlcmd available" -ForegroundColor Green
        } else {
            throw "Invoke-Sqlcmd cmdlet not available after module import"
        }
    } else {
        throw "SqlServer module installation failed"
    }

    # Test 2: Basic connectivity test
    Write-Host "Step 2: Testing SQL Server connectivity..." -ForegroundColor Yellow
    
    # Create credential object
    $SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
    $Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)
    
    # Test basic connection
    $TestQuery = "SELECT @@SERVERNAME AS ServerName, @@VERSION AS Version, GETDATE() AS CurrentTime"
    $Result = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $TestQuery -ConnectionTimeout 30 -QueryTimeout 30 -ErrorAction Stop
    
    Write-Host "SUCCESS: SQL Server connection successful!" -ForegroundColor Green
    Write-Host "Server Name: $($Result.ServerName)" -ForegroundColor Cyan
    Write-Host "Current Time: $($Result.CurrentTime)" -ForegroundColor Cyan
    Write-Host "SQL Version: $($Result.Version.Split([char]13)[0])" -ForegroundColor Cyan

    # Test 3: Check SQL Server 2022 Linux specific features
    Write-Host "Step 3: Checking SQL Server 2022 Linux environment..." -ForegroundColor Yellow
    
    $LinuxQuery = @"
SELECT 
    SERVERPROPERTY('ProductVersion') AS ProductVersion,
    SERVERPROPERTY('ProductLevel') AS ProductLevel,
    SERVERPROPERTY('Edition') AS Edition,
    SERVERPROPERTY('EngineEdition') AS EngineEdition,
    CASE 
        WHEN SERVERPROPERTY('ProductMajorVersion') >= 16 THEN 'SQL Server 2022+'
        WHEN SERVERPROPERTY('ProductMajorVersion') = 15 THEN 'SQL Server 2019'
        WHEN SERVERPROPERTY('ProductMajorVersion') = 14 THEN 'SQL Server 2017'
        ELSE 'Older Version'
    END AS VersionCategory
"@

    $LinuxResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $LinuxQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: SQL Server environment details:" -ForegroundColor Green
    Write-Host "Product Version: $($LinuxResult.ProductVersion)" -ForegroundColor Cyan
    Write-Host "Product Level: $($LinuxResult.ProductLevel)" -ForegroundColor Cyan
    Write-Host "Edition: $($LinuxResult.Edition)" -ForegroundColor Cyan
    Write-Host "Version Category: $($LinuxResult.VersionCategory)" -ForegroundColor Cyan

    # Test 4: Check user permissions for audit
    Write-Host "Step 4: Checking user permissions for CIS audit..." -ForegroundColor Yellow
    
    $PermissionQuery = @"
SELECT 
    USER_NAME() AS CurrentUser,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW SERVER STATE') AS HasViewServerState,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW ANY DEFINITION') AS HasViewAnyDefinition,
    IS_SRVROLEMEMBER('sysadmin') AS IsSysAdmin,
    IS_SRVROLEMEMBER('securityadmin') AS IsSecurityAdmin
"@

    $PermResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $PermissionQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: User permission check:" -ForegroundColor Green
    Write-Host "Current User: $($PermResult.CurrentUser)" -ForegroundColor Cyan
    Write-Host "VIEW SERVER STATE: $($PermResult.HasViewServerState)" -ForegroundColor $(if ($PermResult.HasViewServerState) { "Green" } else { "Red" })
    Write-Host "VIEW ANY DEFINITION: $($PermResult.HasViewAnyDefinition)" -ForegroundColor $(if ($PermResult.HasViewAnyDefinition) { "Green" } else { "Red" })
    Write-Host "SysAdmin Role: $($PermResult.IsSysAdmin)" -ForegroundColor Cyan
    Write-Host "SecurityAdmin Role: $($PermResult.IsSecurityAdmin)" -ForegroundColor Cyan

    # Test 5: Quick CIS control test
    Write-Host "Step 5: Testing sample CIS control (Authentication Mode)..." -ForegroundColor Yellow
    
    $CISTestQuery = @"
SELECT 
    'CIS-4.1' AS ControlID,
    'SQL Server Authentication Mode' AS ControlName,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Windows Authentication Only'
        ELSE 'Mixed Mode Authentication'
    END AS CurrentValue,
    'Windows Authentication Only' AS BaselineValue,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus
"@

    $CISResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $CISTestQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: Sample CIS Control Test (CIS-4.1):" -ForegroundColor Green
    Write-Host "Control: $($CISResult.ControlName)" -ForegroundColor Cyan
    Write-Host "Current Value: $($CISResult.CurrentValue)" -ForegroundColor Cyan
    Write-Host "Baseline: $($CISResult.BaselineValue)" -ForegroundColor Cyan
    Write-Host "Compliance Status: $($CISResult.ComplianceStatus)" -ForegroundColor $(if ($CISResult.ComplianceStatus -eq "Compliant") { "Green" } else { "Yellow" })

    Write-Host ""
    Write-Host "CONNECTION TEST SUCCESSFUL!" -ForegroundColor Green
    Write-Host "Your SQL Server 2022 Linux environment is ready for CIS Controls v8 audit testing." -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "CONNECTION TEST FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.InnerException) {
        Write-Host "Inner Error: $($_.Exception.InnerException.Message)" -ForegroundColor Red
    }

    # Additional debugging information
    Write-Host ""
    Write-Host "Debug Information:" -ForegroundColor Yellow
    Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host "Available SqlServer modules:" -ForegroundColor White
    Get-Module -ListAvailable -Name SqlServer | ForEach-Object {
        Write-Host "  - Version: $($_.Version), Path: $($_.ModuleBase)" -ForegroundColor White
    }
    Write-Host "Invoke-Sqlcmd available: $(if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) { 'Yes' } else { 'No' })" -ForegroundColor White

    Write-Host ""
    Write-Host "Troubleshooting suggestions:" -ForegroundColor Yellow
    Write-Host "1. Restart PowerShell session and try again" -ForegroundColor White
    Write-Host "2. Verify SQL Server container is running: docker ps" -ForegroundColor White
    Write-Host "3. Check port mapping: docker port <container_name>" -ForegroundColor White
    Write-Host "4. Verify credentials: docker exec <container> /opt/mssql-tools/bin/sqlcmd -S localhost -U testuser -P 1234" -ForegroundColor White
    Write-Host "5. Try manual module import: Import-Module SqlServer -Force" -ForegroundColor White
}
