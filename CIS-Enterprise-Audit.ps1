#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    CIS Controls v8 Enterprise Audit for Windows Server 2019
    
.DESCRIPTION
    Enterprise-scale PowerShell script to audit multiple Windows Server 2019 systems
    against CIS Controls v8 and generate consolidated compliance reports.
    
.PARAMETER ComputerListFile
    Path to text file containing list of computer names (one per line)
    
.PARAMETER ComputerName
    Array of computer names to audit
    
.PARAMETER OutputPath
    Directory path for audit reports and logs
    
.PARAMETER Credential
    Credentials for remote computer access
    
.PARAMETER MaxConcurrentJobs
    Maximum number of concurrent audit jobs (default: 10)
    
.PARAMETER GenerateConsolidatedReport
    Generate enterprise-wide consolidated report
    
.PARAMETER EmailReport
    Send email report to specified recipients
    
.PARAMETER SMTPServer
    SMTP server for email notifications
    
.PARAMETER EmailTo
    Email recipients for reports
    
.PARAMETER EmailFrom
    Email sender address
    
.EXAMPLE
    .\CIS-Enterprise-Audit.ps1 -ComputerListFile "servers.txt" -OutputPath "C:\CISAudits" -GenerateConsolidatedReport
    
.EXAMPLE
    .\CIS-Enterprise-Audit.ps1 -<PERSON>Name "Server01","Server02","Server03" -EmailReport -EmailTo "<EMAIL>"
#>

[CmdletBinding()]
param(
    [string]$ComputerListFile,
    [string[]]$ComputerName,
    [string]$OutputPath = (Join-Path (Get-Location).Path "CIS-Enterprise-Audit-$(Get-Date -Format 'yyyyMMdd-HHmmss')"),
    [PSCredential]$Credential,
    [int]$MaxConcurrentJobs = 10,
    [switch]$GenerateConsolidatedReport,
    [switch]$EmailReport,
    [string]$SMTPServer,
    [string[]]$EmailTo,
    [string]$EmailFrom
)

# Global Variables
$Script:StartTime = Get-Date
$Script:LogFile = Join-Path $OutputPath "Enterprise-Audit-Log-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
$Script:AllResults = @()
$Script:JobResults = @{}

# Initialize logging
function Write-EnterpriseLog {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry -ForegroundColor $(
        switch ($Level) {
            "INFO" { "White" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
            "SUCCESS" { "Green" }
        }
    )
    
    if (Test-Path $Script:LogFile) {
        Add-Content -Path $Script:LogFile -Value $logEntry
    }
}

# Prepare computer list
if ($ComputerListFile) {
    if (!(Test-Path $ComputerListFile)) {
        Write-Error "Computer list file not found: $ComputerListFile"
        exit 1
    }
    $ComputerName = Get-Content $ComputerListFile | Where-Object { $_.Trim() -ne "" }
}

if (!$ComputerName -or $ComputerName.Count -eq 0) {
    Write-Error "No computers specified for audit"
    exit 1
}

# Create output directory
if (!(Test-Path $OutputPath)) {
    try {
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
        Write-EnterpriseLog "Created output directory: $OutputPath" "INFO"
    }
    catch {
        Write-Error "Failed to create output directory: $($_.Exception.Message)"
        exit 1
    }
}

# Initialize log file
"CIS Controls v8 Enterprise Audit Log" | Out-File -FilePath $Script:LogFile -Encoding UTF8

Write-EnterpriseLog "Starting CIS Controls v8 Enterprise Audit" "INFO"
Write-EnterpriseLog "Target Systems: $($ComputerName.Count)" "INFO"
Write-EnterpriseLog "Output Path: $OutputPath" "INFO"
Write-EnterpriseLog "Max Concurrent Jobs: $MaxConcurrentJobs" "INFO"

# Test connectivity to all systems
Write-EnterpriseLog "Testing connectivity to target systems..." "INFO"
$reachableComputers = @()
$unreachableComputers = @()

foreach ($computer in $ComputerName) {
    Write-Host "Testing $computer..." -NoNewline
    try {
        if ($computer -eq $env:COMPUTERNAME -or (Test-Connection -ComputerName $computer -Count 1 -Quiet)) {
            $reachableComputers += $computer
            Write-Host " OK" -ForegroundColor Green
        } else {
            $unreachableComputers += $computer
            Write-Host " FAILED" -ForegroundColor Red
        }
    }
    catch {
        $unreachableComputers += $computer
        Write-Host " ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-EnterpriseLog "Reachable systems: $($reachableComputers.Count)" "SUCCESS"
Write-EnterpriseLog "Unreachable systems: $($unreachableComputers.Count)" "WARNING"

if ($unreachableComputers.Count -gt 0) {
    Write-EnterpriseLog "Unreachable systems: $($unreachableComputers -join ', ')" "WARNING"
}

if ($reachableComputers.Count -eq 0) {
    Write-EnterpriseLog "No reachable systems found. Exiting." "ERROR"
    exit 1
}

# Define audit job script block
$auditScriptBlock = {
    param($ComputerName, $Credential, $OutputPath)
    
    try {
        # Import the main audit framework
        $frameworkPath = Join-Path $using:PSScriptRoot "CIS-Audit-Framework.ps1"
        if (!(Test-Path $frameworkPath)) {
            throw "CIS-Audit-Framework.ps1 not found in script directory"
        }
        
        # Execute audit
        $params = @{
            ComputerName = @($ComputerName)
            OutputPath = $OutputPath
            OutputFormat = "JSON"
        }
        
        if ($Credential) {
            $params.Credential = $Credential
        }
        
        $result = & $frameworkPath @params
        
        return @{
            ComputerName = $ComputerName
            Success = $true
            Results = $result.Results
            Summary = $result.Summary
            ReportPaths = $result.ReportPaths
            Duration = $result.Duration
            Error = $null
        }
    }
    catch {
        return @{
            ComputerName = $ComputerName
            Success = $false
            Results = $null
            Summary = $null
            ReportPaths = $null
            Duration = $null
            Error = $_.Exception.Message
        }
    }
}

# Execute audits using PowerShell jobs
Write-EnterpriseLog "Starting parallel audit execution..." "INFO"

$jobs = @()
$jobIndex = 0

foreach ($computer in $reachableComputers) {
    # Wait if we've reached the maximum concurrent jobs
    while ((Get-Job -State Running).Count -ge $MaxConcurrentJobs) {
        Start-Sleep -Seconds 2
        
        # Check for completed jobs
        $completedJobs = Get-Job -State Completed
        foreach ($completedJob in $completedJobs) {
            $jobResult = Receive-Job -Job $completedJob
            $Script:JobResults[$completedJob.Name] = $jobResult
            Remove-Job -Job $completedJob
            
            if ($jobResult.Success) {
                Write-EnterpriseLog "Completed audit for $($jobResult.ComputerName)" "SUCCESS"
                $Script:AllResults += $jobResult.Results
            } else {
                Write-EnterpriseLog "Failed audit for $($jobResult.ComputerName): $($jobResult.Error)" "ERROR"
            }
        }
    }
    
    # Start new job
    $jobName = "CISAudit_$computer"
    $job = Start-Job -Name $jobName -ScriptBlock $auditScriptBlock -ArgumentList $computer, $Credential, $OutputPath
    $jobs += $job
    
    Write-EnterpriseLog "Started audit job for $computer (Job: $jobName)" "INFO"
    $jobIndex++
}

# Wait for all remaining jobs to complete
Write-EnterpriseLog "Waiting for all audit jobs to complete..." "INFO"

while ((Get-Job -State Running).Count -gt 0) {
    Start-Sleep -Seconds 5
    
    $runningJobs = Get-Job -State Running
    $completedJobs = Get-Job -State Completed
    
    Write-Host "Running jobs: $($runningJobs.Count), Completed: $($completedJobs.Count)" -ForegroundColor Yellow
    
    # Process completed jobs
    foreach ($completedJob in $completedJobs) {
        if (!$Script:JobResults.ContainsKey($completedJob.Name)) {
            $jobResult = Receive-Job -Job $completedJob
            $Script:JobResults[$completedJob.Name] = $jobResult
            Remove-Job -Job $completedJob
            
            if ($jobResult.Success) {
                Write-EnterpriseLog "Completed audit for $($jobResult.ComputerName)" "SUCCESS"
                $Script:AllResults += $jobResult.Results
            } else {
                Write-EnterpriseLog "Failed audit for $($jobResult.ComputerName): $($jobResult.Error)" "ERROR"
            }
        }
    }
}

# Process any remaining completed jobs
$remainingJobs = Get-Job -State Completed
foreach ($job in $remainingJobs) {
    if (!$Script:JobResults.ContainsKey($job.Name)) {
        $jobResult = Receive-Job -Job $job
        $Script:JobResults[$job.Name] = $jobResult
        
        if ($jobResult.Success) {
            Write-EnterpriseLog "Completed audit for $($jobResult.ComputerName)" "SUCCESS"
            $Script:AllResults += $jobResult.Results
        } else {
            Write-EnterpriseLog "Failed audit for $($jobResult.ComputerName): $($jobResult.Error)" "ERROR"
        }
    }
    Remove-Job -Job $job
}

# Generate consolidated enterprise report
if ($GenerateConsolidatedReport -and $Script:AllResults.Count -gt 0) {
    Write-EnterpriseLog "Generating consolidated enterprise report..." "INFO"

    # Load report generation functions
    $reportGeneratorPath = Join-Path $PSScriptRoot "CIS-Report-Generator.ps1"
    if (Test-Path $reportGeneratorPath) {
        . $reportGeneratorPath
    }

    # Calculate enterprise-wide summary
    $enterpriseSummary = @{
        TotalSystems = ($Script:AllResults | Select-Object -Unique ComputerName).Count
        TotalControls = $Script:AllResults.Count
        Compliant = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
        NonCompliant = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
        NotConfigured = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Not Configured" }).Count
        Errors = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Error" }).Count
        HighRiskIssues = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "High" }).Count
        MediumRiskIssues = ($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "Medium" }).Count
        CompliancePercentage = if ($Script:AllResults.Count -gt 0) {
            [math]::Round((($Script:AllResults | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count / $Script:AllResults.Count) * 100, 2)
        } else { 0 }
        AuditDate = $Script:StartTime
        Duration = (Get-Date) - $Script:StartTime
        SuccessfulAudits = ($Script:JobResults.Values | Where-Object { $_.Success }).Count
        FailedAudits = ($Script:JobResults.Values | Where-Object { !$_.Success }).Count
    }

    # Generate enterprise reports
    $enterpriseReportPath = Join-Path $OutputPath "Enterprise-Reports"
    if (!(Test-Path $enterpriseReportPath)) {
        New-Item -Path $enterpriseReportPath -ItemType Directory -Force | Out-Null
    }

    # Generate HTML enterprise report
    $htmlReport = Export-CISAuditToHTML -Results $Script:AllResults -OutputPath $enterpriseReportPath -Summary $enterpriseSummary
    $csvReport = Export-CISAuditToCSV -Results $Script:AllResults -OutputPath $enterpriseReportPath -Summary $enterpriseSummary
    $jsonReport = Export-CISAuditToJSON -Results $Script:AllResults -OutputPath $enterpriseReportPath -Summary $enterpriseSummary

    Write-EnterpriseLog "Enterprise reports generated:" "SUCCESS"
    Write-EnterpriseLog "  HTML: $htmlReport" "INFO"
    Write-EnterpriseLog "  CSV: $($csvReport.ReportPath)" "INFO"
    Write-EnterpriseLog "  JSON: $jsonReport" "INFO"

    # Generate system-by-system summary
    $systemSummaryPath = Join-Path $enterpriseReportPath "System-Summary-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
    $systemSummary = @()

    foreach ($computer in ($Script:AllResults | Select-Object -Unique ComputerName).ComputerName) {
        $computerResults = $Script:AllResults | Where-Object { $_.ComputerName -eq $computer }
        $jobResult = $Script:JobResults.Values | Where-Object { $_.ComputerName -eq $computer }

        $systemSummary += [PSCustomObject]@{
            ComputerName = $computer
            AuditStatus = if ($jobResult -and $jobResult.Success) { "Success" } else { "Failed" }
            TotalControls = $computerResults.Count
            Compliant = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
            NonCompliant = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
            NotConfigured = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Not Configured" }).Count
            Errors = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Error" }).Count
            CompliancePercentage = if ($computerResults.Count -gt 0) {
                [math]::Round((($computerResults | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count / $computerResults.Count) * 100, 2)
            } else { 0 }
            HighRiskIssues = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "High" }).Count
            MediumRiskIssues = ($computerResults | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "Medium" }).Count
            AuditDuration = if ($jobResult) { $jobResult.Duration } else { "N/A" }
        }
    }

    $systemSummary | Export-Csv -Path $systemSummaryPath -NoTypeInformation -Encoding UTF8
    Write-EnterpriseLog "System summary report: $systemSummaryPath" "SUCCESS"
}

# Email report if requested
if ($EmailReport -and $EmailTo -and $SMTPServer -and $EmailFrom) {
    Write-EnterpriseLog "Sending email report..." "INFO"

    try {
        $emailSubject = "CIS Controls v8 Enterprise Audit Report - $(Get-Date -Format 'yyyy-MM-dd')"
        $emailBody = @"
CIS Controls v8 Enterprise Audit Report
Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

EXECUTIVE SUMMARY:
- Total Systems Audited: $($enterpriseSummary.TotalSystems)
- Successful Audits: $($enterpriseSummary.SuccessfulAudits)
- Failed Audits: $($enterpriseSummary.FailedAudits)
- Overall Compliance: $($enterpriseSummary.CompliancePercentage)%

COMPLIANCE DETAILS:
- Compliant Controls: $($enterpriseSummary.Compliant)
- Non-Compliant Controls: $($enterpriseSummary.NonCompliant)
- Not Configured: $($enterpriseSummary.NotConfigured)
- Errors: $($enterpriseSummary.Errors)

RISK ASSESSMENT:
- High Risk Issues: $($enterpriseSummary.HighRiskIssues)
- Medium Risk Issues: $($enterpriseSummary.MediumRiskIssues)

Audit Duration: $($enterpriseSummary.Duration.ToString('hh\:mm\:ss'))

Detailed reports are attached to this email.

This is an automated report generated by the CIS Controls v8 Enterprise Audit system.
"@

        $attachments = @()
        if ($htmlReport) { $attachments += $htmlReport }
        if ($csvReport -and $csvReport.ReportPath) { $attachments += $csvReport.ReportPath }
        if ($systemSummaryPath) { $attachments += $systemSummaryPath }

        $emailParams = @{
            To = $EmailTo
            From = $EmailFrom
            Subject = $emailSubject
            Body = $emailBody
            SmtpServer = $SMTPServer
            Attachments = $attachments
        }

        Send-MailMessage @emailParams
        Write-EnterpriseLog "Email report sent successfully to: $($EmailTo -join ', ')" "SUCCESS"
    }
    catch {
        Write-EnterpriseLog "Failed to send email report: $($_.Exception.Message)" "ERROR"
    }
}

# Final summary
$endTime = Get-Date
$totalDuration = $endTime - $Script:StartTime

Write-EnterpriseLog "=== ENTERPRISE AUDIT SUMMARY ===" "INFO"
Write-EnterpriseLog "Total Systems Targeted: $($ComputerName.Count)" "INFO"
Write-EnterpriseLog "Reachable Systems: $($reachableComputers.Count)" "SUCCESS"
Write-EnterpriseLog "Unreachable Systems: $($unreachableComputers.Count)" "WARNING"
Write-EnterpriseLog "Successful Audits: $(($Script:JobResults.Values | Where-Object { $_.Success }).Count)" "SUCCESS"
Write-EnterpriseLog "Failed Audits: $(($Script:JobResults.Values | Where-Object { !$_.Success }).Count)" "ERROR"

if ($Script:AllResults.Count -gt 0) {
    Write-EnterpriseLog "Total Controls Evaluated: $($Script:AllResults.Count)" "INFO"
    Write-EnterpriseLog "Overall Compliance: $($enterpriseSummary.CompliancePercentage)%" "INFO"
    Write-EnterpriseLog "High Risk Issues: $($enterpriseSummary.HighRiskIssues)" "WARNING"
    Write-EnterpriseLog "Medium Risk Issues: $($enterpriseSummary.MediumRiskIssues)" "WARNING"
}

Write-EnterpriseLog "Total Audit Duration: $($totalDuration.ToString('hh\:mm\:ss'))" "INFO"
Write-EnterpriseLog "Output Directory: $OutputPath" "INFO"

if ($unreachableComputers.Count -gt 0) {
    Write-EnterpriseLog "Systems requiring attention: $($unreachableComputers -join ', ')" "WARNING"
}

Write-EnterpriseLog "CIS Controls v8 Enterprise Audit completed!" "SUCCESS"

# Return results for programmatic use
return @{
    TotalSystems = $ComputerName.Count
    SuccessfulAudits = ($Script:JobResults.Values | Where-Object { $_.Success }).Count
    FailedAudits = ($Script:JobResults.Values | Where-Object { !$_.Success }).Count
    AllResults = $Script:AllResults
    Summary = if ($enterpriseSummary) { $enterpriseSummary } else { $null }
    Duration = $totalDuration
    OutputPath = $OutputPath
    UnreachableSystems = $unreachableComputers
}
