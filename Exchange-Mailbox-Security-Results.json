{"AdministratorDiscovery": {"AdminClassification": {}, "AdminDomainMap": {}, "RoleAssignments": [], "CrossDomainRelationships": []}, "AuditMetadata": {"Error": "The term 'Get-OrganizationConfig' is not recognized as a name of a cmdlet, function, script file, or executable program.\r\nCheck the spelling of the name, or if a path was included, verify that the path is correct and try again."}, "CriticalError": {"StackTrace": null, "Timestamp": "2025-08-25T18:49:49.6184719+03:00", "Message": "Exchange PowerShell module required"}}