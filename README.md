# CIS Controls v8 Audit Framework for Windows Server 2019

A comprehensive PowerShell-based auditing solution for evaluating Windows Server 2019 systems against the 20 most critical CIS Controls v8 technical configurations.

## 🚀 Features

- **Comprehensive Auditing**: Evaluates 20 critical CIS Controls v8 configurations
- **Multi-Format Reports**: Generates CSV, HTML, and JSON reports
- **Enterprise Scale**: Supports auditing multiple systems concurrently
- **Automated Remediation**: Generates PowerShell scripts for non-compliant configurations
- **Risk Assessment**: Categorizes findings by risk level (High/Medium/Low)
- **Email Notifications**: Automated email reporting capabilities
- **Backup Support**: Creates backup scripts before remediation

## 📋 Prerequisites

- Windows PowerShell 5.1 or later
- Administrative privileges on target systems
- Network connectivity to remote systems (if auditing multiple servers)
- SMTP server access (for email notifications)

## 🏗️ Architecture

The framework consists of four main components:

1. **CIS-Audit-Framework.ps1** - Main audit engine
2. **CIS-Report-Generator.ps1** - Report generation functions
3. **CIS-Remediation-Generator.ps1** - Remediation script generator
4. **CIS-Enterprise-Audit.ps1** - Enterprise-scale batch auditing

## 🎯 CIS Controls Covered

The framework evaluates these critical CIS Controls v8:

### Tier 1 - Foundational Controls
- **Control 1**: Asset Inventory (Network Discovery, SSDP)
- **Control 2**: Software Inventory (Windows Installer policies)
- **Control 3**: Data Protection (BitLocker configuration)
- **Control 4**: Secure Configuration (Guest account, Anonymous access)
- **Control 5**: Account Management (Password policies, Account lockout)
- **Control 6**: Access Control (User rights assignments)

### Tier 2 - Essential Controls
- **Control 7**: Vulnerability Management (Windows Update)
- **Control 8**: Audit Log Management (Logon auditing)
- **Control 9**: Email/Web Protection (SmartScreen)
- **Control 10**: Malware Defenses (Windows Defender)
- **Control 11**: Data Recovery (System Restore)
- **Control 12**: Network Infrastructure (Windows Firewall)

### Additional Critical Controls
- **Control 13**: Network Monitoring
- **Control 16**: Application Security (PowerShell execution policy)

## 🚀 Quick Start

### Single System Audit

```powershell
# Basic audit with HTML report
.\CIS-Audit-Framework.ps1 -OutputFormat HTML -IncludeRemediation

# Audit with all report formats
.\CIS-Audit-Framework.ps1 -OutputFormat All -OutputPath "C:\CISAudits"
```

### Multiple Systems Audit

```powershell
# Audit specific servers
.\CIS-Audit-Framework.ps1 -ComputerName "Server01","Server02","Server03" -OutputFormat All

# Audit with credentials
$cred = Get-Credential
.\CIS-Audit-Framework.ps1 -ComputerName "Server01","Server02" -Credential $cred
```

### Enterprise Audit

```powershell
# Create server list file
"Server01" | Out-File servers.txt
"Server02" | Add-Content servers.txt
"Server03" | Add-Content servers.txt

# Run enterprise audit
.\CIS-Enterprise-Audit.ps1 -ComputerListFile "servers.txt" -GenerateConsolidatedReport -MaxConcurrentJobs 5
```

### Generate Remediation Scripts

```powershell
# Generate remediation scripts from audit results
.\CIS-Remediation-Generator.ps1 -AuditResultsPath "C:\CISAudits\results.json" -CreateBackup -GenerateGroupPolicyScript
```

## 📊 Report Formats

### HTML Report
- Executive dashboard with compliance metrics
- Interactive tables with filtering
- Risk-based color coding
- Remediation commands included

### CSV Report
- Detailed tabular data
- Easy import into Excel or databases
- Separate summary file

### JSON Report
- Machine-readable format
- Complete audit metadata
- API integration friendly

## 🔧 Configuration Examples

### Email Notifications

```powershell
.\CIS-Enterprise-Audit.ps1 `
    -ComputerListFile "servers.txt" `
    -GenerateConsolidatedReport `
    -EmailReport `
    -SMTPServer "mail.company.com" `
    -EmailFrom "<EMAIL>" `
    -EmailTo "<EMAIL>","<EMAIL>"
```

### Advanced Enterprise Audit

```powershell
$credential = Get-Credential -Message "Enter domain admin credentials"

.\CIS-Enterprise-Audit.ps1 `
    -ComputerName (Get-ADComputer -Filter "OperatingSystem -like '*Server 2019*'").Name `
    -Credential $credential `
    -OutputPath "\\FileServer\CISAudits\$(Get-Date -Format 'yyyy-MM')" `
    -MaxConcurrentJobs 15 `
    -GenerateConsolidatedReport `
    -EmailReport `
    -SMTPServer "smtp.company.com" `
    -EmailFrom "<EMAIL>" `
    -EmailTo "<EMAIL>"
```

## 📈 Understanding Results

### Compliance Status
- **Compliant**: Configuration matches CIS recommendation
- **Non-Compliant**: Configuration deviates from CIS recommendation
- **Not Configured**: Setting is not configured/found
- **Error**: Unable to read configuration

### Risk Levels
- **High**: Critical security configurations that pose significant risk
- **Medium**: Important configurations with moderate security impact
- **Low**: Best practice configurations with minimal security impact

### Remediation Priority
1. **Immediate (0-30 days)**: High-risk non-compliant controls
2. **Short-term (1-3 months)**: Medium-risk and foundational controls
3. **Long-term (3-6 months)**: Remaining controls and optimizations

## 🛠️ Remediation Workflow

1. **Run Initial Audit**
   ```powershell
   .\CIS-Audit-Framework.ps1 -OutputFormat JSON -IncludeRemediation
   ```

2. **Generate Remediation Scripts**
   ```powershell
   .\CIS-Remediation-Generator.ps1 -AuditResultsPath "results.json" -CreateBackup
   ```

3. **Review Generated Scripts**
   - Examine remediation commands
   - Test in non-production environment
   - Validate backup procedures

4. **Execute Remediation**
   ```powershell
   # Run backup script first
   .\CIS-Backup-ServerName-timestamp.ps1
   
   # Then run remediation
   .\CIS-Remediation-ServerName-timestamp.ps1
   ```

5. **Verify Remediation**
   ```powershell
   .\CIS-Audit-Framework.ps1 -OutputFormat HTML
   ```

## 📁 Output Structure

```
OutputPath/
├── CIS-Audit-Report-timestamp.html
├── CIS-Audit-Report-timestamp.csv
├── CIS-Audit-Report-timestamp.json
├── CIS-Audit-Summary-timestamp.csv
├── CIS-Audit-Log-timestamp.txt
├── Enterprise-Reports/
│   ├── Enterprise-CIS-Report.html
│   ├── System-Summary.csv
│   └── Consolidated-Results.json
└── Remediation-Scripts/
    ├── CIS-Remediation-Server01.ps1
    ├── CIS-Backup-Server01.ps1
    └── CIS-GroupPolicy-Guide.txt
```

## ⚠️ Important Notes

### Security Considerations
- Run scripts with appropriate administrative privileges
- Test all remediation scripts in non-production environments
- Review Group Policy changes before implementation
- Maintain backups of current configurations

### Performance Considerations
- Limit concurrent jobs based on network capacity
- Consider system load during business hours
- Monitor memory usage for large-scale audits

### Compliance Notes
- Results should be validated against current CIS Benchmark documentation
- Some controls may require manual Group Policy configuration
- Regular re-auditing is recommended (monthly/quarterly)
- Document any approved deviations from CIS recommendations

## 🔍 Troubleshooting

### Common Issues

**"Access Denied" Errors**
- Ensure running as Administrator
- Verify credentials for remote systems
- Check Windows Firewall settings

**"Computer Not Found" Errors**
- Verify network connectivity
- Check DNS resolution
- Confirm computer names are correct

**Missing Report Files**
- Check output path permissions
- Verify disk space availability
- Review error logs for details

### Debug Mode
```powershell
# Enable verbose logging
.\CIS-Audit-Framework.ps1 -Verbose -Debug
```

## 📞 Support

For issues, questions, or contributions:
- Review the generated log files for detailed error information
- Check Windows Event Logs for system-level issues
- Validate PowerShell execution policy settings
- Ensure all prerequisite modules are installed

## 📄 License

This CIS Controls v8 Audit Framework is provided as-is for educational and compliance purposes. Users are responsible for validating configurations against current CIS Benchmark documentation and testing in their specific environments.
