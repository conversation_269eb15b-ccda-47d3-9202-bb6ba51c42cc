# CIS Controls v8 Audit Framework - Usage Examples
# These examples demonstrate various ways to use the CIS audit framework

#Requires -Version 5.1
#Requires -RunAsAdministrator

# Set the script directory (adjust path as needed)
$ScriptPath = "C:\CIS-Audit-Framework"
Set-Location $ScriptPath

Write-Host "CIS Controls v8 Audit Framework - Usage Examples" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Example 1: Basic Local System Audit
Write-Host "`nExample 1: Basic Local System Audit" -ForegroundColor Yellow
Write-Host "Auditing local system with HTML report and remediation guidance"

$example1 = @"
.\CIS-Audit-Framework.ps1 -OutputFormat HTML -IncludeRemediation
"@

Write-Host $example1 -ForegroundColor Cyan
Read-Host "Press Enter to run Example 1 (or Ctrl+C to skip)"

try {
    Invoke-Expression $example1
    Write-Host "Example 1 completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Example 1 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Example 2: Multiple Systems with Credentials
Write-Host "`nExample 2: Multiple Systems Audit with Credentials" -ForegroundColor Yellow
Write-Host "Auditing multiple servers with domain credentials"

$example2 = @"
# Get credentials for remote access
`$credential = Get-Credential -Message "Enter domain admin credentials"

# Audit multiple servers
.\CIS-Audit-Framework.ps1 -ComputerName "Server01","Server02","Server03" -Credential `$credential -OutputFormat All -OutputPath "C:\CISAudits\MultiServer"
"@

Write-Host $example2 -ForegroundColor Cyan
$runExample2 = Read-Host "Run Example 2? (y/n)"

if ($runExample2 -eq 'y') {
    try {
        $credential = Get-Credential -Message "Enter domain admin credentials"
        .\CIS-Audit-Framework.ps1 -ComputerName "localhost" -Credential $credential -OutputFormat All -OutputPath "C:\CISAudits\MultiServer"
        Write-Host "Example 2 completed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Example 2 failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Example 3: Enterprise Audit with Email Notifications
Write-Host "`nExample 3: Enterprise Audit with Email Notifications" -ForegroundColor Yellow
Write-Host "Large-scale audit with consolidated reporting and email notifications"

$example3 = @"
# Configure email settings
`$emailParams = @{
    SMTPServer = "smtp.company.com"
    EmailFrom = "<EMAIL>"
    EmailTo = @("<EMAIL>", "<EMAIL>")
}

# Run enterprise audit
.\CIS-Enterprise-Audit.ps1 -ComputerListFile "examples\servers.txt" -GenerateConsolidatedReport -EmailReport @emailParams -MaxConcurrentJobs 10
"@

Write-Host $example3 -ForegroundColor Cyan
Write-Host "Note: This example requires SMTP server configuration" -ForegroundColor Yellow

# Example 4: Audit with Remediation Script Generation
Write-Host "`nExample 4: Complete Audit and Remediation Workflow" -ForegroundColor Yellow
Write-Host "Full workflow: Audit -> Generate Remediation -> Create Backups"

$example4 = @"
# Step 1: Run initial audit
.\CIS-Audit-Framework.ps1 -OutputFormat JSON -IncludeRemediation -OutputPath "C:\CISAudits\Workflow"

# Step 2: Generate remediation scripts
.\CIS-Remediation-Generator.ps1 -AuditResultsPath "C:\CISAudits\Workflow\CIS-Audit-Report-*.json" -CreateBackup -GenerateGroupPolicyScript -OutputPath "C:\CISAudits\Remediation"

# Step 3: Review and execute remediation (manual step)
Write-Host "Review generated scripts before execution!"
"@

Write-Host $example4 -ForegroundColor Cyan

# Example 5: Scheduled Audit Task
Write-Host "`nExample 5: Creating Scheduled Audit Task" -ForegroundColor Yellow
Write-Host "Setting up automated monthly CIS audits"

$example5 = @"
# Create scheduled task for monthly CIS audits
`$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\CIS-Audit-Framework\CIS-Audit-Framework.ps1 -OutputFormat All -OutputPath C:\CISAudits\Monthly"
`$trigger = New-ScheduledTaskTrigger -Monthly -At "02:00AM" -DaysOfMonth 1
`$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
`$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName "CIS Controls v8 Monthly Audit" -Action `$action -Trigger `$trigger -Principal `$principal -Settings `$settings -Description "Automated monthly CIS Controls v8 compliance audit"
"@

Write-Host $example5 -ForegroundColor Cyan

# Example 6: Custom Control Subset Audit
Write-Host "`nExample 6: Filtering High-Risk Controls Only" -ForegroundColor Yellow
Write-Host "Focus audit on high-risk controls for quick assessment"

$example6 = @"
# This would require modifying the baseline to include only high-risk controls
# For demonstration, we'll show how to filter results post-audit

# Run full audit
.\CIS-Audit-Framework.ps1 -OutputFormat JSON -OutputPath "C:\CISAudits\HighRisk"

# Filter results for high-risk non-compliant controls
`$auditResults = Get-Content "C:\CISAudits\HighRisk\CIS-Audit-Report-*.json" | ConvertFrom-Json
`$highRiskIssues = `$auditResults.Results | Where-Object { `$_.RiskLevel -eq "High" -and `$_.ComplianceStatus -eq "Non-Compliant" }

Write-Host "High-risk non-compliant controls found: `$(`$highRiskIssues.Count)"
`$highRiskIssues | Format-Table ControlID, ControlName, CurrentValue, RecommendedValue
"@

Write-Host $example6 -ForegroundColor Cyan

# Example 7: Compliance Trend Analysis
Write-Host "`nExample 7: Compliance Trend Analysis" -ForegroundColor Yellow
Write-Host "Comparing audit results over time"

$example7 = @"
# Create monthly audit directory structure
`$currentMonth = Get-Date -Format "yyyy-MM"
`$auditPath = "C:\CISAudits\Trends\`$currentMonth"

# Run audit with timestamped results
.\CIS-Audit-Framework.ps1 -OutputFormat All -OutputPath `$auditPath

# Compare with previous month (if exists)
`$previousMonth = (Get-Date).AddMonths(-1).ToString("yyyy-MM")
`$previousPath = "C:\CISAudits\Trends\`$previousMonth"

if (Test-Path `$previousPath) {
    Write-Host "Comparing with previous month's results..."
    # Custom comparison logic would go here
    Write-Host "Trend analysis complete - check reports for details"
}
"@

Write-Host $example7 -ForegroundColor Cyan

# Example 8: Integration with Configuration Management
Write-Host "`nExample 8: Integration with SCCM/Group Policy" -ForegroundColor Yellow
Write-Host "Using audit results to drive configuration management"

$example8 = @"
# Run audit and export non-compliant settings
.\CIS-Audit-Framework.ps1 -OutputFormat CSV -OutputPath "C:\CISAudits\ConfigMgmt"

# Process results for configuration management integration
`$csvResults = Import-Csv "C:\CISAudits\ConfigMgmt\CIS-Audit-Report-*.csv"
`$nonCompliant = `$csvResults | Where-Object { `$_.ComplianceStatus -eq "Non-Compliant" }

# Group by configuration type for targeted remediation
`$registrySettings = `$nonCompliant | Where-Object { `$_.Type -eq "Registry" }
`$serviceSettings = `$nonCompliant | Where-Object { `$_.Type -eq "Service" }
`$policySettings = `$nonCompliant | Where-Object { `$_.Type -eq "SecurityPolicy" }

Write-Host "Registry settings to remediate: `$(`$registrySettings.Count)"
Write-Host "Service settings to remediate: `$(`$serviceSettings.Count)"
Write-Host "Policy settings to remediate: `$(`$policySettings.Count)"
"@

Write-Host $example8 -ForegroundColor Cyan

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "CIS Audit Framework Examples Summary" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "1. Basic Local Audit - Quick compliance check" -ForegroundColor White
Write-Host "2. Multi-Server Audit - Enterprise assessment" -ForegroundColor White
Write-Host "3. Enterprise with Email - Automated reporting" -ForegroundColor White
Write-Host "4. Full Workflow - Audit to remediation" -ForegroundColor White
Write-Host "5. Scheduled Tasks - Automated compliance monitoring" -ForegroundColor White
Write-Host "6. High-Risk Focus - Priority-based assessment" -ForegroundColor White
Write-Host "7. Trend Analysis - Compliance over time" -ForegroundColor White
Write-Host "8. Config Management - Integration with existing tools" -ForegroundColor White

Write-Host "`nFor more information, see README.md" -ForegroundColor Yellow
Write-Host "All scripts should be tested in non-production environments first!" -ForegroundColor Red
