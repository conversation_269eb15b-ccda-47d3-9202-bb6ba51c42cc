# Simplified SQL Server 2022 Linux Connection Test
Write-Host "SQL Server 2022 Linux Connection Test (Simplified)" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Connection parameters
$ServerInstance = "***********,1433"
$Username = "testuser"
$Password = "1234"

try {
    # Step 1: Try to import SqlServer module (assuming it's already installed)
    Write-Host "Step 1: Importing SqlServer module..." -ForegroundColor Yellow
    
    # Try different import methods
    try {
        Import-Module SqlServer -Force -ErrorAction Stop
        Write-Host "SUCCESS: SqlServer module imported" -ForegroundColor Green
    } catch {
        # Try alternative import
        try {
            Import-Module -Name "SqlServer" -Force -Global -ErrorAction Stop
            Write-Host "SUCCESS: SqlServer module imported (alternative method)" -ForegroundColor Green
        } catch {
            # Manual path import as last resort
            $SqlModulePath = Get-ChildItem -Path "$env:ProgramFiles\WindowsPowerShell\Modules\SqlServer" -ErrorAction SilentlyContinue | Sort-Object Name -Descending | Select-Object -First 1
            if ($SqlModulePath) {
                Import-Module $SqlModulePath.FullName -Force -ErrorAction Stop
                Write-Host "SUCCESS: SqlServer module imported from path: $($SqlModulePath.FullName)" -ForegroundColor Green
            } else {
                throw "SqlServer module not found in standard locations"
            }
        }
    }
    
    # Verify Invoke-Sqlcmd is available
    if (-not (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue)) {
        throw "Invoke-Sqlcmd cmdlet not available after module import"
    }
    Write-Host "SUCCESS: Invoke-Sqlcmd cmdlet is available" -ForegroundColor Green

    # Step 2: Test SQL Server connectivity
    Write-Host "Step 2: Testing SQL Server connectivity..." -ForegroundColor Yellow
    
    # Create credential object
    $SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
    $Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)
    
    # Test basic connection
    $TestQuery = "SELECT @@SERVERNAME AS ServerName, @@VERSION AS Version, GETDATE() AS CurrentTime"
    $Result = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $TestQuery -ConnectionTimeout 30 -QueryTimeout 30 -ErrorAction Stop
    
    Write-Host "SUCCESS: SQL Server connection established!" -ForegroundColor Green
    Write-Host "Server Name: $($Result.ServerName)" -ForegroundColor Cyan
    Write-Host "Current Time: $($Result.CurrentTime)" -ForegroundColor Cyan
    Write-Host "SQL Version: $($Result.Version.Split([char]13)[0])" -ForegroundColor Cyan

    # Step 3: Get SQL Server 2022 details
    Write-Host "Step 3: Getting SQL Server 2022 environment details..." -ForegroundColor Yellow
    
    $VersionQuery = @"
SELECT 
    SERVERPROPERTY('ProductVersion') AS ProductVersion,
    SERVERPROPERTY('ProductLevel') AS ProductLevel,
    SERVERPROPERTY('Edition') AS Edition,
    SERVERPROPERTY('EngineEdition') AS EngineEdition,
    CASE 
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) >= 16 THEN 'SQL Server 2022+'
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) = 15 THEN 'SQL Server 2019'
        ELSE 'Older Version'
    END AS VersionCategory,
    SERVERPROPERTY('IsIntegratedSecurityOnly') AS IsIntegratedSecurityOnly
"@

    $VersionResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $VersionQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: SQL Server environment verified" -ForegroundColor Green
    Write-Host "Product Version: $($VersionResult.ProductVersion)" -ForegroundColor Cyan
    Write-Host "Product Level: $($VersionResult.ProductLevel)" -ForegroundColor Cyan
    Write-Host "Edition: $($VersionResult.Edition)" -ForegroundColor Cyan
    Write-Host "Version Category: $($VersionResult.VersionCategory)" -ForegroundColor Cyan
    Write-Host "Authentication Mode: $(if ($VersionResult.IsIntegratedSecurityOnly -eq 1) { 'Windows Only' } else { 'Mixed Mode' })" -ForegroundColor Cyan

    # Step 4: Test user permissions
    Write-Host "Step 4: Checking user permissions for CIS audit..." -ForegroundColor Yellow
    
    $PermissionQuery = @"
SELECT 
    USER_NAME() AS CurrentUser,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW SERVER STATE') AS HasViewServerState,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW ANY DEFINITION') AS HasViewAnyDefinition,
    IS_SRVROLEMEMBER('sysadmin') AS IsSysAdmin
"@

    $PermResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $PermissionQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: User permissions verified" -ForegroundColor Green
    Write-Host "Current User: $($PermResult.CurrentUser)" -ForegroundColor Cyan
    Write-Host "VIEW SERVER STATE: $(if ($PermResult.HasViewServerState) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($PermResult.HasViewServerState) { "Green" } else { "Red" })
    Write-Host "VIEW ANY DEFINITION: $(if ($PermResult.HasViewAnyDefinition) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($PermResult.HasViewAnyDefinition) { "Green" } else { "Red" })
    Write-Host "SysAdmin Role: $(if ($PermResult.IsSysAdmin) { 'YES' } else { 'NO' })" -ForegroundColor Cyan

    # Step 5: Quick CIS control test
    Write-Host "Step 5: Testing sample CIS control..." -ForegroundColor Yellow
    
    $CISQuery = @"
SELECT 
    'CIS-4.1' AS ControlID,
    'Authentication Mode' AS ControlName,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Windows Authentication Only'
        ELSE 'Mixed Mode Authentication'
    END AS CurrentValue,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus
"@

    $CISResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Credential $Credential -Query $CISQuery -ErrorAction Stop
    
    Write-Host "SUCCESS: Sample CIS control tested" -ForegroundColor Green
    Write-Host "Control: $($CISResult.ControlName)" -ForegroundColor Cyan
    Write-Host "Current Value: $($CISResult.CurrentValue)" -ForegroundColor Cyan
    Write-Host "Compliance Status: $($CISResult.ComplianceStatus)" -ForegroundColor $(if ($CISResult.ComplianceStatus -eq "Compliant") { "Green" } else { "Yellow" })

    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "CONNECTION TEST SUCCESSFUL!" -ForegroundColor Green
    Write-Host "SQL Server 2022 Linux is ready for CIS audit!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "CONNECTION TEST FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    Write-Host ""
    Write-Host "Debug Information:" -ForegroundColor Yellow
    Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host "SqlServer Module Available: $(if (Get-Module -ListAvailable -Name SqlServer) { 'YES' } else { 'NO' })" -ForegroundColor White
    Write-Host "Invoke-Sqlcmd Available: $(if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) { 'YES' } else { 'NO' })" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Yellow
    Write-Host "1. If module import failed, try: Import-Module SqlServer -Force" -ForegroundColor White
    Write-Host "2. If connection failed, verify SQL Server container is running" -ForegroundColor White
    Write-Host "3. Check credentials with: docker exec <container> /opt/mssql-tools/bin/sqlcmd -S localhost -U testuser -P 1234" -ForegroundColor White
}
