# PAM (Privileged Access Management) - 5 Critical Controls Audit Framework

## Overview
This document outlines the 5 most critical Privileged Access Management (PAM) controls for comprehensive security auditing. Each control includes category classification, best practices, audit questions, required evidence, and detailed auditing steps.

---

## Control 1: Privileged Account Inventory & Management

| **Attribute** | **Details** |
|---------------|-------------|
| **Category** | Identity and Access Management (IAM) |
| **Best Practice** | Maintain a complete, accurate, and up-to-date inventory of all privileged accounts across the organization. Implement proper lifecycle management for privileged accounts from creation to decommissioning. |
| **Audit Question** | Are all privileged accounts properly inventoried, documented, and managed throughout their lifecycle? |
| **Evidence Required** | • Complete privileged account inventory report<br>• Account creation/modification logs<br>• Account ownership documentation<br>• Privileged group membership reports<br>• Service account inventory<br>• Account lifecycle procedures |
| **Auditing Steps** | 1. **Extract Active Directory privileged groups:**<br>&nbsp;&nbsp;&nbsp;`Get-ADGroupMember "Domain Admins" \| Export-CSV`<br>&nbsp;&nbsp;&nbsp;`Get-ADGroupMember "Enterprise Admins" \| Export-CSV`<br>&nbsp;&nbsp;&nbsp;`Get-ADGroupMember "Schema Admins" \| Export-CSV`<br>2. **Enumerate local administrators on servers:**<br>&nbsp;&nbsp;&nbsp;`Get-LocalGroupMember "Administrators" \| Export-CSV`<br>3. **Identify service accounts:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Filter {ServicePrincipalName -like "*"}`<br>4. **Review account creation dates and last logon:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Properties LastLogonDate, Created`<br>5. **Cross-reference with HR systems for account ownership**<br>6. **Identify orphaned/dormant accounts (>90 days inactive)**<br>7. **Validate account naming conventions compliance**<br>8. **Review account lifecycle documentation** |

---

## Control 2: Privileged Access Controls & Authentication

| **Attribute** | **Details** |
|---------------|-------------|
| **Category** | Authentication and Authorization |
| **Best Practice** | Implement strong authentication mechanisms for all privileged access, including multi-factor authentication (MFA), smart cards, or biometric authentication. Establish robust break-glass procedures for emergency access. |
| **Audit Question** | Are strong authentication controls, including MFA, properly implemented and enforced for all privileged access? |
| **Evidence Required** | • MFA configuration reports<br>• Authentication policy documentation<br>• Smart card/certificate deployment status<br>• Break-glass procedure documentation<br>• Authentication logs and reports<br>• Conditional access policies |
| **Auditing Steps** | 1. **Review MFA enforcement policies:**<br>&nbsp;&nbsp;&nbsp;`Get-MsolUser \| Where {$_.StrongAuthenticationRequirements}`<br>2. **Check Azure AD Conditional Access policies:**<br>&nbsp;&nbsp;&nbsp;`Get-AzureADMSConditionalAccessPolicy`<br>3. **Validate smart card authentication settings:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Filter * -Properties SmartcardLogonRequired`<br>4. **Review Group Policy authentication settings:**<br>&nbsp;&nbsp;&nbsp;`gpresult /r /scope computer`<br>5. **Examine authentication logs for privileged accounts:**<br>&nbsp;&nbsp;&nbsp;`Get-WinEvent -LogName Security -FilterXPath "*[System[EventID=4624]]"`<br>6. **Test break-glass account accessibility and procedures**<br>7. **Verify certificate-based authentication configuration**<br>8. **Review privileged access workstation (PAW) implementations** |

---

## Control 3: Privileged Session Management & Monitoring

| **Attribute** | **Details** |
|---------------|-------------|
| **Category** | Session Management and Monitoring |
| **Best Practice** | Implement comprehensive session recording, real-time monitoring, and automated alerting for all privileged sessions. Establish session timeout policies and approval workflows for high-risk privileged activities. |
| **Audit Question** | Are all privileged sessions properly recorded, monitored, and controlled with appropriate timeout and approval mechanisms? |
| **Evidence Required** | • Session recording configurations<br>• Privileged session logs and recordings<br>• Real-time monitoring alerts<br>• Session timeout policy settings<br>• Approval workflow documentation<br>• Session analytics reports |
| **Auditing Steps** | 1. **Review session recording configurations:**<br>&nbsp;&nbsp;&nbsp;`Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\SessionRecording"`<br>2. **Check PowerShell logging settings:**<br>&nbsp;&nbsp;&nbsp;`Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ScriptBlockLogging"`<br>3. **Examine RDP session configurations:**<br>&nbsp;&nbsp;&nbsp;`Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server"`<br>4. **Review privileged session timeout settings:**<br>&nbsp;&nbsp;&nbsp;`Get-ADDefaultDomainPasswordPolicy`<br>&nbsp;&nbsp;&nbsp;`Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"`<br>5. **Analyze privileged session logs:**<br>&nbsp;&nbsp;&nbsp;`Get-WinEvent -LogName "Microsoft-Windows-TerminalServices-LocalSessionManager/Operational"`<br>6. **Test session monitoring and alerting mechanisms**<br>7. **Verify session approval workflows for critical operations**<br>8. **Review session analytics and reporting capabilities** |

---

## Control 4: Least Privilege & Just-in-Time (JIT) Access

| **Attribute** | **Details** |
|---------------|-------------|
| **Category** | Access Control and Privilege Management |
| **Best Practice** | Implement least privilege principles ensuring users receive only the minimum access required for their role. Deploy Just-in-Time (JIT) access for temporary privilege elevation with time-limited grants and regular access reviews. |
| **Audit Question** | Are least privilege principles properly implemented with JIT access controls and regular privilege reviews? |
| **Evidence Required** | • User privilege assignment reports<br>• JIT access configuration and logs<br>• Access review documentation<br>• Role-based access control (RBAC) mappings<br>• Privilege escalation logs<br>• Access recertification records |
| **Auditing Steps** | 1. **Analyze user privilege assignments:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Filter * -Properties MemberOf \| Select Name, MemberOf`<br>2. **Review Azure AD Privileged Identity Management (PIM):**<br>&nbsp;&nbsp;&nbsp;`Get-AzureADMSPrivilegedRoleAssignment`<br>3. **Check JIT access configurations:**<br>&nbsp;&nbsp;&nbsp;`Get-AzureADMSPrivilegedRoleDefinition`<br>4. **Examine privilege escalation events:**<br>&nbsp;&nbsp;&nbsp;`Get-WinEvent -LogName Security -FilterXPath "*[System[EventID=4672]]"`<br>5. **Review access request and approval logs:**<br>&nbsp;&nbsp;&nbsp;`Get-AzureADAuditDirectoryLogs -Filter "category eq 'RoleManagement'"`<br>6. **Validate time-limited access grants:**<br>&nbsp;&nbsp;&nbsp;`Get-AzureADMSPrivilegedRoleAssignment -Filter "assignmentState eq 'Eligible'"`<br>7. **Assess role-based access control implementations**<br>8. **Review access recertification processes and compliance**<br>9. **Test privilege escalation request workflows** |

---

## Control 5: Privileged Credential Management

| **Attribute** | **Details** |
|---------------|-------------|
| **Category** | Credential and Secret Management |
| **Best Practice** | Implement centralized credential vaulting with automatic password rotation, secure storage of API keys and certificates, and proper management of shared accounts. Ensure all privileged credentials are protected and regularly rotated. |
| **Audit Question** | Are all privileged credentials properly stored, managed, and rotated through centralized vaulting solutions? |
| **Evidence Required** | • Credential vault configuration reports<br>• Password rotation policies and logs<br>• API key and certificate inventory<br>• Shared account management documentation<br>• Credential access logs<br>• Vault security configurations |
| **Auditing Steps** | 1. **Review password vault implementations:**<br>&nbsp;&nbsp;&nbsp;`Get-ItemProperty "HKLM:\SOFTWARE\CyberArk\*" -ErrorAction SilentlyContinue`<br>2. **Check password rotation policies:**<br>&nbsp;&nbsp;&nbsp;`Get-ADDefaultDomainPasswordPolicy`<br>&nbsp;&nbsp;&nbsp;`Get-ADFineGrainedPasswordPolicy -Filter *`<br>3. **Examine service account password ages:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Filter {ServicePrincipalName -like "*"} -Properties PasswordLastSet`<br>4. **Review API key and certificate management:**<br>&nbsp;&nbsp;&nbsp;`Get-ChildItem Cert:\LocalMachine\My \| Where {$_.Subject -like "*admin*"}`<br>5. **Analyze credential access logs:**<br>&nbsp;&nbsp;&nbsp;`Get-WinEvent -LogName Security -FilterXPath "*[System[EventID=4648]]"`<br>6. **Validate shared account password complexity:**<br>&nbsp;&nbsp;&nbsp;`Get-ADUser -Filter {SamAccountName -like "*svc*"} -Properties PasswordNeverExpires`<br>7. **Test credential vault accessibility and security**<br>8. **Review emergency credential access procedures**<br>9. **Verify credential encryption and storage security**<br>10. **Assess integration with applications and services** |

---

## Audit Execution Summary

### Prerequisites
- Administrative access to domain controllers and target systems
- Access to Azure AD/Office 365 admin portal (if applicable)
- Privileged access management tools (CyberArk, Azure PIM, etc.)
- Security event log access permissions

### Recommended Audit Frequency
- **Monthly**: Privileged account inventory and access reviews
- **Quarterly**: Comprehensive PAM control assessment
- **Annually**: Full PAM program evaluation and policy review

### Key Risk Indicators
- Privileged accounts without MFA
- Dormant privileged accounts (>90 days inactive)
- Shared accounts with static passwords
- Privileged sessions without recording/monitoring
- Excessive permanent privileged access grants

### Compliance Frameworks
These controls align with:
- **NIST Cybersecurity Framework**: PR.AC (Access Control)
- **ISO 27001**: A.9 (Access Control Management)
- **CIS Controls v8**: Control 5 (Account Management), Control 6 (Access Control Management)
- **SOX**: IT General Controls (ITGC)
- **PCI DSS**: Requirement 7 (Restrict Access), Requirement 8 (Identify and Authenticate Access)

---

*This framework provides a comprehensive approach to auditing critical PAM controls. Each control should be evaluated in the context of the organization's specific environment, risk tolerance, and compliance requirements.*
