#Requires -Version 5.1
<#
================================================================================
Microsoft Exchange Server Security Controls Audit Script
================================================================================
Description: Comprehensive audit script for Exchange Server security configuration compliance
Version: 1.0 (Exchange 2016/2019/Online Compatible)
Created: August 17, 2025

INSTRUCTIONS FOR ADMIN:
1. Copy this file to the Exchange Server or management workstation
2. Run PowerShell as Administrator and execute:
   
   .\Exchange-Security-Audit-Complete.ps1 -OutputPath "C:\Temp\Exchange-Audit-Results.json"
   
   OR with default output location:
   .\Exchange-Security-Audit-Complete.ps1

3. Send the generated Exchange-Audit-Results.json file back for analysis

CRITICAL: This script is 100% READ-ONLY and safe for production environments
All operations use only Get-* cmdlets and read-only Exchange PowerShell commands
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\Exchange-Audit-Results.json",
    
    [Parameter(Mandatory = $false)]
    [switch]$IncludeMailboxDetails = $false,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxMailboxSample = 100
)

# Initialize audit session
$AuditStartTime = Get-Date
$AuditID = [System.Guid]::NewGuid()
$AuditResults = @{}
$TotalControls = 11

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Microsoft Exchange Server Security Controls Audit" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
Write-Host "Started: $($AuditStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "Output: $OutputPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

try {
    # =============================================================================
    # AUDIT METADATA AND ENVIRONMENT INFORMATION
    # =============================================================================
    
    Write-Host "Step 1/12: Collecting Exchange environment metadata..." -ForegroundColor Yellow
    
    try {
        $ExchangeServers = Get-ExchangeServer -ErrorAction Stop
        $OrganizationConfig = Get-OrganizationConfig -ErrorAction Stop
        $ExchangeVersion = $ExchangeServers | Select-Object -First 1 | ForEach-Object { $_.AdminDisplayVersion }
        
        $AuditMetadata = @{
            AuditID = $AuditID
            AuditStartTime = $AuditStartTime
            ExchangeVersion = $ExchangeVersion.ToString()
            OrganizationName = $OrganizationConfig.Name
            ExchangeServers = $ExchangeServers | Select-Object Name, ServerRole, AdminDisplayVersion, Site
            TotalServers = $ExchangeServers.Count
            ComplianceFramework = "Exchange Security Controls"
            AssessmentScope = "11 Critical Security Controls"
            AuditUser = $env:USERNAME
            ComputerName = $env:COMPUTERNAME
        }
        
        $AuditResults.Add("AuditMetadata", $AuditMetadata)
        Write-Host "✓ Environment metadata collected successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to collect environment metadata: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AuditMetadata", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 1: AUTHENTICATION AND ACCESS CONTROL
    # =============================================================================
    
    Write-Host "Step 2/12: Assessing authentication and access control..." -ForegroundColor Yellow
    
    try {
        $AuthPolicies = Get-AuthenticationPolicy -ErrorAction SilentlyContinue
        $OwaMailboxPolicies = Get-OwaMailboxPolicy -ErrorAction Stop
        $ActiveSyncPolicies = Get-ActiveSyncMailboxPolicy -ErrorAction Stop
        
        $AuthenticationControl = @{
            ControlID = "EXC-1.1"
            ControlName = "Authentication and Access Control"
            RiskLevel = "High"
            CVSSScore = 7.8
            AuthenticationPolicies = $AuthPolicies | Select-Object Name, IsDefault, AllowBasicAuthActiveSync, AllowBasicAuthImap, AllowBasicAuthPop, AllowBasicAuthWebServices
            OwaMailboxPolicies = $OwaMailboxPolicies | Select-Object Name, IsDefault, BasicAuthenticationEnabled, FormsAuthenticationEnabled
            ActiveSyncPolicies = $ActiveSyncPolicies | Select-Object Name, IsDefault, PasswordEnabled, AlphanumericPasswordRequired, PasswordRecoveryEnabled
            CurrentValue = if ($AuthPolicies) { "Authentication policies configured" } else { "No authentication policies found" }
            BaselineValue = "Modern authentication enforced, basic authentication disabled"
            ComplianceStatus = if ($AuthPolicies -and ($AuthPolicies | Where-Object { -not $_.AllowBasicAuthActiveSync })) { "Compliant" } else { "Review Required" }
            Finding = if ($AuthPolicies) { "Authentication policies present - verify basic auth is disabled" } else { "No authentication policies configured - security risk" }
            Recommendation = "Ensure modern authentication is enforced and basic authentication is disabled for all protocols"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("EXC_1_1_AuthenticationControl", $AuthenticationControl)
        Write-Host "✓ Authentication and access control assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess authentication control: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_1_1_AuthenticationControl", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 2: TRANSPORT SECURITY AND ENCRYPTION
    # =============================================================================
    
    Write-Host "Step 3/12: Assessing transport security and encryption..." -ForegroundColor Yellow
    
    try {
        $ReceiveConnectors = Get-ReceiveConnector -ErrorAction Stop
        $SendConnectors = Get-SendConnector -ErrorAction Stop
        $TransportConfig = Get-TransportConfig -ErrorAction Stop
        
        $TransportSecurity = @{
            ControlID = "EXC-2.1"
            ControlName = "Transport Security and Encryption"
            RiskLevel = "Critical"
            CVSSScore = 9.1
            ReceiveConnectors = $ReceiveConnectors | Select-Object Identity, AuthMechanism, PermissionGroups, RequireTLS, EnableAuthGSSAPI
            SendConnectors = $SendConnectors | Select-Object Identity, RequireTLS, TlsAuthLevel, SmartHosts
            TransportConfig = $TransportConfig | Select-Object TLSReceiveDomainSecureList, TLSSendDomainSecureList, InternalSMTPServers
            TLSEnabledConnectors = ($ReceiveConnectors | Where-Object { $_.RequireTLS -eq $true }).Count
            TotalConnectors = $ReceiveConnectors.Count
            CurrentValue = "TLS enabled on $($TLSEnabledConnectors) of $($ReceiveConnectors.Count) receive connectors"
            BaselineValue = "TLS required on all connectors"
            ComplianceStatus = if ($TLSEnabledConnectors -eq $ReceiveConnectors.Count) { "Compliant" } else { "Non-Compliant" }
            ComplianceScore = if ($ReceiveConnectors.Count -gt 0) { [math]::Round(($TLSEnabledConnectors / $ReceiveConnectors.Count) * 100, 2) } else { 0 }
            Finding = if ($TLSEnabledConnectors -eq $ReceiveConnectors.Count) { "All receive connectors properly configured with TLS" } else { "Some connectors allow unencrypted connections - security risk" }
            Recommendation = "Enable RequireTLS on all receive connectors and configure appropriate TLS settings on send connectors"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("EXC_2_1_TransportSecurity", $TransportSecurity)
        Write-Host "✓ Transport security and encryption assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess transport security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_2_1_TransportSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 3: ADMINISTRATIVE ROLE ASSIGNMENTS
    # =============================================================================
    
    Write-Host "Step 4/12: Assessing administrative role assignments..." -ForegroundColor Yellow
    
    try {
        $RoleGroups = Get-RoleGroup -ErrorAction Stop
        $ManagementRoles = Get-ManagementRole -ErrorAction Stop
        $RoleAssignments = Get-ManagementRoleAssignment -ErrorAction Stop
        
        $PrivilegedRoles = $RoleGroups | Where-Object { 
            $_.Name -in @("Organization Management", "Exchange Administrators", "Domain Admins", "Enterprise Admins") 
        }
        
        $AdminRoleControl = @{
            ControlID = "EXC-3.1"
            ControlName = "Administrative Role Assignments"
            RiskLevel = "High"
            CVSSScore = 8.5
            TotalRoleGroups = $RoleGroups.Count
            TotalManagementRoles = $ManagementRoles.Count
            TotalRoleAssignments = $RoleAssignments.Count
            PrivilegedRoleGroups = $PrivilegedRoles | Select-Object Name, Members, ManagedBy, WhenCreated
            PrivilegedMemberCount = ($PrivilegedRoles | ForEach-Object { $_.Members }).Count
            OrganizationManagement = $RoleGroups | Where-Object { $_.Name -eq "Organization Management" } | Select-Object Name, Members
            CurrentValue = "Organization Management has $($OrganizationManagement.Members.Count) members"
            BaselineValue = "Minimal privileged role assignments with regular review"
            ComplianceStatus = if ($OrganizationManagement.Members.Count -le 5) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($OrganizationManagement.Members.Count -le 3) { 100 } elseif ($OrganizationManagement.Members.Count -le 5) { 75 } else { 25 }
            Finding = if ($OrganizationManagement.Members.Count -le 5) { "Privileged role membership appears reasonable" } else { "High number of privileged users - review required" }
            Recommendation = "Regularly review privileged role assignments and implement principle of least privilege"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("EXC_3_1_AdminRoleControl", $AdminRoleControl)
        Write-Host "✓ Administrative role assignments assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess administrative roles: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_3_1_AdminRoleControl", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 4: AUDIT LOGGING CONFIGURATION
    # =============================================================================
    
    Write-Host "Step 5/12: Assessing audit logging configuration..." -ForegroundColor Yellow
    
    try {
        $AdminAuditLogConfig = Get-AdminAuditLogConfig -ErrorAction Stop
        $MailboxAuditBypassAssociation = Get-MailboxAuditBypassAssociation -ErrorAction SilentlyContinue
        
        $AuditLoggingControl = @{
            ControlID = "EXC-4.1"
            ControlName = "Audit Logging Configuration"
            RiskLevel = "Medium"
            CVSSScore = 6.2
            AdminAuditLogEnabled = $AdminAuditLogConfig.AdminAuditLogEnabled
            AdminAuditLogCmdlets = $AdminAuditLogConfig.AdminAuditLogCmdlets
            AdminAuditLogParameters = $AdminAuditLogConfig.AdminAuditLogParameters
            LogLevel = $AdminAuditLogConfig.LogLevel
            AuditBypassUsers = $MailboxAuditBypassAssociation | Select-Object Name, AuditBypassEnabled
            AuditBypassCount = ($MailboxAuditBypassAssociation | Where-Object { $_.AuditBypassEnabled }).Count
            CurrentValue = if ($AdminAuditLogConfig.AdminAuditLogEnabled) { "Admin audit logging enabled" } else { "Admin audit logging disabled" }
            BaselineValue = "Comprehensive audit logging enabled"
            ComplianceStatus = if ($AdminAuditLogConfig.AdminAuditLogEnabled) { "Compliant" } else { "Non-Compliant" }
            ComplianceScore = if ($AdminAuditLogConfig.AdminAuditLogEnabled) { 100 } else { 0 }
            Finding = if ($AdminAuditLogConfig.AdminAuditLogEnabled) { "Admin audit logging properly enabled" } else { "Admin audit logging disabled - compliance risk" }
            Recommendation = if (-not $AdminAuditLogConfig.AdminAuditLogEnabled) { "Enable admin audit logging: Set-AdminAuditLogConfig -AdminAuditLogEnabled $true" } else { "Audit logging properly configured" }
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("EXC_4_1_AuditLogging", $AuditLoggingControl)
        Write-Host "✓ Audit logging configuration assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess audit logging: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_4_1_AuditLogging", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 5: CLIENT ACCESS POLICIES
    # =============================================================================
    
    Write-Host "Step 6/12: Assessing client access policies..." -ForegroundColor Yellow
    
    try {
        $CASMailboxPolicies = Get-CASMailboxPlan -ErrorAction SilentlyContinue
        if (-not $CASMailboxPolicies) {
            $CASMailboxPolicies = Get-CASMailbox -ResultSize 10 -ErrorAction Stop
        }
        
        $ClientAccessPolicies = Get-ClientAccessRule -ErrorAction SilentlyContinue
        
        $ClientAccessControl = @{
            ControlID = "EXC-5.1"
            ControlName = "Client Access Policies"
            RiskLevel = "Medium"
            CVSSScore = 5.8
            CASMailboxPolicies = $CASMailboxPolicies | Select-Object Identity, ActiveSyncEnabled, OWAEnabled, PopEnabled, ImapEnabled, MAPIEnabled
            ClientAccessRules = $ClientAccessPolicies | Select-Object Name, Enabled, Priority, Action, AnyOfClientIPAddressesOrRanges
            ActiveSyncDisabledCount = ($CASMailboxPolicies | Where-Object { -not $_.ActiveSyncEnabled }).Count
            TotalCASPolicies = $CASMailboxPolicies.Count
            CurrentValue = "ActiveSync disabled for $($ActiveSyncDisabledCount) of $($TotalCASPolicies) policies"
            BaselineValue = "Restrictive client access policies with protocol controls"
            ComplianceStatus = if ($ClientAccessPolicies) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($ClientAccessPolicies) { 75 } else { 25 }
            Finding = if ($ClientAccessPolicies) { "Client access rules configured" } else { "No client access rules found - consider implementing restrictions" }
            Recommendation = "Implement client access rules to restrict access based on location, protocol, and device compliance"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("EXC_5_1_ClientAccess", $ClientAccessControl)
        Write-Host "✓ Client access policies assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess client access policies: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_5_1_ClientAccess", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 6: MESSAGE HYGIENE AND FILTERING
    # =============================================================================

    Write-Host "Step 7/12: Assessing message hygiene and filtering..." -ForegroundColor Yellow

    try {
        $AntiMalwareConfig = Get-MalwareFilterPolicy -ErrorAction SilentlyContinue
        $AntiSpamConfig = Get-HostedContentFilterPolicy -ErrorAction SilentlyContinue
        $SafeAttachmentPolicies = Get-SafeAttachmentPolicy -ErrorAction SilentlyContinue
        $SafeLinksPolicies = Get-SafeLinksPolicy -ErrorAction SilentlyContinue

        $MessageHygieneControl = @{
            ControlID = "EXC-6.1"
            ControlName = "Message Hygiene and Filtering"
            RiskLevel = "High"
            CVSSScore = 7.5
            MalwareFilterPolicies = $AntiMalwareConfig | Select-Object Name, IsDefault, Action, EnableFileFilter
            ContentFilterPolicies = $AntiSpamConfig | Select-Object Name, IsDefault, HighConfidenceSpamAction, SpamAction
            SafeAttachmentPolicies = $SafeAttachmentPolicies | Select-Object Name, IsDefault, Action, Enable
            SafeLinksPolicies = $SafeLinksPolicies | Select-Object Name, IsDefault, IsEnabled, ScanUrls
            MalwarePoliciesCount = if ($AntiMalwareConfig) { $AntiMalwareConfig.Count } else { 0 }
            ContentFilterCount = if ($AntiSpamConfig) { $AntiSpamConfig.Count } else { 0 }
            CurrentValue = "Malware policies: $($MalwarePoliciesCount), Content filter policies: $($ContentFilterCount)"
            BaselineValue = "Comprehensive anti-malware and anti-spam protection enabled"
            ComplianceStatus = if ($AntiMalwareConfig -and $AntiSpamConfig) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($AntiMalwareConfig -and $AntiSpamConfig) { 100 } else { 50 }
            Finding = if ($AntiMalwareConfig -and $AntiSpamConfig) { "Message hygiene policies configured" } else { "Limited message protection - review configuration" }
            Recommendation = "Ensure comprehensive anti-malware, anti-spam, and Advanced Threat Protection policies are configured"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_6_1_MessageHygiene", $MessageHygieneControl)
        Write-Host "✓ Message hygiene and filtering assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess message hygiene: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_6_1_MessageHygiene", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 7: DATABASE AND BACKUP SECURITY
    # =============================================================================

    Write-Host "Step 8/12: Assessing database and backup security..." -ForegroundColor Yellow

    try {
        $MailboxDatabases = Get-MailboxDatabase -ErrorAction Stop
        $DatabaseAvailabilityGroups = Get-DatabaseAvailabilityGroup -ErrorAction SilentlyContinue

        $DatabaseSecurityControl = @{
            ControlID = "EXC-7.1"
            ControlName = "Database and Backup Security"
            RiskLevel = "High"
            CVSSScore = 7.8
            MailboxDatabases = $MailboxDatabases | Select-Object Name, Server, CircularLoggingEnabled, BackgroundDatabaseMaintenance, DeletedItemRetention
            DatabaseAvailabilityGroups = $DatabaseAvailabilityGroups | Select-Object Name, Servers, WitnessServer, AlternateWitnessServer
            TotalDatabases = $MailboxDatabases.Count
            CircularLoggingEnabled = ($MailboxDatabases | Where-Object { $_.CircularLoggingEnabled }).Count
            DAGConfigured = if ($DatabaseAvailabilityGroups) { $true } else { $false }
            CurrentValue = "Circular logging enabled on $($CircularLoggingEnabled) of $($MailboxDatabases.Count) databases"
            BaselineValue = "Circular logging disabled, proper backup strategy implemented"
            ComplianceStatus = if ($CircularLoggingEnabled -eq 0) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($CircularLoggingEnabled -eq 0) { 100 } else { 25 }
            Finding = if ($CircularLoggingEnabled -eq 0) { "Circular logging properly disabled for backup integrity" } else { "Circular logging enabled - may impact backup and recovery" }
            Recommendation = "Disable circular logging and implement proper backup strategy with regular testing"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_7_1_DatabaseSecurity", $DatabaseSecurityControl)
        Write-Host "✓ Database and backup security assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess database security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_7_1_DatabaseSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 8: MAILBOX PERMISSIONS AND DELEGATION
    # =============================================================================

    Write-Host "Step 9/12: Assessing mailbox permissions and delegation..." -ForegroundColor Yellow

    try {
        # Sample mailboxes for permission analysis (limit for performance)
        $SampleMailboxes = Get-Mailbox -ResultSize $MaxMailboxSample -ErrorAction Stop
        $MailboxPermissions = @()

        foreach ($Mailbox in $SampleMailboxes) {
            try {
                $Permissions = Get-MailboxPermission -Identity $Mailbox.Identity -ErrorAction SilentlyContinue |
                    Where-Object { $_.User -notlike "NT AUTHORITY\*" -and $_.User -notlike "S-1-*" }
                if ($Permissions) {
                    $MailboxPermissions += $Permissions
                }
            }
            catch {
                # Continue with next mailbox if permission check fails
                continue
            }
        }

        $MailboxPermissionControl = @{
            ControlID = "EXC-8.1"
            ControlName = "Mailbox Permissions and Delegation"
            RiskLevel = "Medium"
            CVSSScore = 6.5
            SampleSize = $SampleMailboxes.Count
            TotalPermissions = $MailboxPermissions.Count
            FullAccessPermissions = ($MailboxPermissions | Where-Object { $_.AccessRights -contains "FullAccess" }).Count
            SendAsPermissions = ($MailboxPermissions | Where-Object { $_.AccessRights -contains "SendAs" }).Count
            PermissionsSample = $MailboxPermissions | Select-Object Identity, User, AccessRights -First 20
            CurrentValue = "Full Access: $($FullAccessPermissions), Send As: $($SendAsPermissions) permissions found"
            BaselineValue = "Minimal mailbox delegations with regular review"
            ComplianceStatus = if ($FullAccessPermissions -le ($SampleMailboxes.Count * 0.1)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($FullAccessPermissions -eq 0) { 100 } elseif ($FullAccessPermissions -le ($SampleMailboxes.Count * 0.05)) { 75 } else { 50 }
            Finding = if ($FullAccessPermissions -le ($SampleMailboxes.Count * 0.1)) { "Mailbox delegation appears reasonable" } else { "High number of mailbox delegations - review required" }
            Recommendation = "Regularly review mailbox permissions and remove unnecessary delegations"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_8_1_MailboxPermissions", $MailboxPermissionControl)
        Write-Host "✓ Mailbox permissions and delegation assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess mailbox permissions: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_8_1_MailboxPermissions", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 9: VIRTUAL DIRECTORY SECURITY
    # =============================================================================

    Write-Host "Step 10/12: Assessing virtual directory security..." -ForegroundColor Yellow

    try {
        $OwaVirtualDirectories = Get-OwaVirtualDirectory -ErrorAction Stop
        $EcpVirtualDirectories = Get-EcpVirtualDirectory -ErrorAction Stop
        $EwsVirtualDirectories = Get-WebServicesVirtualDirectory -ErrorAction Stop
        $ActiveSyncVirtualDirectories = Get-ActiveSyncVirtualDirectory -ErrorAction Stop

        $VirtualDirectoryControl = @{
            ControlID = "EXC-9.1"
            ControlName = "Virtual Directory Security"
            RiskLevel = "Medium"
            CVSSScore = 6.8
            OwaVirtualDirectories = $OwaVirtualDirectories | Select-Object Identity, BasicAuthentication, WindowsAuthentication, FormsAuthentication, ExternalUrl, InternalUrl
            EcpVirtualDirectories = $EcpVirtualDirectories | Select-Object Identity, BasicAuthentication, WindowsAuthentication, ExternalUrl, InternalUrl
            EwsVirtualDirectories = $EwsVirtualDirectories | Select-Object Identity, BasicAuthentication, WindowsAuthentication, ExternalUrl, InternalUrl
            ActiveSyncVirtualDirectories = $ActiveSyncVirtualDirectories | Select-Object Identity, BasicAuthEnabled, WindowsAuthEnabled, ExternalUrl, InternalUrl
            BasicAuthEnabledOwa = ($OwaVirtualDirectories | Where-Object { $_.BasicAuthentication }).Count
            BasicAuthEnabledEws = ($EwsVirtualDirectories | Where-Object { $_.BasicAuthentication }).Count
            TotalOwaDirectories = $OwaVirtualDirectories.Count
            CurrentValue = "Basic auth enabled on $($BasicAuthEnabledOwa) OWA and $($BasicAuthEnabledEws) EWS virtual directories"
            BaselineValue = "Basic authentication disabled, secure authentication methods only"
            ComplianceStatus = if ($BasicAuthEnabledOwa -eq 0 -and $BasicAuthEnabledEws -eq 0) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($BasicAuthEnabledOwa -eq 0 -and $BasicAuthEnabledEws -eq 0) { 100 } else { 25 }
            Finding = if ($BasicAuthEnabledOwa -eq 0 -and $BasicAuthEnabledEws -eq 0) { "Basic authentication properly disabled" } else { "Basic authentication enabled - security risk" }
            Recommendation = "Disable basic authentication on all virtual directories and use modern authentication"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_9_1_VirtualDirectory", $VirtualDirectoryControl)
        Write-Host "✓ Virtual directory security assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess virtual directory security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_9_1_VirtualDirectory", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 10: RETENTION AND COMPLIANCE POLICIES
    # =============================================================================

    Write-Host "Step 11/12: Assessing retention and compliance policies..." -ForegroundColor Yellow

    try {
        $RetentionPolicies = Get-RetentionPolicy -ErrorAction SilentlyContinue
        $RetentionPolicyTags = Get-RetentionPolicyTag -ErrorAction SilentlyContinue
        $DlpPolicies = Get-DlpPolicy -ErrorAction SilentlyContinue
        $CompliancePolicies = Get-DeviceCompliancePolicy -ErrorAction SilentlyContinue

        $ComplianceControl = @{
            ControlID = "EXC-10.1"
            ControlName = "Retention and Compliance Policies"
            RiskLevel = "Medium"
            CVSSScore = 5.5
            RetentionPolicies = $RetentionPolicies | Select-Object Name, IsDefault, RetentionPolicyTagLinks
            RetentionPolicyTags = $RetentionPolicyTags | Select-Object Name, Type, RetentionEnabled, AgeLimitForRetention
            DlpPolicies = $DlpPolicies | Select-Object Name, State, Mode, ExchangeLocation
            CompliancePolicies = $CompliancePolicies | Select-Object Name, Enabled, CreationDate
            RetentionPolicyCount = if ($RetentionPolicies) { $RetentionPolicies.Count } else { 0 }
            DlpPolicyCount = if ($DlpPolicies) { $DlpPolicies.Count } else { 0 }
            CurrentValue = "Retention policies: $($RetentionPolicyCount), DLP policies: $($DlpPolicyCount)"
            BaselineValue = "Comprehensive retention and DLP policies implemented"
            ComplianceStatus = if ($RetentionPolicies -and $DlpPolicies) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($RetentionPolicies -and $DlpPolicies) { 100 } elseif ($RetentionPolicies -or $DlpPolicies) { 50 } else { 0 }
            Finding = if ($RetentionPolicies -and $DlpPolicies) { "Retention and DLP policies configured" } else { "Limited compliance policies - review requirements" }
            Recommendation = "Implement comprehensive retention policies and DLP rules based on organizational requirements"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_10_1_Compliance", $ComplianceControl)
        Write-Host "✓ Retention and compliance policies assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess compliance policies: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_10_1_Compliance", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 11: ORGANIZATION CONFIGURATION SECURITY
    # =============================================================================

    Write-Host "Step 12/12: Assessing organization configuration security..." -ForegroundColor Yellow

    try {
        $OrgConfig = Get-OrganizationConfig -ErrorAction Stop
        $SharingPolicies = Get-SharingPolicy -ErrorAction SilentlyContinue
        $RemoteDomains = Get-RemoteDomain -ErrorAction Stop

        $OrganizationSecurityControl = @{
            ControlID = "EXC-11.1"
            ControlName = "Organization Configuration Security"
            RiskLevel = "Medium"
            CVSSScore = 6.0
            OrganizationConfig = $OrgConfig | Select-Object Name, ExternalDirectoryOrganizationId, IsDehydrated, WhenCreated
            SharingPolicies = $SharingPolicies | Select-Object Name, Enabled, Domains
            RemoteDomains = $RemoteDomains | Select-Object Name, DomainName, AllowedOOFType, AutoReplyEnabled, AutoForwardEnabled
            ExternalSharingEnabled = if ($SharingPolicies) { ($SharingPolicies | Where-Object { $_.Enabled }).Count } else { 0 }
            AutoForwardingEnabled = ($RemoteDomains | Where-Object { $_.AutoForwardEnabled }).Count
            TotalRemoteDomains = $RemoteDomains.Count
            CurrentValue = "External sharing policies: $($ExternalSharingEnabled), Auto-forwarding enabled domains: $($AutoForwardingEnabled)"
            BaselineValue = "Restricted external sharing and auto-forwarding policies"
            ComplianceStatus = if ($AutoForwardingEnabled -eq 0) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($AutoForwardingEnabled -eq 0 -and $ExternalSharingEnabled -le 1) { 100 } else { 50 }
            Finding = if ($AutoForwardingEnabled -eq 0) { "Auto-forwarding properly restricted" } else { "Auto-forwarding enabled - potential data leakage risk" }
            Recommendation = "Review and restrict external sharing policies and disable auto-forwarding where not required"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("EXC_11_1_OrganizationSecurity", $OrganizationSecurityControl)
        Write-Host "✓ Organization configuration security assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to assess organization security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("EXC_11_1_OrganizationSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # COMPLIANCE SUMMARY AND FINAL PROCESSING
    # =============================================================================

    Write-Host ""
    Write-Host "Generating compliance summary..." -ForegroundColor Yellow

    $AuditEndTime = Get-Date
    $AuditDuration = $AuditEndTime - $AuditStartTime

    # Calculate overall compliance metrics
    $SuccessfulAssessments = ($AuditResults.Keys | Where-Object {
        $_ -ne "AuditMetadata" -and $_ -ne "ComplianceSummary" -and
        (-not $AuditResults[$_].ContainsKey("Error") -or $AuditResults[$_].Error -eq $null)
    }).Count
    $FailedAssessments = $TotalControls - $SuccessfulAssessments

    $ComplianceSummary = @{
        AssessmentType = "Exchange Server Security Controls Compliance Summary"
        TotalControlsAssessed = $TotalControls
        SuccessfulAssessments = $SuccessfulAssessments
        FailedAssessments = $FailedAssessments
        AuditStartTime = $AuditStartTime
        AuditEndTime = $AuditEndTime
        AuditDurationSeconds = [math]::Round($AuditDuration.TotalSeconds, 2)
        AuditID = $AuditID
        FrameworkVersion = "Exchange Security Controls v1.0"
        AuditType = "Read-Only Assessment - Production Safe"
        AuditExecutedBy = $env:USERNAME
        ComputerName = $env:COMPUTERNAME
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    }

    $AuditResults.Add("ComplianceSummary", $ComplianceSummary)

} catch {
    Write-Host "✗ Critical error during audit execution: $($_.Exception.Message)" -ForegroundColor Red
    $AuditResults.Add("CriticalError", @{
        Message = $_.Exception.Message
        StackTrace = $_.Exception.StackTrace
        Timestamp = Get-Date
    })
} finally {
    # =============================================================================
    # OUTPUT GENERATION AND COMPLETION
    # =============================================================================

    Write-Host ""
    Write-Host "Generating JSON output..." -ForegroundColor Yellow

    try {
        # Convert results to JSON and save to file
        $JsonOutput = $AuditResults | ConvertTo-Json -Depth 10 -ErrorAction Stop
        $JsonOutput | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop

        Write-Host ""
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Exchange Server Security Controls Audit Completed" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
        Write-Host "End Time: $((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Duration: $([math]::Round(((Get-Date) - $AuditStartTime).TotalSeconds, 2)) seconds" -ForegroundColor Cyan
        Write-Host "Controls Assessed: $TotalControls" -ForegroundColor Cyan
        Write-Host "Output File: $OutputPath" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "✓ JSON audit results saved successfully" -ForegroundColor Green
        Write-Host "✓ Send the file '$OutputPath' for compliance analysis" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green

        # Display summary statistics
        if ($AuditResults.ContainsKey("ComplianceSummary")) {
            $Summary = $AuditResults["ComplianceSummary"]
            Write-Host ""
            Write-Host "AUDIT SUMMARY:" -ForegroundColor Yellow
            Write-Host "- Total Controls: $($Summary.TotalControlsAssessed)" -ForegroundColor White
            Write-Host "- Successful Assessments: $($Summary.SuccessfulAssessments)" -ForegroundColor Green
            Write-Host "- Failed Assessments: $($Summary.FailedAssessments)" -ForegroundColor Red
            Write-Host "- Success Rate: $([math]::Round(($Summary.SuccessfulAssessments / $Summary.TotalControlsAssessed) * 100, 1))%" -ForegroundColor Cyan
        }

    } catch {
        Write-Host "✗ Failed to save JSON output: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Attempting to save to alternative location..." -ForegroundColor Yellow

        try {
            $AlternativePath = ".\Exchange-Audit-Results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
            $JsonOutput | Out-File -FilePath $AlternativePath -Encoding UTF8 -ErrorAction Stop
            Write-Host "✓ JSON output saved to alternative location: $AlternativePath" -ForegroundColor Green
        } catch {
            Write-Host "✗ Failed to save to alternative location. Displaying results:" -ForegroundColor Red
            Write-Host $JsonOutput
        }
    }
}

<#
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN (Exchange Server):
================================================================================

1. EXCHANGE SERVER LOCAL EXECUTION (Recommended):
   .\Exchange-Security-Audit-Complete.ps1

2. WITH CUSTOM OUTPUT PATH:
   .\Exchange-Security-Audit-Complete.ps1 -OutputPath "C:\Temp\Exchange-Audit-Results.json"

3. INCLUDING DETAILED MAILBOX ANALYSIS:
   .\Exchange-Security-Audit-Complete.ps1 -IncludeMailboxDetails -MaxMailboxSample 500

4. REMOTE EXECUTION (Exchange Management Shell):
   # Connect to Exchange first, then run the script
   $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://ExchangeServer/PowerShell/
   Import-PSSession $Session
   .\Exchange-Security-Audit-Complete.ps1

PREREQUISITES:
- Exchange Server 2016/2019 or Exchange Online
- Exchange Management Shell or PowerShell with Exchange module
- User account with appropriate Exchange permissions:
  * View-Only Organization Management (minimum)
  * Organization Management (for comprehensive assessment)
- PowerShell 5.1 or later

IMPORTANT NOTES:
- This script is 100% READ-ONLY and safe for production
- No Exchange configuration modifications are performed
- Output is in JSON format for easy processing
- Script assesses 11 critical Exchange security controls
- Compatible with Exchange 2016, 2019, and Exchange Online
- All operations use only Get-* cmdlets and read-only functions

SEND BACK: The generated Exchange-Audit-Results.json file

SECURITY VERIFICATION:
- Only Get-* cmdlets used (Get-ExchangeServer, Get-Mailbox, etc.)
- No Set-*, New-*, Remove-*, or Enable-* cmdlets
- No configuration changes or data modifications
- Read-only access to Exchange configuration and metadata
- Safe for production environments

================================================================================
#>
