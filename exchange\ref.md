
## 📊 **Single Command Line Conversions**

### **JSON to CSV (One-Liners)**

#### **Full Access Permissions to CSV**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Csv "FullAccess.csv" -NoTypeInformation
```

#### **Send-As Permissions to CSV**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_4_1_SendAsPermissions.SendAsPermissions | Export-Csv "SendAs.csv" -NoTypeInformation
```

#### **Cross-Domain Analysis to CSV**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_2_1_FullAccessPermissions.CrossDomainAnalysis.CrossDomainRelationships | Export-Csv "CrossDomain.csv" -NoTypeInformation
```

#### **Administrator Discovery to CSV**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).AdministratorDiscovery.RoleAssignments | Export-Csv "Admins.csv" -NoTypeInformation
```

#### **Audit Summary to CSV**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).ComplianceSummary | Export-Csv "Summary.csv" -NoTypeInformation
```

---

### **JSON to Excel (One-Liners)**

#### **Full Access Permissions to Excel**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Excel "FullAccess.xlsx" -AutoSize -BoldTopRow
```

#### **All Sections to Multi-Sheet Excel**
```powershell
$r = Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json; $r.ComplianceSummary | Export-Excel "Audit.xlsx" -WorksheetName "Summary" -AutoSize; $r.MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Excel "Audit.xlsx" -WorksheetName "FullAccess" -AutoSize
```

---

### **Most Common Use Cases**

#### **Export Full Access Permissions (Most Used)**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Csv "FullAccess.csv" -NoTypeInformation
```

#### **Export High-Risk Cross-Domain Relationships**
```powershell
(Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json).MBX_2_1_FullAccessPermissions.CrossDomainAnalysis.CrossDomainRelationships | Where-Object {$_.RiskLevel -eq "High"} | Export-Csv "HighRisk.csv" -NoTypeInformation
```

#### **Export Complete Excel Report**
```powershell
$r = Get-Content "Exchange-Mailbox-Security-Results.json" | ConvertFrom-Json; $r.MBX_2_1_FullAccessPermissions.FullAccessPermissions | Export-Excel "Report.xlsx" -WorksheetName "FullAccess" -AutoSize -BoldTopRow
```

**Note**: For Excel commands, you need the ImportExcel module installed first:
```powershell
Install-Module -Name ImportExcel -Force
```

