# Microsoft SQL Server 2019 CIS Controls v8 Compliance Audit Framework

## 📋 Overview

This comprehensive audit framework assesses Microsoft SQL Server 2019 environments against the 20 most critical CIS Controls v8 technical configurations for database security. The framework provides read-only assessment capabilities with detailed compliance reporting and remediation guidance.

## 🎯 Framework Objectives

- **Comprehensive Assessment:** Evaluate 20 critical CIS Controls v8 configurations
- **Risk-Based Analysis:** CVSS-based severity scoring for non-compliant findings
- **Multi-Format Reporting:** CSV, HTML, JSON outputs for various stakeholders
- **Production Safe:** 100% read-only operations with minimal permission requirements
- **Enterprise Ready:** Support for local and remote SQL Server instances

## 📁 Directory Structure

```
mssql-2019/
├── README.md                           # This documentation file
├── CIS-Controls-v8-Mapping.md         # CIS Controls mapping and references
├── scripts/
│   ├── individual/                     # Individual CIS control check scripts
│   │   ├── CIS-01-Authentication.sql
│   │   ├── CIS-02-Access-Control.sql
│   │   ├── CIS-03-Encryption-TDE.sql
│   │   └── ... (17 more scripts)
│   ├── Master-Audit-Script.sql        # Combined audit execution
│   └── PowerShell/
│       ├── Invoke-CISAudit.ps1        # Main orchestration script
│       ├── Report-Generator.ps1       # Report generation module
│       └── Utilities.ps1              # Helper functions
├── config/
│   ├── CIS-Baseline-Config.json       # CIS Controls v8 baseline values
│   └── Audit-Settings.json            # Audit configuration settings
├── output/
│   ├── templates/                      # Report templates
│   └── samples/                        # Sample output files
└── docs/
    ├── Installation-Guide.md           # Setup and installation
    ├── Troubleshooting-Guide.md        # Common issues and solutions
    └── Interpretation-Guide.md         # Report interpretation
```

## 🔍 CIS Controls v8 Coverage (20 Critical Configurations)

### **Authentication & Access Control (Controls 4.1-4.8)**
1. **CIS 4.1** - SQL Server Authentication Mode Configuration
2. **CIS 4.2** - Default Database User Account Security
3. **CIS 4.3** - SQL Server Service Account Configuration
4. **CIS 4.4** - Password Policy Enforcement
5. **CIS 4.5** - Login Auditing Configuration

### **Encryption & Data Protection (Controls 3.1-3.6)**
6. **CIS 3.1** - Transparent Data Encryption (TDE) Status
7. **CIS 3.2** - Connection Encryption Configuration
8. **CIS 3.3** - Backup Encryption Settings
9. **CIS 3.4** - Certificate Management
10. **CIS 3.5** - Column-Level Encryption Assessment

### **Audit & Monitoring (Controls 8.1-8.5)**
11. **CIS 8.1** - SQL Server Audit Configuration
12. **CIS 8.2** - Login Failure Auditing
13. **CIS 8.3** - Database Schema Change Auditing
14. **CIS 8.4** - Data Access Auditing
15. **CIS 8.5** - Audit Log Security and Retention

### **Network Security (Controls 12.1-12.3)**
16. **CIS 12.1** - Network Protocol Configuration
17. **CIS 12.2** - Port Security Configuration
18. **CIS 12.3** - Remote Access Security

### **System Configuration (Controls 5.1-5.2)**
19. **CIS 5.1** - Database File Security Permissions
20. **CIS 5.2** - SQL Server Configuration Security

## 🚀 Quick Start

### **Prerequisites**
- SQL Server 2019 (Express, Standard, or Enterprise)
- Windows Server 2016/2019/2022
- PowerShell 5.1 or later
- Minimum permissions: `db_datareader` equivalent

### **Basic Execution**
```powershell
# Navigate to framework directory
cd .\mssql-2019\

# Execute comprehensive audit
.\scripts\PowerShell\Invoke-CISAudit.ps1 -ServerInstance "localhost" -OutputPath ".\output\"

# Execute with custom configuration
.\scripts\PowerShell\Invoke-CISAudit.ps1 -ServerInstance "SQLSERVER01\INSTANCE01" -ConfigFile ".\config\Custom-Audit-Settings.json"
```

### **Advanced Options**
```powershell
# Remote server audit with credentials
.\scripts\PowerShell\Invoke-CISAudit.ps1 -ServerInstance "remote-sql.domain.com" -Credential (Get-Credential) -OutputPath "C:\Audits\"

# Specific control assessment
.\scripts\PowerShell\Invoke-CISAudit.ps1 -ServerInstance "localhost" -ControlFilter "CIS-3.*,CIS-8.*" -ReportFormat "HTML,JSON"
```

## 📊 Report Formats

### **CSV Output**
- Structured data for analysis and GRC tool integration
- Compliance tracking and trend analysis
- Automated processing and dashboard integration

### **HTML Report**
- Executive summary with compliance dashboard
- Detailed findings with visual indicators
- Remediation recommendations with code examples
- Risk severity matrix and compliance trends

### **JSON Format**
- Programmatic integration with SIEM systems
- API consumption for security tools
- Automated workflow integration
- Machine-readable compliance data

## 🔒 Security & Compliance

### **Read-Only Operations**
- **Verified Safe:** All scripts use only SELECT statements and system views
- **No Modifications:** Zero write, update, or delete operations
- **Production Ready:** Safe for live production environments
- **Audit Trail:** Complete logging of all operations performed

### **Minimal Permissions Required**
```sql
-- SQL Server Permissions
GRANT VIEW SERVER STATE TO [AuditUser];
GRANT VIEW ANY DEFINITION TO [AuditUser];
-- Database-level permissions
USE [DatabaseName];
GRANT db_datareader TO [AuditUser];
```

### **Windows Permissions**
- Local read access to SQL Server installation directory
- Registry read access for service configuration
- Event log read access for audit trail verification

## 📈 Compliance Scoring

### **Compliance States**
- ✅ **Compliant:** Configuration meets CIS Controls v8 recommendations
- ❌ **Non-Compliant:** Configuration deviates from security baseline
- ⚠️ **Not Configured:** Required security feature not implemented
- ➖ **Not Applicable:** Control not relevant to current environment

### **Risk Severity Ratings (CVSS-based)**
- 🔴 **Critical (9.0-10.0):** Immediate security risk requiring urgent action
- 🟠 **High (7.0-8.9):** Significant security risk requiring prompt attention
- 🟡 **Medium (4.0-6.9):** Moderate security risk requiring planned remediation
- 🟢 **Low (0.1-3.9):** Minor security risk for future consideration

## 🛠️ Customization

### **Configuration Files**
- **CIS-Baseline-Config.json:** Modify baseline values for organizational requirements
- **Audit-Settings.json:** Customize audit scope and reporting preferences
- **Custom control definitions:** Add organization-specific security checks

### **Report Templates**
- **HTML Templates:** Customize branding and layout
- **CSV Schemas:** Modify data structure for specific tools
- **JSON Formats:** Adapt for API integration requirements

## 📞 Support & Documentation

### **Detailed Guides**
- **Installation Guide:** Step-by-step setup instructions
- **Troubleshooting Guide:** Common issues and resolution steps
- **Interpretation Guide:** Understanding audit results and recommendations

### **Sample Outputs**
- Example CSV files with compliance data
- Sample HTML reports with visual dashboards
- JSON format examples for integration reference

## 🔄 Version Information

- **Framework Version:** 1.0
- **CIS Controls Version:** v8.1
- **SQL Server Support:** 2019 (all editions)
- **Windows Support:** Server 2016/2019/2022
- **Last Updated:** August 17, 2025

## ⚖️ Legal & Compliance

### **Audit Authority**
🏛️ **Authority:** Internal IT Security & Compliance Team  
🧑‍⚖️ **Final Verdict:** Approved for Production Audit Purposes

### **Compliance Frameworks**
- CIS Controls v8.1 alignment
- NIST Cybersecurity Framework mapping
- ISO 27001 control correlation
- SOX IT general controls support

---

**Note:** This framework is designed for security assessment and compliance verification. All scripts are read-only and safe for production environments. Regular execution is recommended for continuous compliance monitoring and security posture assessment.
