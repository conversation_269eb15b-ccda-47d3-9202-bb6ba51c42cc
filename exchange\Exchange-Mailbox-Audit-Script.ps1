#Requires -Version 3.0

<#
.SYNOPSIS
    Exchange Server 2016 Comprehensive Mailbox Audit Script
    
.DESCRIPTION
    Production-ready, read-only PowerShell script for auditing Exchange Server 2016 mailboxes.
    Extracts comprehensive mailbox information for compliance and migration planning purposes.
    
    CRITICAL: This script is 100% READ-ONLY and performs no write, modify, or delete operations.
    
.PARAMETER ExchangeServer
    Exchange Server FQDN for remote PowerShell connection (optional for local execution)
    
.PARAMETER OutputPath
    Directory path for output files (default: current directory)
    
.PARAMETER DomainFilter
    Filter mailboxes by email domain (e.g., '*@contoso.com')
    
.PARAMETER OrganizationalUnit
    Filter mailboxes by specific Organizational Unit
    
.PARAMETER IncludeDisabled
    Include disabled mailboxes in the report (default: $false)
    
.PARAMETER MaxResults
    Maximum number of mailboxes to process (default: unlimited)
    
.PARAMETER UseRemoteSession
    Use remote PowerShell session instead of local Exchange Management Shell
    
.PARAMETER Credential
    Credentials for remote PowerShell connection
    
.EXAMPLE
    .\Exchange-Mailbox-Audit-Script.ps1 -OutputPath "C:\ExchangeAudit"
    
.EXAMPLE
    .\Exchange-Mailbox-Audit-Script.ps1 -DomainFilter "*@contoso.com" -IncludeDisabled -OutputPath "C:\Audit"
    
.EXAMPLE
    .\Exchange-Mailbox-Audit-Script.ps1 -ExchangeServer "exchange01.contoso.com" -UseRemoteSession -Credential (Get-Credential)
    
.NOTES
    Version: 1.0
    Author: Exchange Audit Team
    Created: August 10, 2024
    
    Prerequisites:
    - Exchange Server 2016 environment
    - Exchange Management Shell or remote PowerShell access
    - Read permissions on Exchange organization
    - PowerShell 3.0 or later
    
    Minimum Required Permissions:
    - View-Only Organization Management role
    - Recipient Management role (read-only)
    - Or equivalent custom role with Get-* cmdlet permissions
#>

[CmdletBinding()]
param(
    [string]$ExchangeServer,
    [string]$OutputPath = (Get-Location).Path,
    [string]$DomainFilter,
    [string]$OrganizationalUnit,
    [switch]$IncludeDisabled = $false,
    [int]$MaxResults = 0,
    [switch]$UseRemoteSession = $false,
    [PSCredential]$Credential
)

# Global Variables
$Script:StartTime = Get-Date
$Script:LogFile = Join-Path $OutputPath "Exchange-Mailbox-Audit-Log-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
$Script:ErrorCount = 0
$Script:MailboxCount = 0
$Script:ExchangeSession = $null

# Initialize logging function
function Write-AuditLog {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS", "PROGRESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Console output with colors
    $color = switch ($Level) {
        "INFO" { "White" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "SUCCESS" { "Green" }
        "PROGRESS" { "Cyan" }
    }
    
    Write-Host $logEntry -ForegroundColor $color
    
    # Log to file
    try {
        Add-Content -Path $Script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
    }
    catch {
        # Silently continue if logging fails
    }
}

# Validate prerequisites
function Test-Prerequisites {
    Write-AuditLog "Validating prerequisites..." "INFO"
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 3) {
        Write-AuditLog "PowerShell 3.0 or later is required. Current version: $($PSVersionTable.PSVersion)" "ERROR"
        return $false
    }
    
    # Check output directory
    if (!(Test-Path $OutputPath)) {
        try {
            New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
            Write-AuditLog "Created output directory: $OutputPath" "SUCCESS"
        }
        catch {
            Write-AuditLog "Failed to create output directory: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    
    # Initialize log file
    try {
        "Exchange Server 2016 Mailbox Audit Log - Started: $(Get-Date)" | Out-File -FilePath $Script:LogFile -Encoding UTF8
        Write-AuditLog "Log file initialized: $Script:LogFile" "SUCCESS"
    }
    catch {
        Write-AuditLog "Failed to initialize log file: $($_.Exception.Message)" "WARNING"
    }
    
    return $true
}

# Establish Exchange connection
function Connect-ExchangeEnvironment {
    Write-AuditLog "Establishing Exchange connection..." "INFO"
    
    try {
        if ($UseRemoteSession) {
            # Remote PowerShell connection
            if (!$ExchangeServer) {
                Write-AuditLog "ExchangeServer parameter required for remote connection" "ERROR"
                return $false
            }
            
            $sessionParams = @{
                ConfigurationName = 'Microsoft.Exchange'
                ConnectionUri = "http://$ExchangeServer/PowerShell/"
                Authentication = 'Kerberos'
            }
            
            if ($Credential) {
                $sessionParams.Credential = $Credential
            }
            
            Write-AuditLog "Connecting to Exchange Server: $ExchangeServer" "INFO"
            $Script:ExchangeSession = New-PSSession @sessionParams
            Import-PSSession $Script:ExchangeSession -DisableNameChecking -AllowClobber | Out-Null
            
            Write-AuditLog "Remote Exchange session established successfully" "SUCCESS"
        }
        else {
            # Local Exchange Management Shell
            Write-AuditLog "Using local Exchange Management Shell" "INFO"
            
            # Test if Exchange cmdlets are available
            if (!(Get-Command Get-Mailbox -ErrorAction SilentlyContinue)) {
                Write-AuditLog "Exchange Management Shell not detected. Please run from Exchange Management Shell or use -UseRemoteSession" "ERROR"
                return $false
            }
            
            Write-AuditLog "Exchange Management Shell detected successfully" "SUCCESS"
        }
        
        # Test Exchange connectivity
        $testMailbox = Get-Mailbox -ResultSize 1 -ErrorAction Stop
        Write-AuditLog "Exchange connectivity test successful" "SUCCESS"
        
        return $true
    }
    catch {
        Write-AuditLog "Failed to establish Exchange connection: $($_.Exception.Message)" "ERROR"
        $Script:ErrorCount++
        return $false
    }
}

# Get mailbox statistics with error handling
function Get-MailboxStatisticsSafe {
    param([string]$Identity)
    
    try {
        $stats = Get-MailboxStatistics -Identity $Identity -ErrorAction Stop
        return $stats
    }
    catch {
        Write-AuditLog "Failed to get statistics for mailbox: $Identity - $($_.Exception.Message)" "WARNING"
        return $null
    }
}

# Get mailbox database information
function Get-MailboxDatabaseInfo {
    param([string]$Database)
    
    try {
        $dbInfo = Get-MailboxDatabase -Identity $Database -ErrorAction Stop
        return @{
            DatabaseName = $dbInfo.Name
            ServerName = $dbInfo.Server
            DatabasePath = $dbInfo.EdbFilePath
        }
    }
    catch {
        Write-AuditLog "Failed to get database info for: $Database - $($_.Exception.Message)" "WARNING"
        return @{
            DatabaseName = $Database
            ServerName = "Unknown"
            DatabasePath = "Unknown"
        }
    }
}

# Convert bytes to human readable format
function Convert-BytesToSize {
    param([long]$Bytes)
    
    if ($Bytes -eq 0) { return "0 MB" }
    
    $sizes = @("Bytes", "KB", "MB", "GB", "TB")
    $index = 0
    $size = $Bytes
    
    while ($size -gt 1024 -and $index -lt $sizes.Length - 1) {
        $size = $size / 1024
        $index++
    }
    
    return "{0:N2} {1}" -f $size, $sizes[$index]
}

# Main mailbox processing function
function Get-MailboxAuditData {
    Write-AuditLog "Starting mailbox data collection..." "INFO"
    
    try {
        # Build Get-Mailbox parameters
        $getMailboxParams = @{
            ResultSize = if ($MaxResults -gt 0) { $MaxResults } else { "Unlimited" }
        }
        
        # Add filters
        if ($DomainFilter) {
            $getMailboxParams.Filter = "EmailAddresses -like '$DomainFilter'"
            Write-AuditLog "Applying domain filter: $DomainFilter" "INFO"
        }
        
        if ($OrganizationalUnit) {
            $getMailboxParams.OrganizationalUnit = $OrganizationalUnit
            Write-AuditLog "Filtering by OU: $OrganizationalUnit" "INFO"
        }
        
        # Get mailboxes
        Write-AuditLog "Retrieving mailbox list..." "PROGRESS"
        $mailboxes = Get-Mailbox @getMailboxParams
        
        if (!$mailboxes) {
            Write-AuditLog "No mailboxes found matching the specified criteria" "WARNING"
            return @()
        }
        
        $totalMailboxes = ($mailboxes | Measure-Object).Count
        Write-AuditLog "Found $totalMailboxes mailboxes to process" "SUCCESS"
        
        $auditData = @()
        $processedCount = 0
        
        foreach ($mailbox in $mailboxes) {
            $processedCount++
            
            # Progress indicator
            if ($processedCount % 50 -eq 0 -or $processedCount -eq $totalMailboxes) {
                $percentComplete = [math]::Round(($processedCount / $totalMailboxes) * 100, 2)
                Write-AuditLog "Processing mailbox $processedCount of $totalMailboxes ($percentComplete%)" "PROGRESS"
            }
            
            try {
                # Get mailbox statistics
                $stats = Get-MailboxStatisticsSafe -Identity $mailbox.Identity
                
                # Get database information
                $dbInfo = Get-MailboxDatabaseInfo -Database $mailbox.Database
                
                # Get AD user information
                $adUser = $null
                try {
                    $adUser = Get-User -Identity $mailbox.Identity -ErrorAction Stop
                }
                catch {
                    Write-AuditLog "Failed to get AD user info for: $($mailbox.Identity)" "WARNING"
                }
                
                # Skip disabled mailboxes if not requested
                $validRecipientTypes = @("UserMailbox", "SharedMailbox", "RoomMailbox", "EquipmentMailbox")
                if (!$IncludeDisabled -and $adUser -and $adUser.RecipientTypeDetails -notin $validRecipientTypes) {
                    continue
                }
                
                # Build email aliases list
                $emailAliases = @()
                foreach ($address in $mailbox.EmailAddresses) {
                    if ($address -like "smtp:*" -and $address -ne "SMTP:$($mailbox.PrimarySmtpAddress)") {
                        $emailAliases += $address.ToString().Substring(5)  # Remove "smtp:" prefix
                    }
                }
                
                # Create audit record
                $auditRecord = [PSCustomObject]@{
                    DisplayName = $mailbox.DisplayName
                    UserPrincipalName = $mailbox.UserPrincipalName
                    SamAccountName = $mailbox.SamAccountName
                    PrimarySmtpAddress = $mailbox.PrimarySmtpAddress
                    EmailAliases = ($emailAliases -join ", ")
                    AccountStatus = if ($adUser) { 
                        if ($adUser.RecipientTypeDetails -match "UserMailbox|SharedMailbox|RoomMailbox|EquipmentMailbox") { "Active" } else { "Disabled" }
                    } else { "Unknown" }
                    MailboxStatus = if ($mailbox.ExchangeVersion) { "Enabled" } else { "Disabled" }
                    LastLogonDate = if ($stats) { $stats.LastLogonTime } else { "Never" }
                    LastEmailActivity = if ($stats) { $stats.LastUserActionTime } else { "Never" }
                    LastItemReceived = if ($stats) { $stats.LastUserActionTime } else { "Never" }
                    AccountCreationDate = if ($adUser) { $adUser.WhenCreated } else { "Unknown" }
                    AccountDisabledDate = if ($adUser -and !$adUser.RecipientTypeDetails -match "UserMailbox|SharedMailbox|RoomMailbox|EquipmentMailbox") { 
                        $adUser.WhenChanged 
                    } else { 
                        "N/A" 
                    }
                    MailboxCreationDate = $mailbox.WhenCreated
                    MailboxSizeMB = if ($stats -and $stats.TotalItemSize) { 
                        [math]::Round(($stats.TotalItemSize.Value.ToBytes() / 1MB), 2)
                    } else { 0 }
                    MailboxSizeFormatted = if ($stats -and $stats.TotalItemSize) { 
                        Convert-BytesToSize -Bytes $stats.TotalItemSize.Value.ToBytes()
                    } else { "0 MB" }
                    ItemCount = if ($stats) { $stats.ItemCount } else { 0 }
                    DatabaseName = $dbInfo.DatabaseName
                    ServerLocation = $dbInfo.ServerName
                    MailboxType = $mailbox.RecipientTypeDetails
                    OrganizationalUnit = $mailbox.OrganizationalUnit
                    LastModified = $mailbox.WhenChanged
                    ExchangeVersion = $mailbox.ExchangeVersion
                    AuditEnabled = $mailbox.AuditEnabled
                    AuditDate = $Script:StartTime
                }
                
                $auditData += $auditRecord
                $Script:MailboxCount++
                
            }
            catch {
                Write-AuditLog "Error processing mailbox $($mailbox.Identity): $($_.Exception.Message)" "ERROR"
                $Script:ErrorCount++
                continue
            }
        }
        
        Write-AuditLog "Mailbox data collection completed. Processed: $Script:MailboxCount mailboxes" "SUCCESS"
        return $auditData
        
    }
    catch {
        Write-AuditLog "Critical error during mailbox data collection: $($_.Exception.Message)" "ERROR"
        $Script:ErrorCount++
        return @()
    }
}

# Export data to CSV
function Export-AuditData {
    param([array]$AuditData)
    
    if ($AuditData.Count -eq 0) {
        Write-AuditLog "No data to export" "WARNING"
        return $null
    }
    
    try {
        $csvPath = Join-Path $OutputPath "Exchange-Mailbox-Audit-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
        
        Write-AuditLog "Exporting data to CSV: $csvPath" "INFO"
        $AuditData | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
        
        Write-AuditLog "CSV export completed successfully" "SUCCESS"
        Write-AuditLog "Report location: $csvPath" "INFO"
        
        return $csvPath
        
    }
    catch {
        Write-AuditLog "Failed to export CSV: $($_.Exception.Message)" "ERROR"
        $Script:ErrorCount++
        return $null
    }
}

# Generate summary report
function New-SummaryReport {
    param([array]$AuditData, [string]$CsvPath)
    
    try {
        $summaryPath = Join-Path $OutputPath "Exchange-Mailbox-Audit-Summary-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
        
        # Calculate success rate with division by zero protection
        $totalProcessed = $Script:MailboxCount + $Script:ErrorCount
        $successRate = if ($totalProcessed -gt 0) {
            [math]::Round(($Script:MailboxCount / $totalProcessed) * 100, 2)
        } else { 0 }

        $summary = @"
Exchange Server 2016 Mailbox Audit Summary
==========================================
Audit Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Duration: $((Get-Date) - $Script:StartTime)

STATISTICS:
-----------
Total Mailboxes Processed: $Script:MailboxCount
Active Mailboxes: $(($AuditData | Where-Object {$_.AccountStatus -eq 'Active'}).Count)
Disabled Mailboxes: $(($AuditData | Where-Object {$_.AccountStatus -eq 'Disabled'}).Count)
Shared Mailboxes: $(($AuditData | Where-Object {$_.MailboxType -like '*Shared*'}).Count)
Room Mailboxes: $(($AuditData | Where-Object {$_.MailboxType -like '*Room*'}).Count)
Equipment Mailboxes: $(($AuditData | Where-Object {$_.MailboxType -like '*Equipment*'}).Count)

MAILBOX SIZES:
--------------
Total Mailbox Data: $([math]::Round((($AuditData | Measure-Object -Property MailboxSizeMB -Sum).Sum / 1024), 2)) GB
Average Mailbox Size: $([math]::Round((($AuditData | Measure-Object -Property MailboxSizeMB -Average).Average), 2)) MB
Largest Mailbox: $([math]::Round((($AuditData | Measure-Object -Property MailboxSizeMB -Maximum).Maximum), 2)) MB
Smallest Mailbox: $([math]::Round((($AuditData | Measure-Object -Property MailboxSizeMB -Minimum).Minimum), 2)) MB

AUDIT SETTINGS:
---------------
Mailboxes with Auditing Enabled: $(($AuditData | Where-Object {$_.AuditEnabled -eq $true}).Count)
Mailboxes without Auditing: $(($AuditData | Where-Object {$_.AuditEnabled -eq $false}).Count)

ERROR SUMMARY:
--------------
Total Errors Encountered: $Script:ErrorCount
Success Rate: $successRate%

OUTPUT FILES:
-------------
CSV Report: $CsvPath
Summary Report: $summaryPath
Log File: $Script:LogFile

FILTERS APPLIED:
----------------
Domain Filter: $(if ($DomainFilter) { $DomainFilter } else { 'None' })
Organizational Unit: $(if ($OrganizationalUnit) { $OrganizationalUnit } else { 'None' })
Include Disabled: $IncludeDisabled
Max Results: $(if ($MaxResults -gt 0) { $MaxResults } else { 'Unlimited' })

RECOMMENDATIONS:
----------------
$(if ($Script:ErrorCount -gt 0) { "- Review error log for failed mailbox processing" })
$(if (($AuditData | Where-Object {$_.AuditEnabled -eq $false}).Count -gt 0) { "- Consider enabling auditing for compliance requirements" })
$(if (($AuditData | Where-Object {$_.LastLogonDate -eq 'Never'}).Count -gt 0) { "- Review mailboxes that have never been accessed" })
- Regular mailbox audits recommended for compliance and migration planning
- Monitor mailbox growth trends for capacity planning

Report generated by Exchange Mailbox Audit Script v1.0
"@

        $summary | Out-File -FilePath $summaryPath -Encoding UTF8
        Write-AuditLog "Summary report generated: $summaryPath" "SUCCESS"
        
        return $summaryPath
        
    }
    catch {
        Write-AuditLog "Failed to generate summary report: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# Cleanup function
function Disconnect-ExchangeEnvironment {
    if ($Script:ExchangeSession) {
        try {
            Remove-PSSession $Script:ExchangeSession -ErrorAction SilentlyContinue
            Write-AuditLog "Exchange remote session closed" "INFO"
        }
        catch {
            Write-AuditLog "Error closing Exchange session: $($_.Exception.Message)" "WARNING"
        }
    }
}

# Main execution
try {
    Write-Host "Exchange Server 2016 Mailbox Audit Script" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow
    Write-Host ""
    
    # Validate prerequisites
    if (!(Test-Prerequisites)) {
        Write-AuditLog "Prerequisites validation failed. Exiting." "ERROR"
        exit 1
    }
    
    # Connect to Exchange
    if (!(Connect-ExchangeEnvironment)) {
        Write-AuditLog "Exchange connection failed. Exiting." "ERROR"
        exit 1
    }
    
    # Collect mailbox data
    Write-AuditLog "Beginning mailbox audit data collection..." "INFO"
    $auditData = Get-MailboxAuditData
    
    if ($auditData.Count -eq 0) {
        Write-AuditLog "No mailbox data collected. Check filters and permissions." "WARNING"
        exit 1
    }
    
    # Export to CSV
    $csvPath = Export-AuditData -AuditData $auditData
    
    # Generate summary
    $summaryPath = New-SummaryReport -AuditData $auditData -CsvPath $csvPath
    
    # Final summary
    $endTime = Get-Date
    $duration = $endTime - $Script:StartTime
    
    Write-Host ""
    Write-Host "=== AUDIT COMPLETED SUCCESSFULLY ===" -ForegroundColor Green
    Write-Host "Mailboxes Processed: $Script:MailboxCount" -ForegroundColor White
    Write-Host "Errors Encountered: $Script:ErrorCount" -ForegroundColor $(if ($Script:ErrorCount -gt 0) { "Yellow" } else { "Green" })
    Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White
    Write-Host "CSV Report: $csvPath" -ForegroundColor Cyan
    Write-Host "Summary Report: $summaryPath" -ForegroundColor Cyan
    Write-Host "Log File: $Script:LogFile" -ForegroundColor Cyan
    
    Write-AuditLog "Exchange mailbox audit completed successfully!" "SUCCESS"
    
}
catch {
    Write-AuditLog "Critical script error: $($_.Exception.Message)" "ERROR"
    Write-Host "CRITICAL ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # Cleanup
    Disconnect-ExchangeEnvironment
    Write-AuditLog "Script execution completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "INFO"
}

# Return results for programmatic use
return @{
    MailboxCount = $Script:MailboxCount
    ErrorCount = $Script:ErrorCount
    CsvPath = $csvPath
    SummaryPath = $summaryPath
    LogPath = $Script:LogFile
    Duration = $duration
}
