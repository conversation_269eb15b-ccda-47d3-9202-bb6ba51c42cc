/*
================================================================================
CIS Controls v8 - Transparent Data Encryption Assessment (CIS Control 3.1)
================================================================================
Description: Verifies Transparent Data Encryption (TDE) configuration for data protection
Control ID: CIS-3.1
Risk Level: High
CVSS Score: 7.2

Baseline Requirement: Sensitive databases must be encrypted using TDE
Security Impact: Unencrypted databases expose sensitive data to unauthorized 
                access through file system or backup theft

Assessment Query: Checks TDE encryption status for all user databases
Compliance Check: encryption_state = 3 (encrypted) for sensitive databases

Remediation: CREATE DATABASE ENCRYPTION KEY; ALTER DATABASE SET ENCRYPTION ON;
================================================================================
*/

-- CIS Control 3.1: Transparent Data Encryption Assessment
SELECT 
    'CIS-3.1' AS ControlID,
    'Transparent Data Encryption (TDE)' AS ControlName,
    'High' AS RiskLevel,
    7.2 AS CVSSScore,
    db.name AS DatabaseName,
    db.database_id,
    ISNULL(dek.encryption_state, 0) AS EncryptionState,
    CASE 
        WHEN dek.encryption_state = 0 OR dek.encryption_state IS NULL THEN 'Not Encrypted'
        WHEN dek.encryption_state = 1 THEN 'Encryption in Progress'
        WHEN dek.encryption_state = 2 THEN 'Encryption Key Change in Progress'
        WHEN dek.encryption_state = 3 THEN 'Encrypted'
        WHEN dek.encryption_state = 4 THEN 'Key Change in Progress'
        WHEN dek.encryption_state = 5 THEN 'Decryption in Progress'
        WHEN dek.encryption_state = 6 THEN 'Protection Change in Progress'
        ELSE 'Unknown State'
    END AS EncryptionStatus,
    'Encrypted (State 3)' AS BaselineValue,
    CASE 
        WHEN dek.encryption_state = 3 THEN 'Compliant'
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 'In Progress'
        WHEN dek.encryption_state = 5 THEN 'Decrypting - Non-Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN dek.encryption_state = 3 THEN 100
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 50
        ELSE 0
    END AS ComplianceScore,
    dek.encryption_algorithm_desc,
    dek.key_algorithm,
    dek.key_length,
    dek.create_date AS EncryptionKeyCreateDate,
    dek.modify_date AS EncryptionKeyModifyDate,
    CASE 
        WHEN db.name IN ('master', 'model', 'msdb', 'tempdb') THEN 'System Database'
        WHEN dek.encryption_state = 3 THEN 'User database properly encrypted'
        WHEN dek.encryption_state IS NULL THEN 'User database not encrypted - security risk'
        ELSE 'User database encryption in transition'
    END AS SecurityAssessment,
    db.create_date AS DatabaseCreateDate,
    db.collation_name,
    GETDATE() AS AssessmentDate,
    @@SERVERNAME AS ServerName
FROM sys.databases db
LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id
WHERE db.database_id > 4 -- Exclude system databases
FOR JSON PATH, ROOT('TDEAssessment');

-- Master key and certificate analysis for TDE
SELECT 
    'TDE Certificate Analysis' AS AssessmentType,
    c.name AS CertificateName,
    c.certificate_id,
    c.subject,
    c.issuer_name,
    c.start_date,
    c.expiry_date,
    CASE 
        WHEN c.expiry_date > GETDATE() THEN 'Valid'
        ELSE 'Expired'
    END AS CertificateStatus,
    DATEDIFF(day, GETDATE(), c.expiry_date) AS DaysUntilExpiry,
    c.thumbprint,
    c.key_length,
    CASE 
        WHEN c.expiry_date <= DATEADD(month, 3, GETDATE()) AND c.expiry_date > GETDATE() THEN 'Warning - Certificate expires within 3 months'
        WHEN c.expiry_date <= GETDATE() THEN 'Critical - Certificate expired'
        ELSE 'Certificate valid'
    END AS ExpiryWarning,
    CASE 
        WHEN c.expiry_date <= DATEADD(month, 3, GETDATE()) THEN 'Plan certificate renewal'
        WHEN c.expiry_date <= GETDATE() THEN 'Immediate certificate renewal required'
        ELSE 'Monitor certificate expiry'
    END AS Recommendation
FROM sys.certificates c
WHERE c.name LIKE '%TDE%' OR c.name LIKE '%DEK%' OR c.name LIKE '%Encryption%'
   OR c.certificate_id IN (
       SELECT dek.encryptor_thumbprint 
       FROM sys.dm_database_encryption_keys dek 
       WHERE dek.encryptor_type = 'CERTIFICATE'
   )
FOR JSON PATH, ROOT('TDECertificateAnalysis');

-- Database master key analysis
SELECT 
    'Database Master Key Analysis' AS AssessmentType,
    db.name AS DatabaseName,
    CASE 
        WHEN dmk.is_master_key_encrypted_by_server = 1 THEN 'Encrypted by Service Master Key'
        ELSE 'Not encrypted by Service Master Key'
    END AS MasterKeyEncryption,
    dmk.create_date AS MasterKeyCreateDate,
    dmk.modify_date AS MasterKeyModifyDate,
    CASE 
        WHEN dmk.is_master_key_encrypted_by_server = 1 THEN 'Automatic encryption available'
        ELSE 'Manual password required for access'
    END AS AccessMethod,
    CASE 
        WHEN dmk.is_master_key_encrypted_by_server = 1 THEN 'Standard configuration'
        ELSE 'Review if manual password management is intended'
    END AS SecurityNote
FROM sys.databases db
INNER JOIN sys.symmetric_keys dmk ON dmk.name = '##MS_DatabaseMasterKey##'
WHERE db.database_id > 4
FOR JSON PATH, ROOT('DatabaseMasterKeyAnalysis');

-- TDE performance impact analysis
SELECT 
    'TDE Performance Impact' AS AssessmentType,
    db.name AS DatabaseName,
    CASE 
        WHEN dek.encryption_state = 3 THEN 'Encryption Complete'
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 'Encryption in Progress'
        ELSE 'Not Encrypted'
    END AS EncryptionStatus,
    dek.percent_complete,
    CASE 
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 'Monitor system performance during encryption process'
        WHEN dek.encryption_state = 3 THEN 'Minimal ongoing performance impact expected'
        ELSE 'No TDE performance impact'
    END AS PerformanceNote,
    CASE 
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 'Schedule encryption during low-activity periods'
        WHEN dek.encryption_state = 3 THEN 'Monitor backup and restore performance'
        ELSE 'Consider implementing TDE for data protection'
    END AS Recommendation
FROM sys.databases db
LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id
WHERE db.database_id > 4
FOR JSON PATH, ROOT('TDEPerformanceImpact');

-- TDE compliance summary
SELECT 
    'TDE Compliance Summary' AS AssessmentType,
    COUNT(*) AS TotalUserDatabases,
    COUNT(CASE WHEN dek.encryption_state = 3 THEN 1 END) AS EncryptedDatabases,
    COUNT(CASE WHEN dek.encryption_state IS NULL THEN 1 END) AS UnencryptedDatabases,
    COUNT(CASE WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 1 END) AS EncryptionInProgress,
    CASE 
        WHEN COUNT(CASE WHEN dek.encryption_state IS NULL THEN 1 END) = 0 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS OverallCompliance,
    CAST(COUNT(CASE WHEN dek.encryption_state = 3 THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS CompliancePercentage,
    CASE 
        WHEN COUNT(CASE WHEN dek.encryption_state IS NULL THEN 1 END) = 0 THEN 'All user databases encrypted'
        ELSE CAST(COUNT(CASE WHEN dek.encryption_state IS NULL THEN 1 END) AS VARCHAR(10)) + ' database(s) require TDE implementation'
    END AS SecurityStatus,
    CASE 
        WHEN COUNT(CASE WHEN dek.encryption_state IS NULL THEN 1 END) > 0 THEN 'Implement TDE on unencrypted databases'
        ELSE 'Maintain current TDE configuration and monitor certificate expiry'
    END AS PrimaryRecommendation
FROM sys.databases db
LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id
WHERE db.database_id > 4
FOR JSON PATH, ROOT('TDEComplianceSummary');

-- Backup encryption verification (related to TDE)
SELECT 
    'Backup Encryption Verification' AS AssessmentType,
    bs.database_name,
    bs.backup_start_date,
    bs.backup_finish_date,
    bs.type AS BackupType,
    CASE bs.type
        WHEN 'D' THEN 'Full Database'
        WHEN 'I' THEN 'Differential'
        WHEN 'L' THEN 'Transaction Log'
        ELSE 'Other'
    END AS BackupTypeDescription,
    bs.is_encrypted AS BackupEncrypted,
    bs.encryptor_type,
    bs.encryptor_thumbprint,
    CASE 
        WHEN bs.is_encrypted = 1 THEN 'Backup encrypted'
        ELSE 'Backup not encrypted'
    END AS BackupSecurityStatus,
    CASE 
        WHEN bs.is_encrypted = 0 THEN 'Consider implementing backup encryption for additional security'
        ELSE 'Backup properly encrypted'
    END AS BackupRecommendation
FROM msdb.dbo.backupset bs
WHERE bs.backup_start_date > DATEADD(day, -30, GETDATE())
   AND bs.database_name NOT IN ('master', 'model', 'msdb', 'tempdb')
FOR JSON PATH, ROOT('BackupEncryptionVerification');

/*
================================================================================
Remediation Steps for Non-Compliant Configuration:
================================================================================

1. Create Database Master Key (if not exists):
   USE master;
   CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'StrongPassword123!';

2. Create Certificate for TDE:
   CREATE CERTIFICATE TDECert WITH SUBJECT = 'TDE Certificate for Database Encryption';

3. Backup Certificate (CRITICAL - Store securely):
   BACKUP CERTIFICATE TDECert TO FILE = 'C:\Certificates\TDECert.cer'
   WITH PRIVATE KEY (FILE = 'C:\Certificates\TDECert.pvk',
   ENCRYPTION BY PASSWORD = 'CertificatePassword123!');

4. Create Database Encryption Key:
   USE [DatabaseName];
   CREATE DATABASE ENCRYPTION KEY
   WITH ALGORITHM = AES_256
   ENCRYPTION BY SERVER CERTIFICATE TDECert;

5. Enable TDE:
   ALTER DATABASE [DatabaseName] SET ENCRYPTION ON;

6. Monitor Encryption Progress:
   SELECT db_name(database_id), encryption_state, percent_complete
   FROM sys.dm_database_encryption_keys;

Security Benefits:
- Protects data at rest from unauthorized file access
- Encrypts database backups automatically
- Transparent to applications (no code changes required)
- Helps meet compliance requirements (PCI DSS, HIPAA, etc.)
- Protects against storage media theft

Important Considerations:
- Backup and securely store TDE certificates
- Monitor certificate expiry dates
- Plan for key rotation and certificate renewal
- Consider performance impact during initial encryption
- Ensure proper key management procedures

================================================================================
*/
