# CIS Audit Report Generation Functions

function Export-CISAuditToCSV {
    param(
        [array]$Results,
        [string]$OutputPath,
        [hashtable]$Summary
    )
    
    $csvPath = Join-Path $OutputPath "CIS-Audit-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
    
    try {
        $Results | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
        
        # Add summary to a separate CSV
        $summaryPath = Join-Path $OutputPath "CIS-Audit-Summary-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
        $summaryObj = [PSCustomObject]$Summary
        $summaryObj | Export-Csv -Path $summaryPath -NoTypeInformation -Encoding UTF8
        
        Write-AuditLog "CSV report exported to: $csvPath" "SUCCESS"
        Write-AuditLog "CSV summary exported to: $summaryPath" "SUCCESS"
        
        return @{
            ReportPath = $csvPath
            SummaryPath = $summaryPath
        }
    }
    catch {
        Write-AuditLog "Error exporting CSV report: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Export-CISAuditToJSON {
    param(
        [array]$Results,
        [string]$OutputPath,
        [hashtable]$Summary
    )
    
    $jsonPath = Join-Path $OutputPath "CIS-Audit-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    
    try {
        $reportData = @{
            AuditMetadata = @{
                GeneratedDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                Framework = "CIS Controls v8"
                TargetOS = "Windows Server 2019"
                TotalSystems = ($Results | Select-Object -Unique ComputerName).Count
            }
            Summary = $Summary
            Results = $Results
        }
        
        $reportData | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonPath -Encoding UTF8
        
        Write-AuditLog "JSON report exported to: $jsonPath" "SUCCESS"
        return $jsonPath
    }
    catch {
        Write-AuditLog "Error exporting JSON report: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Export-CISAuditToHTML {
    param(
        [array]$Results,
        [string]$OutputPath,
        [hashtable]$Summary
    )
    
    $htmlPath = Join-Path $OutputPath "CIS-Audit-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
    
    try {
        $html = @"
<!DOCTYPE html>
<html>
<head>
    <title>CIS Controls v8 Audit Report - Windows Server 2019</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .summary-item { text-align: center; padding: 15px; border-radius: 5px; }
        .compliant { background-color: #d4edda; color: #155724; }
        .non-compliant { background-color: #f8d7da; color: #721c24; }
        .not-configured { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f5c6cb; color: #721c24; }
        table { width: 100%; border-collapse: collapse; background-color: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #34495e; color: white; }
        tr:hover { background-color: #f5f5f5; }
        .status-compliant { color: #28a745; font-weight: bold; }
        .status-non-compliant { color: #dc3545; font-weight: bold; }
        .status-not-configured { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .risk-high { background-color: #dc3545; color: white; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .risk-medium { background-color: #ffc107; color: black; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .risk-low { background-color: #28a745; color: white; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; }
        .remediation { font-family: monospace; background-color: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CIS Controls v8 Audit Report</h1>
        <h2>Windows Server 2019 Environment</h2>
        <p>Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
        <p>Total Systems Audited: $(($Results | Select-Object -Unique ComputerName).Count)</p>
    </div>
    
    <div class="summary">
        <h2>Compliance Summary</h2>
        <div class="summary-grid">
            <div class="summary-item compliant">
                <h3>$($Summary.Compliant)</h3>
                <p>Compliant Controls</p>
            </div>
            <div class="summary-item non-compliant">
                <h3>$($Summary.NonCompliant)</h3>
                <p>Non-Compliant Controls</p>
            </div>
            <div class="summary-item not-configured">
                <h3>$($Summary.NotConfigured)</h3>
                <p>Not Configured</p>
            </div>
            <div class="summary-item error">
                <h3>$($Summary.Errors)</h3>
                <p>Errors</p>
            </div>
        </div>
        <div style="margin-top: 20px; text-align: center;">
            <h3>Overall Compliance: $($Summary.CompliancePercentage)%</h3>
            <p><strong>High Risk Issues:</strong> $($Summary.HighRiskIssues) | <strong>Medium Risk Issues:</strong> $($Summary.MediumRiskIssues)</p>
        </div>
    </div>
    
    <div style="background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>Detailed Audit Results</h2>
        <table>
            <thead>
                <tr>
                    <th>Computer</th>
                    <th>Control ID</th>
                    <th>Control Name</th>
                    <th>Current Value</th>
                    <th>Recommended</th>
                    <th>Status</th>
                    <th>Risk Level</th>
                    <th>Remediation</th>
                </tr>
            </thead>
            <tbody>
"@

        foreach ($result in $Results) {
            $statusClass = switch ($result.ComplianceStatus) {
                "Compliant" { "status-compliant" }
                "Non-Compliant" { "status-non-compliant" }
                "Not Configured" { "status-not-configured" }
                "Error" { "status-error" }
            }
            
            $riskClass = switch ($result.RiskLevel) {
                "High" { "risk-high" }
                "Medium" { "risk-medium" }
                "Low" { "risk-low" }
            }
            
            $currentValueDisplay = if ($null -eq $result.CurrentValue) { "Not Set" } else { $result.CurrentValue }
            $remediationDisplay = if ($result.Remediation) { "<div class='remediation'>$($result.Remediation)</div>" } else { "N/A" }
            
            $html += @"
                <tr>
                    <td>$($result.ComputerName)</td>
                    <td>$($result.ControlID)</td>
                    <td>$($result.ControlName)</td>
                    <td>$currentValueDisplay</td>
                    <td>$($result.RecommendedValue)</td>
                    <td class="$statusClass">$($result.ComplianceStatus)</td>
                    <td><span class="$riskClass">$($result.RiskLevel)</span></td>
                    <td>$remediationDisplay</td>
                </tr>
"@
        }
        
        $html += @"
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 5px;">
        <h3>Report Information</h3>
        <p><strong>Framework:</strong> CIS Controls v8</p>
        <p><strong>Target OS:</strong> Windows Server 2019</p>
        <p><strong>Audit Date:</strong> $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
        <p><strong>Total Controls Evaluated:</strong> $($Summary.TotalControls)</p>
    </div>
</body>
</html>
"@

        $html | Out-File -FilePath $htmlPath -Encoding UTF8
        
        Write-AuditLog "HTML report exported to: $htmlPath" "SUCCESS"
        return $htmlPath
    }
    catch {
        Write-AuditLog "Error exporting HTML report: $($_.Exception.Message)" "ERROR"
        return $null
    }
}
