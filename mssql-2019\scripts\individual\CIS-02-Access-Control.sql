/*
================================================================================
CIS Controls v8 - Default Account Security Assessment (CIS Control 4.2)
================================================================================
Description: Verifies default SQL Server accounts are properly secured
Control ID: CIS-4.2
Risk Level: Critical
CVSS Score: 9.1

Baseline Requirement: Default 'sa' and 'guest' accounts must be disabled
Security Impact: Default accounts with known names are primary targets for 
                attackers and must be secured to prevent unauthorized access

Assessment Query: Checks status of default SQL Server accounts
Compliance Check: sa account disabled AND guest account access revoked

Remediation: ALTER LOGIN [sa] DISABLE; REVOKE CONNECT FROM [guest];
================================================================================
*/

-- CIS Control 4.2: Default Account Security Assessment
SELECT 
    'CIS-4.2' AS ControlID,
    'Default Database User Accounts' AS ControlName,
    'Critical' AS RiskLevel,
    9.1 AS CVSSScore,
    login_name,
    CASE 
        WHEN login_name = 'sa' AND is_disabled = 1 THEN 'Compliant - SA account disabled'
        WHEN login_name = 'sa' AND is_disabled = 0 THEN 'Non-Compliant - SA account enabled'
        WHEN login_name = 'guest' THEN 'Guest account present - check database permissions'
        ELSE 'Other default account found'
    END AS CurrentStatus,
    CASE 
        WHEN login_name = 'sa' THEN 'Disabled'
        WHEN login_name = 'guest' THEN 'No database access'
        ELSE 'Secured/Disabled'
    END AS BaselineValue,
    CASE 
        WHEN login_name = 'sa' AND is_disabled = 1 THEN 'Compliant'
        WHEN login_name = 'sa' AND is_disabled = 0 THEN 'Non-Compliant'
        ELSE 'Review Required'
    END AS ComplianceStatus,
    CASE 
        WHEN login_name = 'sa' AND is_disabled = 1 THEN 100
        WHEN login_name = 'sa' AND is_disabled = 0 THEN 0
        ELSE 50
    END AS ComplianceScore,
    is_disabled,
    create_date,
    modify_date,
    GETDATE() AS AssessmentDate,
    @@SERVERNAME AS ServerName
FROM sys.sql_logins 
WHERE name IN ('sa', 'guest')
   OR name LIKE '%admin%'
   OR name LIKE '%test%'
FOR JSON PATH, ROOT('DefaultAccountAssessment');

-- Detailed SA account analysis
SELECT 
    'SA Account Analysis' AS AssessmentType,
    name AS AccountName,
    is_disabled,
    CASE 
        WHEN is_disabled = 1 THEN 'Secure - SA account is disabled'
        ELSE 'Critical Risk - SA account is enabled and accessible'
    END AS SecurityStatus,
    CASE 
        WHEN is_disabled = 0 THEN 'Immediate action required - disable SA account'
        ELSE 'No action required - SA account properly disabled'
    END AS Recommendation,
    create_date,
    modify_date,
    CASE 
        WHEN password_hash IS NOT NULL THEN 'Password hash present'
        ELSE 'No password hash'
    END AS PasswordStatus,
    is_policy_checked,
    is_expiration_checked,
    CASE 
        WHEN is_disabled = 0 AND is_policy_checked = 0 THEN 'High Risk - No password policy enforcement'
        WHEN is_disabled = 0 AND is_policy_checked = 1 THEN 'Medium Risk - Password policy enforced but account enabled'
        ELSE 'Low Risk - Account disabled'
    END AS RiskAssessment
FROM sys.sql_logins 
WHERE name = 'sa'
FOR JSON PATH, ROOT('SAAccountAnalysis');

-- Guest account database access analysis
SELECT 
    'Guest Account Database Access' AS AssessmentType,
    db.name AS DatabaseName,
    CASE 
        WHEN dp.permission_name IS NOT NULL THEN 'Guest has ' + dp.permission_name + ' permission'
        ELSE 'No explicit permissions found'
    END AS GuestPermissions,
    CASE 
        WHEN dp.permission_name IS NOT NULL THEN 'Non-Compliant - Guest account has database access'
        ELSE 'Compliant - No guest access detected'
    END AS ComplianceStatus,
    CASE 
        WHEN dp.permission_name IS NOT NULL THEN 'Revoke guest account permissions: REVOKE CONNECT FROM [guest]'
        ELSE 'No action required'
    END AS Recommendation
FROM sys.databases db
LEFT JOIN sys.database_permissions dp ON dp.grantee_principal_id = USER_ID('guest')
WHERE db.database_id > 4 -- Exclude system databases
FOR JSON PATH, ROOT('GuestAccountAccess');

-- Server-level permissions for default accounts
SELECT 
    'Server-Level Permissions Analysis' AS AssessmentType,
    sp.class_desc,
    sp.permission_name,
    sp.state_desc,
    pr.name AS PrincipalName,
    pr.type_desc AS PrincipalType,
    CASE 
        WHEN pr.name IN ('sa', 'guest') AND sp.state_desc = 'GRANT' THEN 'High Risk - Default account has server permissions'
        WHEN pr.name IN ('sa', 'guest') AND sp.state_desc = 'DENY' THEN 'Good - Default account permissions denied'
        ELSE 'Review - Check if permissions are appropriate'
    END AS RiskAssessment,
    CASE 
        WHEN pr.name = 'sa' AND sp.state_desc = 'GRANT' THEN 'Consider disabling SA account instead of managing permissions'
        WHEN pr.name = 'guest' AND sp.state_desc = 'GRANT' THEN 'Revoke guest account server permissions'
        ELSE 'Review permissions alignment with security policy'
    END AS Recommendation
FROM sys.server_permissions sp
INNER JOIN sys.server_principals pr ON sp.grantee_principal_id = pr.principal_id
WHERE pr.name IN ('sa', 'guest', 'public')
   AND sp.permission_name NOT IN ('CONNECT SQL') -- Exclude basic connection permission
FOR JSON PATH, ROOT('ServerPermissionsAnalysis');

-- Additional security accounts analysis
SELECT 
    'Additional Security Accounts' AS AssessmentType,
    name AS AccountName,
    type_desc,
    is_disabled,
    create_date,
    CASE 
        WHEN name LIKE '%admin%' OR name LIKE '%test%' OR name LIKE '%demo%' THEN 'Potential security risk - review account necessity'
        WHEN is_disabled = 0 AND create_date < DATEADD(year, -1, GETDATE()) THEN 'Old account - review if still needed'
        ELSE 'Standard account'
    END AS SecurityAssessment,
    CASE 
        WHEN name LIKE '%admin%' OR name LIKE '%test%' OR name LIKE '%demo%' THEN 'Review account purpose and disable if unnecessary'
        ELSE 'Monitor account usage and apply principle of least privilege'
    END AS Recommendation
FROM sys.sql_logins 
WHERE name NOT IN ('sa', 'guest')
   AND (name LIKE '%admin%' OR name LIKE '%test%' OR name LIKE '%demo%' OR name LIKE '%temp%')
FOR JSON PATH, ROOT('AdditionalSecurityAccounts');

-- Compliance summary for default accounts
SELECT 
    'Default Accounts Compliance Summary' AS AssessmentType,
    COUNT(*) AS TotalDefaultAccounts,
    COUNT(CASE WHEN name = 'sa' AND is_disabled = 1 THEN 1 END) AS SAAccountSecured,
    COUNT(CASE WHEN name = 'sa' AND is_disabled = 0 THEN 1 END) AS SAAccountVulnerable,
    CASE 
        WHEN COUNT(CASE WHEN name = 'sa' AND is_disabled = 0 THEN 1 END) = 0 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS OverallCompliance,
    CASE 
        WHEN COUNT(CASE WHEN name = 'sa' AND is_disabled = 0 THEN 1 END) = 0 THEN 100
        ELSE 0
    END AS CompliancePercentage,
    CASE 
        WHEN COUNT(CASE WHEN name = 'sa' AND is_disabled = 0 THEN 1 END) = 0 THEN 'All default accounts properly secured'
        ELSE 'Critical: SA account enabled - immediate remediation required'
    END AS SecurityStatus
FROM sys.sql_logins 
WHERE name IN ('sa', 'guest')
FOR JSON PATH, ROOT('ComplianceSummary');

/*
================================================================================
Remediation Steps for Non-Compliant Configuration:
================================================================================

1. Disable SA Account:
   ALTER LOGIN [sa] DISABLE;

2. Revoke Guest Account Database Access:
   USE [DatabaseName];
   REVOKE CONNECT FROM [guest];

3. Verify SA Account Status:
   SELECT name, is_disabled FROM sys.sql_logins WHERE name = 'sa';

4. Check Guest Account Permissions:
   SELECT dp.permission_name, dp.state_desc 
   FROM sys.database_permissions dp 
   WHERE dp.grantee_principal_id = USER_ID('guest');

5. Additional Security Measures:
   - Rename SA account if organizational policy allows
   - Implement strong password policy for any remaining SQL logins
   - Regular audit of account permissions and access

Security Benefits:
- Eliminates default account attack vectors
- Reduces brute force attack surface
- Improves compliance with security frameworks
- Enhances audit trail clarity
- Supports principle of least privilege

Post-Remediation Monitoring:
- Monitor for attempts to enable SA account
- Regular review of guest account permissions
- Audit login attempts to default accounts
- Implement alerting for default account usage

================================================================================
*/
