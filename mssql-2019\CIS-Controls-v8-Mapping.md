# CIS Controls v8 Mapping for SQL Server 2019 Security Assessment

## 📋 Overview

This document provides detailed mapping between CIS Controls v8 and the 20 critical SQL Server 2019 security configurations assessed by this audit framework. Each control includes specific technical implementations, assessment criteria, and compliance requirements.

---

## 🔐 Authentication & Access Control (CIS Controls 4.x)

### **CIS Control 4.1 - SQL Server Authentication Mode**
- **CIS Reference:** CIS Control 4.1 - Establish and Maintain a Secure Configuration Process
- **SQL Server Setting:** Server Authentication Mode
- **Assessment Query:** `SELECT SERVERPROPERTY('IsIntegratedSecurityOnly')`
- **Baseline Value:** Windows Authentication Mode Only (1)
- **Risk Level:** High
- **Description:** Ensures SQL Server uses Windows Authentication instead of mixed mode to prevent SQL authentication vulnerabilities

### **CIS Control 4.2 - Default Database User Accounts**
- **CIS Reference:** CIS Control 4.2 - Implement a Dedicated Configuration Management System
- **SQL Server Setting:** Default user account security
- **Assessment Query:** `SELECT name, is_disabled FROM sys.sql_logins WHERE name IN ('sa', 'guest')`
- **Baseline Value:** 'sa' account disabled, 'guest' account disabled
- **Risk Level:** Critical
- **Description:** Verifies default accounts are properly secured to prevent unauthorized access

### **CIS Control 4.3 - SQL Server Service Account Configuration**
- **CIS Reference:** CIS Control 4.3 - Configure Automatic Session Locking
- **SQL Server Setting:** Service account privileges and configuration
- **Assessment Query:** Registry and service configuration checks
- **Baseline Value:** Dedicated service accounts with minimal privileges
- **Risk Level:** High
- **Description:** Ensures SQL Server services run under dedicated accounts with least privilege

### **CIS Control 4.4 - Password Policy Enforcement**
- **CIS Reference:** CIS Control 4.4 - Implement and Manage a Firewall
- **SQL Server Setting:** Password policy and expiration settings
- **Assessment Query:** `SELECT name, is_policy_checked, is_expiration_checked FROM sys.sql_logins`
- **Baseline Value:** Password policy enabled, expiration enabled
- **Risk Level:** Medium
- **Description:** Validates password complexity and expiration policies are enforced

### **CIS Control 4.5 - Login Auditing Configuration**
- **CIS Reference:** CIS Control 4.5 - Implement Network Segmentation
- **SQL Server Setting:** Login auditing level
- **Assessment Query:** `SELECT value_in_use FROM sys.configurations WHERE name = 'login auditing'`
- **Baseline Value:** Failed logins audited (2) or All logins audited (3)
- **Risk Level:** Medium
- **Description:** Ensures login attempts are properly audited for security monitoring

---

## 🔒 Encryption & Data Protection (CIS Controls 3.x)

### **CIS Control 3.1 - Transparent Data Encryption (TDE)**
- **CIS Reference:** CIS Control 3.1 - Establish and Maintain a Data Management Process
- **SQL Server Setting:** Database encryption status
- **Assessment Query:** `SELECT db.name, dek.encryption_state FROM sys.databases db LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id`
- **Baseline Value:** Encryption enabled (3) for sensitive databases
- **Risk Level:** High
- **Description:** Verifies sensitive databases are encrypted at rest using TDE

### **CIS Control 3.2 - Connection Encryption**
- **CIS Reference:** CIS Control 3.2 - Configure Data Access Control Lists
- **SQL Server Setting:** Force encryption and certificate configuration
- **Assessment Query:** Registry check for ForceEncryption setting
- **Baseline Value:** Force Encryption enabled (1)
- **Risk Level:** High
- **Description:** Ensures all client connections are encrypted in transit

### **CIS Control 3.3 - Backup Encryption**
- **CIS Reference:** CIS Control 3.3 - Configure Data Classification
- **SQL Server Setting:** Backup encryption configuration
- **Assessment Query:** `SELECT name, is_encrypted FROM msdb.dbo.backupset WHERE backup_start_date > DATEADD(day, -30, GETDATE())`
- **Baseline Value:** Recent backups encrypted (1)
- **Risk Level:** Medium
- **Description:** Validates database backups are encrypted to protect data at rest

### **CIS Control 3.4 - Certificate Management**
- **CIS Reference:** CIS Control 3.4 - Enforce Data Retention
- **SQL Server Setting:** Certificate and key management
- **Assessment Query:** `SELECT name, expiry_date, subject FROM sys.certificates`
- **Baseline Value:** Valid certificates with future expiry dates
- **Risk Level:** Medium
- **Description:** Ensures encryption certificates are properly managed and current

### **CIS Control 3.5 - Column-Level Encryption**
- **CIS Reference:** CIS Control 3.5 - Securely Dispose of Data
- **SQL Server Setting:** Always Encrypted and column encryption
- **Assessment Query:** `SELECT c.name, c.encryption_type FROM sys.columns c WHERE c.encryption_type IS NOT NULL`
- **Baseline Value:** Sensitive columns encrypted
- **Risk Level:** Medium
- **Description:** Verifies sensitive data columns are encrypted using Always Encrypted or similar

---

## 📊 Audit & Monitoring (CIS Controls 8.x)

### **CIS Control 8.1 - SQL Server Audit Configuration**
- **CIS Reference:** CIS Control 8.1 - Establish and Maintain Audit Log Management
- **SQL Server Setting:** Server audit configuration
- **Assessment Query:** `SELECT name, is_state_enabled FROM sys.server_audits`
- **Baseline Value:** At least one audit enabled (1)
- **Risk Level:** High
- **Description:** Ensures SQL Server auditing is properly configured and enabled

### **CIS Control 8.2 - Login Failure Auditing**
- **CIS Reference:** CIS Control 8.2 - Configure Centralized Audit Log Collection
- **SQL Server Setting:** Failed login audit specification
- **Assessment Query:** `SELECT audit_action_name, is_state_enabled FROM sys.server_audit_specifications_details WHERE audit_action_name LIKE '%LOGIN%'`
- **Baseline Value:** Login failures audited
- **Risk Level:** Medium
- **Description:** Validates failed login attempts are captured in audit logs

### **CIS Control 8.3 - Database Schema Change Auditing**
- **CIS Reference:** CIS Control 8.3 - Create an Audit Log Retention Policy
- **SQL Server Setting:** DDL change auditing
- **Assessment Query:** `SELECT audit_action_name FROM sys.database_audit_specifications_details WHERE audit_action_name LIKE '%SCHEMA%'`
- **Baseline Value:** Schema changes audited
- **Risk Level:** Medium
- **Description:** Ensures database structure changes are audited for compliance

### **CIS Control 8.4 - Data Access Auditing**
- **CIS Reference:** CIS Control 8.4 - Standardize Time Synchronization
- **SQL Server Setting:** Data access audit configuration
- **Assessment Query:** `SELECT audit_action_name FROM sys.database_audit_specifications_details WHERE audit_action_name IN ('SELECT', 'INSERT', 'UPDATE', 'DELETE')`
- **Baseline Value:** Sensitive data access audited
- **Risk Level:** Medium
- **Description:** Verifies access to sensitive data is properly audited

### **CIS Control 8.5 - Audit Log Security and Retention**
- **CIS Reference:** CIS Control 8.5 - Collect Detailed Audit Logs
- **SQL Server Setting:** Audit file security and retention
- **Assessment Query:** File system permissions and audit file configuration
- **Baseline Value:** Secure audit files with appropriate retention
- **Risk Level:** Medium
- **Description:** Ensures audit logs are protected and retained per policy

---

## 🌐 Network Security (CIS Controls 12.x)

### **CIS Control 12.1 - Network Protocol Configuration**
- **CIS Reference:** CIS Control 12.1 - Maintain an Inventory of Network Assets
- **SQL Server Setting:** Enabled network protocols
- **Assessment Query:** Registry check for enabled protocols
- **Baseline Value:** Only necessary protocols enabled (TCP/IP, Named Pipes disabled if not needed)
- **Risk Level:** Medium
- **Description:** Ensures only required network protocols are enabled

### **CIS Control 12.2 - Port Security Configuration**
- **CIS Reference:** CIS Control 12.2 - Establish and Maintain a Secure Network Architecture
- **SQL Server Setting:** TCP port configuration
- **Assessment Query:** `SELECT local_tcp_port FROM sys.dm_exec_connections WHERE session_id = @@SPID`
- **Baseline Value:** Non-default ports configured
- **Risk Level:** Low
- **Description:** Validates SQL Server is not using default port 1433

### **CIS Control 12.3 - Remote Access Security**
- **CIS Reference:** CIS Control 12.3 - Securely Manage Network Infrastructure
- **SQL Server Setting:** Remote access and DAC configuration
- **Assessment Query:** `SELECT name, value_in_use FROM sys.configurations WHERE name IN ('remote access', 'remote admin connections')`
- **Baseline Value:** Remote access disabled unless required, DAC enabled
- **Risk Level:** Medium
- **Description:** Ensures remote access is properly controlled and secured

---

## ⚙️ System Configuration (CIS Controls 5.x)

### **CIS Control 5.1 - Database File Security Permissions**
- **CIS Reference:** CIS Control 5.1 - Establish and Maintain an Asset Inventory
- **SQL Server Setting:** File system permissions on database files
- **Assessment Query:** File system ACL checks on .mdf, .ldf, .ndf files
- **Baseline Value:** Restricted access to SQL Server service account and administrators
- **Risk Level:** High
- **Description:** Verifies database files have appropriate file system permissions

### **CIS Control 5.2 - SQL Server Configuration Security**
- **CIS Reference:** CIS Control 5.2 - Establish and Maintain a Software Inventory
- **SQL Server Setting:** Advanced configuration options
- **Assessment Query:** `SELECT name, value_in_use FROM sys.configurations WHERE name IN ('xp_cmdshell', 'Ole Automation Procedures', 'SQL Mail XPs')`
- **Baseline Value:** Dangerous features disabled (0)
- **Risk Level:** High
- **Description:** Ensures potentially dangerous SQL Server features are disabled

---

## 📊 Compliance Matrix

| Control ID | Control Name | Risk Level | Assessment Method | Baseline Status |
|------------|--------------|------------|-------------------|-----------------|
| CIS 4.1 | Authentication Mode | High | SQL Query | Windows Auth Only |
| CIS 4.2 | Default Accounts | Critical | SQL Query | Accounts Disabled |
| CIS 4.3 | Service Accounts | High | Registry/Service | Dedicated Accounts |
| CIS 4.4 | Password Policy | Medium | SQL Query | Policy Enforced |
| CIS 4.5 | Login Auditing | Medium | SQL Query | Failures Audited |
| CIS 3.1 | TDE Encryption | High | SQL Query | Sensitive DBs Encrypted |
| CIS 3.2 | Connection Encryption | High | Registry | Force Encryption |
| CIS 3.3 | Backup Encryption | Medium | SQL Query | Backups Encrypted |
| CIS 3.4 | Certificate Management | Medium | SQL Query | Valid Certificates |
| CIS 3.5 | Column Encryption | Medium | SQL Query | Sensitive Columns |
| CIS 8.1 | Server Audit | High | SQL Query | Audit Enabled |
| CIS 8.2 | Login Failures | Medium | SQL Query | Failures Captured |
| CIS 8.3 | Schema Changes | Medium | SQL Query | DDL Audited |
| CIS 8.4 | Data Access | Medium | SQL Query | Access Audited |
| CIS 8.5 | Audit Security | Medium | File System | Logs Protected |
| CIS 12.1 | Network Protocols | Medium | Registry | Minimal Protocols |
| CIS 12.2 | Port Security | Low | SQL Query | Non-Default Ports |
| CIS 12.3 | Remote Access | Medium | SQL Query | Access Controlled |
| CIS 5.1 | File Permissions | High | File System | Restricted Access |
| CIS 5.2 | Configuration Security | High | SQL Query | Features Disabled |

---

## 🎯 Assessment Methodology

### **Data Collection Methods**
1. **SQL Queries:** Direct database and system view queries
2. **Registry Checks:** Windows registry configuration validation
3. **File System:** Database file and audit log permission verification
4. **Service Configuration:** Windows service account and privilege assessment

### **Compliance Scoring**
- **Compliant (100%):** Configuration matches CIS baseline exactly
- **Non-Compliant (0%):** Configuration deviates from security requirements
- **Partial Compliance (1-99%):** Some aspects compliant, others require attention
- **Not Applicable (N/A):** Control not relevant to current environment

### **Risk Assessment Criteria**
- **Critical:** Immediate security vulnerability requiring urgent remediation
- **High:** Significant security risk with potential for data breach
- **Medium:** Moderate security concern requiring planned remediation
- **Low:** Minor security improvement opportunity

---

**Document Version:** 1.0  
**CIS Controls Version:** v8.1  
**Last Updated:** August 17, 2025  
**Applicable To:** SQL Server 2019 (All Editions)
