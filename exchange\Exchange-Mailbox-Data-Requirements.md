# Exchange Mailbox Data Extraction Requirements

## 📊 Comprehensive Mailbox Audit Data Fields

### User Identity Information
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **DisplayName** | Exchange Mailbox | `Get-Mailbox` | Full display name of the user |
| **UserPrincipalName** | Exchange Mailbox | `Get-Mailbox` | UPN format (<EMAIL>) |
| **SamAccountName** | Exchange Mailbox | `Get-Mailbox` | Windows logon name (pre-Windows 2000) |

### Mailbox Details
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **PrimarySmtpAddress** | Exchange Mailbox | `Get-Mailbox` | Primary email address |
| **EmailAliases** | Exchange Mailbox | `Get-Mailbox` | All secondary SMTP addresses (comma-separated) |
| **MailboxType** | Exchange Mailbox | `Get-Mailbox` | UserMailbox, SharedMailbox, RoomMailbox, etc. |

### Status Information
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **AccountStatus** | Active Directory | `Get-User` | Active/Disabled based on AD account status |
| **MailboxStatus** | Exchange Mailbox | `Get-Mailbox` | Enabled/Disabled based on Exchange properties |

### Activity Tracking
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **LastLogonDate** | Mailbox Statistics | `Get-MailboxStatistics` | Last time user accessed mailbox |
| **LastEmailActivity** | Mailbox Statistics | `Get-MailboxStatistics` | Last user action in mailbox |
| **LastItemReceived** | Mailbox Statistics | `Get-MailboxStatistics` | Last time item was received |

### Lifecycle Data
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **AccountCreationDate** | Active Directory | `Get-User` | When AD account was created |
| **AccountDisabledDate** | Active Directory | `Get-User` | When account was disabled (if applicable) |
| **MailboxCreationDate** | Exchange Mailbox | `Get-Mailbox` | When mailbox was created |
| **LastModified** | Exchange Mailbox | `Get-Mailbox` | Last modification date |

### Storage Information
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **MailboxSizeMB** | Mailbox Statistics | `Get-MailboxStatistics` | Mailbox size in megabytes |
| **MailboxSizeFormatted** | Calculated | Conversion Function | Human-readable size (GB/MB) |
| **ItemCount** | Mailbox Statistics | `Get-MailboxStatistics` | Total number of items in mailbox |
| **DatabaseName** | Exchange Database | `Get-MailboxDatabase` | Name of mailbox database |
| **ServerLocation** | Exchange Database | `Get-MailboxDatabase` | Exchange server hosting the database |

### Additional Metadata
| Field Name | Data Source | PowerShell Cmdlet | Description |
|------------|-------------|-------------------|-------------|
| **OrganizationalUnit** | Exchange Mailbox | `Get-Mailbox` | AD organizational unit |
| **ExchangeVersion** | Exchange Mailbox | `Get-Mailbox` | Exchange version information |
| **AuditEnabled** | Exchange Mailbox | `Get-Mailbox` | Whether mailbox auditing is enabled |
| **AuditDate** | Script Generated | `Get-Date` | When the audit was performed |

## 🔍 Filtering Criteria Implementation

### Domain-Based Filtering
```powershell
# Filter by specific email domain
Get-Mailbox -Filter "EmailAddresses -like '*@contoso.com'"

# Multiple domain filter
Get-Mailbox -Filter "EmailAddresses -like '*@contoso.com' -or EmailAddresses -like '*@subsidiary.com'"

# Exclude specific domains
Get-Mailbox -Filter "EmailAddresses -notlike '*@external.com'"
```

### Organizational Unit Filtering
```powershell
# Filter by specific OU
Get-Mailbox -OrganizationalUnit "contoso.com/Users/<USER>"

# Multiple OUs
Get-Mailbox -OrganizationalUnit "contoso.com/Users/<USER>"
Get-Mailbox -OrganizationalUnit "contoso.com/Users/<USER>"

# Recursive OU search
Get-Mailbox -Filter "OrganizationalUnit -like '*Sales*'"
```

### Status-Based Filtering
```powershell
# Active mailboxes only
Get-Mailbox | Where-Object {(Get-User $_.Identity).RecipientTypeDetails -match "UserMailbox"}

# Include disabled accounts
Get-Mailbox -Filter "RecipientTypeDetails -eq 'DisabledUser'"

# Specific mailbox types
Get-Mailbox -RecipientTypeDetails UserMailbox,SharedMailbox,RoomMailbox
```

### Size-Based Filtering
```powershell
# Large mailboxes (>1GB)
Get-Mailbox | Where-Object {(Get-MailboxStatistics $_.Identity).TotalItemSize -gt 1GB}

# Small or empty mailboxes
Get-Mailbox | Where-Object {(Get-MailboxStatistics $_.Identity).ItemCount -lt 10}
```

### Activity-Based Filtering
```powershell
# Recently active mailboxes (last 30 days)
Get-Mailbox | Where-Object {(Get-MailboxStatistics $_.Identity).LastLogonTime -gt (Get-Date).AddDays(-30)}

# Inactive mailboxes (no activity in 90 days)
Get-Mailbox | Where-Object {(Get-MailboxStatistics $_.Identity).LastLogonTime -lt (Get-Date).AddDays(-90)}
```

## 📋 Data Extraction Methods

### Method 1: Single Cmdlet Approach
```powershell
# Basic mailbox information
$mailboxes = Get-Mailbox -ResultSize Unlimited

foreach ($mailbox in $mailboxes) {
    $stats = Get-MailboxStatistics $mailbox.Identity
    $user = Get-User $mailbox.Identity
    
    # Process data...
}
```

### Method 2: Batch Processing
```powershell
# Process in batches for large environments
$batchSize = 100
$allMailboxes = Get-Mailbox -ResultSize Unlimited
$totalBatches = [math]::Ceiling($allMailboxes.Count / $batchSize)

for ($i = 0; $i -lt $totalBatches; $i++) {
    $startIndex = $i * $batchSize
    $batch = $allMailboxes[$startIndex..($startIndex + $batchSize - 1)]
    
    # Process batch...
}
```

### Method 3: Pipeline Processing
```powershell
# Memory-efficient pipeline approach
Get-Mailbox -ResultSize Unlimited | ForEach-Object {
    $mailbox = $_
    $stats = Get-MailboxStatistics $mailbox.Identity -ErrorAction SilentlyContinue
    $user = Get-User $mailbox.Identity -ErrorAction SilentlyContinue
    
    # Create output object
    [PSCustomObject]@{
        DisplayName = $mailbox.DisplayName
        # ... other properties
    }
} | Export-Csv -Path "mailbox-audit.csv" -NoTypeInformation
```

## 🎯 Data Quality Validation

### Required Field Validation
```powershell
# Ensure all required fields are captured
$requiredFields = @(
    'DisplayName', 'UserPrincipalName', 'SamAccountName',
    'PrimarySmtpAddress', 'AccountStatus', 'MailboxStatus'
)

foreach ($record in $auditData) {
    foreach ($field in $requiredFields) {
        if ([string]::IsNullOrEmpty($record.$field)) {
            Write-Warning "Missing required field '$field' for mailbox: $($record.DisplayName)"
        }
    }
}
```

### Data Type Validation
```powershell
# Validate data types
if ($record.MailboxSizeMB -isnot [double]) {
    $record.MailboxSizeMB = 0
}

if ($record.ItemCount -isnot [int]) {
    $record.ItemCount = 0
}

# Date validation
try {
    $record.LastLogonDate = [DateTime]$record.LastLogonDate
} catch {
    $record.LastLogonDate = "Never"
}
```

### Email Address Validation
```powershell
# Validate email address format
function Test-EmailAddress {
    param([string]$Email)
    return $Email -match '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
}

if (!(Test-EmailAddress $record.PrimarySmtpAddress)) {
    Write-Warning "Invalid email format: $($record.PrimarySmtpAddress)"
}
```

## 📊 Sample Data Structure

### CSV Output Format
```csv
DisplayName,UserPrincipalName,SamAccountName,PrimarySmtpAddress,EmailAliases,AccountStatus,MailboxStatus,LastLogonDate,LastEmailActivity,LastItemReceived,AccountCreationDate,AccountDisabledDate,MailboxCreationDate,MailboxSizeMB,MailboxSizeFormatted,ItemCount,DatabaseName,ServerLocation,MailboxType,OrganizationalUnit,LastModified,ExchangeVersion,AuditEnabled,AuditDate
```

### JSON Output Format
```json
{
  "DisplayName": "John Smith",
  "UserPrincipalName": "<EMAIL>",
  "SamAccountName": "jsmith",
  "PrimarySmtpAddress": "<EMAIL>",
  "EmailAliases": "<EMAIL>, <EMAIL>",
  "AccountStatus": "Active",
  "MailboxStatus": "Enabled",
  "LastLogonDate": "2024-08-09T14:30:22Z",
  "LastEmailActivity": "2024-08-09T14:25:15Z",
  "LastItemReceived": "2024-08-09T14:25:15Z",
  "AccountCreationDate": "2023-01-15T09:00:00Z",
  "AccountDisabledDate": null,
  "MailboxCreationDate": "2023-01-15T10:30:00Z",
  "MailboxSizeMB": 2048.50,
  "MailboxSizeFormatted": "2.00 GB",
  "ItemCount": 15420,
  "DatabaseName": "MDB01",
  "ServerLocation": "EXCHANGE01",
  "MailboxType": "UserMailbox",
  "OrganizationalUnit": "contoso.com/Users",
  "LastModified": "2024-08-09T14:30:22Z",
  "ExchangeVersion": "15.1.2375.7",
  "AuditEnabled": true,
  "AuditDate": "2024-08-10T01:20:53Z"
}
```

## 🔧 Performance Optimization

### Efficient Data Retrieval
```powershell
# Use specific properties to reduce data transfer
Get-Mailbox -ResultSize Unlimited | Select-Object DisplayName, UserPrincipalName, PrimarySmtpAddress

# Batch statistics retrieval
$mailboxes | ForEach-Object -Parallel {
    Get-MailboxStatistics $_.Identity
} -ThrottleLimit 10
```

### Memory Management
```powershell
# Clear variables periodically
if ($processedCount % 1000 -eq 0) {
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
```

### Error Handling
```powershell
# Robust error handling for data collection
try {
    $stats = Get-MailboxStatistics $mailbox.Identity -ErrorAction Stop
} catch {
    Write-Warning "Failed to get statistics for $($mailbox.Identity): $($_.Exception.Message)"
    $stats = $null
}
```

## 📈 Reporting and Analytics

### Summary Statistics
- Total mailboxes audited
- Active vs. disabled accounts
- Mailbox type distribution
- Size distribution analysis
- Activity patterns

### Compliance Reporting
- Mailboxes without auditing enabled
- Oversized mailboxes
- Inactive accounts
- Missing required fields

### Migration Planning Data
- Total data volume
- Database distribution
- Server capacity analysis
- User activity patterns

---

**Document Version:** 1.0  
**Last Updated:** August 10, 2024  
**Next Review:** November 10, 2024  

*This document defines the comprehensive data requirements for Exchange mailbox auditing and should be referenced when developing or modifying audit scripts.*
