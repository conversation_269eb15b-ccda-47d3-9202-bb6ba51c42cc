# Test Local AI Model API Connection
# Testing http://***********:1234/v1 for OpenAI compatibility

Write-Host "Testing Local AI Model API Connection..." -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Test 1: Check available models
Write-Host "`n1. Testing /v1/models endpoint..." -ForegroundColor Yellow
try {
    $modelsResponse = Invoke-RestMethod -Uri "http://***********:1234/v1/models" -Method Get
    Write-Host "✅ Models endpoint successful!" -ForegroundColor Green
    Write-Host "Available models:" -ForegroundColor Cyan
    $modelsResponse.data | ForEach-Object {
        Write-Host "  - $($_.id)" -ForegroundColor White
    }
    $modelName = $modelsResponse.data[0].id
    Write-Host "Using model: $modelName" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Models endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Basic chat completion
Write-Host "`n2. Testing /v1/chat/completions endpoint..." -ForegroundColor Yellow
try {
    $headers = @{
        'Content-Type' = 'application/json'
    }
    
    $requestBody = @{
        model = $modelName
        messages = @(
            @{
                role = "user"
                content = "Hello! Can you help me analyze PowerShell code for security issues?"
            }
        )
        max_tokens = 150
        temperature = 0.3
    } | ConvertTo-Json -Depth 3
    
    Write-Host "Sending request to chat completions..." -ForegroundColor Gray
    $chatResponse = Invoke-RestMethod -Uri "http://***********:1234/v1/chat/completions" -Method Post -Headers $headers -Body $requestBody
    
    Write-Host "✅ Chat completions successful!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    Write-Host $chatResponse.choices[0].message.content -ForegroundColor White
    
    # Display response metadata
    Write-Host "`nResponse Metadata:" -ForegroundColor Yellow
    Write-Host "  Model: $($chatResponse.model)" -ForegroundColor Gray
    Write-Host "  Usage - Prompt tokens: $($chatResponse.usage.prompt_tokens)" -ForegroundColor Gray
    Write-Host "  Usage - Completion tokens: $($chatResponse.usage.completion_tokens)" -ForegroundColor Gray
    Write-Host "  Usage - Total tokens: $($chatResponse.usage.total_tokens)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Chat completions failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception)" -ForegroundColor Red
    exit 1
}

# Test 3: Code analysis capability test
Write-Host "`n3. Testing code analysis capability..." -ForegroundColor Yellow
try {
    $codeAnalysisBody = @{
        model = $modelName
        messages = @(
            @{
                role = "system"
                content = "You are a cybersecurity expert specializing in PowerShell script analysis. Analyze code for security vulnerabilities, best practices, and potential risks."
            },
            @{
                role = "user"
                content = @"
Analyze this PowerShell code for security issues:

```powershell
param([string]`$UserInput)
Invoke-Expression "Get-Process | Where-Object {`$_.Name -eq '`$UserInput'}"
```
"@
            }
        )
        max_tokens = 300
        temperature = 0.1
    } | ConvertTo-Json -Depth 3
    
    $analysisResponse = Invoke-RestMethod -Uri "http://***********:1234/v1/chat/completions" -Method Post -Headers $headers -Body $codeAnalysisBody
    
    Write-Host "✅ Code analysis successful!" -ForegroundColor Green
    Write-Host "Security Analysis Result:" -ForegroundColor Cyan
    Write-Host $analysisResponse.choices[0].message.content -ForegroundColor White
}
catch {
    Write-Host "❌ Code analysis test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Performance and response time
Write-Host "`n4. Testing performance..." -ForegroundColor Yellow
try {
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    $perfTestBody = @{
        model = $modelName
        messages = @(
            @{
                role = "user"
                content = "What are the top 5 security best practices for PowerShell scripts?"
            }
        )
        max_tokens = 200
        temperature = 0.2
    } | ConvertTo-Json -Depth 3
    
    $perfResponse = Invoke-RestMethod -Uri "http://***********:1234/v1/chat/completions" -Method Post -Headers $headers -Body $perfTestBody
    
    $stopwatch.Stop()
    $responseTime = $stopwatch.ElapsedMilliseconds
    
    Write-Host "✅ Performance test completed!" -ForegroundColor Green
    Write-Host "Response time: $responseTime ms" -ForegroundColor Cyan
    Write-Host "Tokens per second: $([math]::Round($perfResponse.usage.completion_tokens / ($responseTime / 1000), 2))" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Performance test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=========================================" -ForegroundColor Green
Write-Host "API Testing Complete!" -ForegroundColor Green
Write-Host "Local AI model is ready for integration." -ForegroundColor Green
