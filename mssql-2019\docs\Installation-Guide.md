# Microsoft SQL Server 2019 CIS Controls v8 Audit Framework - Installation Guide

## 📋 Overview

This guide provides step-by-step instructions for installing and configuring the SQL Server 2019 CIS Controls v8 compliance audit framework. The framework is designed for easy deployment with minimal prerequisites and maximum compatibility.

## 🎯 Prerequisites

### **System Requirements**
- **Operating System:** Windows Server 2016/2019/2022 or Windows 10/11
- **PowerShell:** Version 5.1 or later
- **SQL Server:** 2019 (Express, Standard, or Enterprise editions)
- **Memory:** Minimum 4GB RAM (8GB recommended)
- **Disk Space:** 500MB free space for framework and reports

### **Software Dependencies**
- **SqlServer PowerShell Module:** Required for SQL connectivity
- **Windows PowerShell ISE or VS Code:** Recommended for script editing
- **Web Browser:** For viewing HTML reports (Chrome, Firefox, Edge)

### **Network Requirements**
- **SQL Server Access:** TCP connectivity to target SQL Server instances
- **Port Access:** Default SQL Server port 1433 (or custom configured ports)
- **Firewall:** Windows Firewall exceptions for SQL Server connectivity

## 🚀 Installation Steps

### **Step 1: Download and Extract Framework**

1. **Download Framework Files**
   ```powershell
   # If downloading from repository
   git clone https://github.com/your-org/sql-server-cis-audit.git
   cd sql-server-cis-audit
   ```

2. **Extract to Target Directory**
   ```powershell
   # Extract to recommended location
   $InstallPath = "C:\Tools\SQL-CIS-Audit"
   New-Item -Path $InstallPath -ItemType Directory -Force
   Copy-Item -Path ".\mssql-2019\*" -Destination $InstallPath -Recurse -Force
   ```

3. **Verify Directory Structure**
   ```powershell
   Get-ChildItem -Path $InstallPath -Recurse | Select-Object Name, FullName
   ```

### **Step 2: Install PowerShell Dependencies**

1. **Install SqlServer Module**
   ```powershell
   # Run as Administrator
   Install-Module -Name SqlServer -Force -AllowClobber
   
   # Verify installation
   Get-Module -Name SqlServer -ListAvailable
   ```

2. **Import Required Modules**
   ```powershell
   Import-Module SqlServer
   Import-Module Microsoft.PowerShell.Security
   ```

3. **Set Execution Policy (if needed)**
   ```powershell
   # Check current policy
   Get-ExecutionPolicy
   
   # Set policy to allow script execution
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### **Step 3: Configure SQL Server Permissions**

1. **Create Audit User Account (Recommended)**
   ```sql
   -- Connect to SQL Server as administrator
   USE master;
   
   -- Create login for audit user
   CREATE LOGIN [DOMAIN\AuditUser] FROM WINDOWS;
   
   -- Grant minimum required permissions
   GRANT VIEW SERVER STATE TO [DOMAIN\AuditUser];
   GRANT VIEW ANY DEFINITION TO [DOMAIN\AuditUser];
   
   -- Grant database access for each database to audit
   USE [DatabaseName];
   CREATE USER [DOMAIN\AuditUser] FOR LOGIN [DOMAIN\AuditUser];
   ALTER ROLE db_datareader ADD MEMBER [DOMAIN\AuditUser];
   ```

2. **Alternative: Use Existing Administrative Account**
   ```sql
   -- Verify existing account has required permissions
   SELECT 
       p.permission_name,
       p.state_desc,
       pr.name AS principal_name
   FROM sys.server_permissions p
   INNER JOIN sys.server_principals pr ON p.grantee_principal_id = pr.principal_id
   WHERE pr.name = 'YourExistingAccount';
   ```

### **Step 4: Configure Windows Permissions**

1. **Registry Read Access**
   ```powershell
   # Verify registry access for SQL Server configuration
   $RegPath = "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server"
   Test-Path $RegPath
   ```

2. **File System Access**
   ```powershell
   # Verify access to SQL Server installation directory
   $SQLPath = "C:\Program Files\Microsoft SQL Server"
   Test-Path $SQLPath
   Get-Acl $SQLPath
   ```

3. **Event Log Access**
   ```powershell
   # Verify Windows Event Log read access
   Get-EventLog -LogName Application -Newest 1 -Source "MSSQL*"
   ```

### **Step 5: Framework Configuration**

1. **Review Default Configuration**
   ```powershell
   $ConfigPath = "$InstallPath\config\CIS-Baseline-Config.json"
   $Config = Get-Content $ConfigPath | ConvertFrom-Json
   $Config.metadata
   ```

2. **Customize Configuration (Optional)**
   ```powershell
   # Copy default configuration for customization
   Copy-Item $ConfigPath "$InstallPath\config\Custom-Audit-Settings.json"
   
   # Edit custom configuration as needed
   notepad "$InstallPath\config\Custom-Audit-Settings.json"
   ```

3. **Create Output Directory**
   ```powershell
   $OutputPath = "$InstallPath\output\reports"
   New-Item -Path $OutputPath -ItemType Directory -Force
   ```

## ✅ Verification and Testing

### **Step 1: Test SQL Server Connectivity**

```powershell
# Navigate to framework directory
cd $InstallPath

# Test basic connectivity
$ServerInstance = "localhost"  # Replace with your server
$TestQuery = "SELECT @@SERVERNAME AS ServerName, @@VERSION AS Version"

try {
    $Result = Invoke-Sqlcmd -ServerInstance $ServerInstance -Query $TestQuery
    Write-Host "✓ SQL Server connectivity successful" -ForegroundColor Green
    Write-Host "Server: $($Result.ServerName)" -ForegroundColor Cyan
    Write-Host "Version: $($Result.Version.Split("`n")[0])" -ForegroundColor Cyan
}
catch {
    Write-Host "✗ SQL Server connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}
```

### **Step 2: Test Framework Components**

```powershell
# Test individual SQL script
$TestScript = "$InstallPath\scripts\individual\CIS-01-Authentication.sql"
if (Test-Path $TestScript) {
    Write-Host "✓ Individual scripts found" -ForegroundColor Green
} else {
    Write-Host "✗ Individual scripts missing" -ForegroundColor Red
}

# Test PowerShell orchestration script
$MainScript = "$InstallPath\scripts\PowerShell\Invoke-CISAudit.ps1"
if (Test-Path $MainScript) {
    Write-Host "✓ PowerShell orchestration script found" -ForegroundColor Green
} else {
    Write-Host "✗ PowerShell orchestration script missing" -ForegroundColor Red
}
```

### **Step 3: Run Test Audit**

```powershell
# Execute test audit with limited scope
.\scripts\PowerShell\Invoke-CISAudit.ps1 -ServerInstance "localhost" -ControlFilter "CIS-4.1" -OutputPath ".\output\test"
```

## 🔧 Configuration Options

### **Custom Baseline Configuration**

1. **Modify Compliance Thresholds**
   ```json
   {
     "reportingSettings": {
       "complianceThresholds": {
         "compliant": 100,
         "partiallyCompliant": 75,
         "nonCompliant": 0
       }
     }
   }
   ```

2. **Customize Risk Severity Mapping**
   ```json
   {
     "reportingSettings": {
       "riskSeverityMapping": {
         "critical": {
           "cvssRange": [9.0, 10.0],
           "color": "#DC3545"
         }
       }
     }
   }
   ```

### **Output Format Configuration**

```json
{
  "reportingSettings": {
    "outputFormats": {
      "csv": {
        "enabled": true,
        "filename": "CIS-Compliance-Report-{timestamp}.csv"
      },
      "html": {
        "enabled": true,
        "filename": "CIS-Compliance-Report-{timestamp}.html"
      },
      "json": {
        "enabled": true,
        "filename": "CIS-Compliance-Report-{timestamp}.json"
      }
    }
  }
}
```

## 🛡️ Security Considerations

### **Principle of Least Privilege**
- Use dedicated audit account with minimal permissions
- Avoid using administrative accounts for routine audits
- Regularly review and rotate audit account credentials

### **Network Security**
- Use encrypted connections to SQL Server
- Implement network segmentation for audit systems
- Monitor audit account usage and access patterns

### **Data Protection**
- Secure audit reports containing sensitive configuration data
- Implement appropriate retention policies for audit logs
- Encrypt audit reports if transmitted over networks

## 📞 Support and Troubleshooting

### **Common Installation Issues**

1. **SqlServer Module Installation Fails**
   ```powershell
   # Try alternative installation method
   Install-Module -Name SqlServer -Repository PSGallery -Force -Scope CurrentUser
   ```

2. **PowerShell Execution Policy Restrictions**
   ```powershell
   # Bypass execution policy for specific script
   PowerShell.exe -ExecutionPolicy Bypass -File ".\Invoke-CISAudit.ps1"
   ```

3. **SQL Server Connection Issues**
   ```powershell
   # Test with explicit credentials
   $Credential = Get-Credential
   Invoke-Sqlcmd -ServerInstance "ServerName" -Credential $Credential -Query "SELECT 1"
   ```

### **Validation Checklist**

- [ ] PowerShell 5.1+ installed and accessible
- [ ] SqlServer module installed and imported successfully
- [ ] SQL Server connectivity established
- [ ] Required SQL Server permissions granted
- [ ] Windows registry and file system access verified
- [ ] Framework directory structure complete
- [ ] Configuration files present and valid
- [ ] Output directory created and writable
- [ ] Test audit execution successful

### **Getting Help**

- **Documentation:** Review troubleshooting guide for specific issues
- **Logs:** Check PowerShell execution logs and SQL Server error logs
- **Community:** Consult PowerShell and SQL Server community forums
- **Support:** Contact IT Security & Compliance team for assistance

---

## 🎯 Next Steps

After successful installation:

1. **Review Configuration:** Customize baseline settings for your environment
2. **Schedule Audits:** Set up regular compliance assessments
3. **Integrate Reports:** Connect audit outputs to GRC tools or dashboards
4. **Train Users:** Provide training on framework usage and report interpretation
5. **Monitor Performance:** Track audit execution times and optimize as needed

**Installation Complete!** Your SQL Server 2019 CIS Controls v8 audit framework is ready for production use.
