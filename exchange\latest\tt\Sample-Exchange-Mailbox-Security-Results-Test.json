{"AuditMetadata": {"AuditID": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "AuditStartTime": "2025-09-11 14:30:15", "ScriptVersion": "1.6.6", "ScriptReleaseDate": "September 11, 2025", "ScriptAuthor": "E.Z. Consultancy", "PowerShellVersion": "5.1.19041.4648", "PowerShellEdition": "Desktop", "OrganizationName": "Contoso Corporation", "ExchangeServers": 3, "TotalMailboxes": 1247, "MaxMailboxSample": 500, "AssessmentScope": "5 Critical Mailbox Security Controls", "AuditUser": "admin.user", "ComputerName": "EX-MGMT-01", "DomainFilterEnabled": true, "FilteredDomains": ["contoso.com", "subsidiary.contoso.com"], "DomainFilterScope": "Domain-specific audit for: contoso.com, subsidiary.contoso.com"}, "ComplianceSummary": {"AssessmentType": "Exchange Server Mailbox Security Controls Audit", "TotalControlsAssessed": 5, "SuccessfulAssessments": 4, "FailedAssessments": 1, "AuditStartTime": "2025-09-11 14:30:15", "AuditEndTime": "2025-09-11 14:45:32", "AuditDurationSeconds": 917.45, "AuditID": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "FrameworkVersion": "Exchange Mailbox Security Controls v1.0", "AuditType": "Read-Only Assessment - Production Safe", "AuditExecutedBy": "admin.user", "ComputerName": "EX-MGMT-01", "PowerShellVersion": "5.1.19041.4648"}, "MBX_1_1_ImpersonationRights": {"ControlID": "MBX-1.1", "ControlName": "Mailbox Impersonation Rights", "RiskLevel": "Critical", "CVSSScore": 9.3, "TotalImpersonationAssignments": 3, "ImpersonationUsers": [{"RoleAssignee": "CONTOSO\\svc-exchange-backup", "RoleAssigneeName": "Exchange Backup Service", "RoleAssigneeType": "User", "AssignmentMethod": "Direct", "IsValid": true, "WhenCreated": "2024-03-15 09:22:14"}, {"RoleAssignee": "CONTOSO\\svc-monitoring", "RoleAssigneeName": "Exchange Monitoring Service", "RoleAssigneeType": "User", "AssignmentMethod": "Direct", "IsValid": true, "WhenCreated": "2024-06-20 11:45:33"}, {"RoleAssignee": "<EMAIL>", "RoleAssigneeName": "Exchange Administrator", "RoleAssigneeType": "User", "AssignmentMethod": "RoleGroup", "IsValid": true, "WhenCreated": "2024-01-10 08:15:22"}], "CurrentValue": "3 accounts with ApplicationImpersonation rights", "BaselineValue": "Minimal impersonation rights with regular review", "ComplianceStatus": "Review Required", "ComplianceScore": 75, "Finding": "Limited impersonation rights - review assignments", "Recommendation": "Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions", "AssessmentDate": "2025-09-11 14:30:15"}, "MBX_2_1_FullAccessPermissions": {"ControlID": "MBX-2.1", "ControlName": "Mailbox Full Access Permissions", "RiskLevel": "High", "CVSSScore": 8.1, "SampleSize": 500, "TotalFullAccessPermissions": 47, "FullAccessPermissions": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "<PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Human Resources", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Marketing Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "<EMAIL>", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}], "UniqueUsersWithFullAccess": 6, "CurrentValue": "47 Full Access permissions found across 500 mailboxes", "BaselineValue": "Minimal Full Access delegations with business justification", "ComplianceStatus": "Review Required", "ComplianceScore": 75, "Finding": "High number of Full Access permissions - review required", "Recommendation": "Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.", "AssessmentDate": "2025-09-11 14:30:15", "CrossDomainAnalysis": {"CrossDomainRelationships": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Marketing Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "subsidiary.contoso.com", "AdminUser": "<EMAIL>", "AdminDomain": "contoso.com", "AdminClassification": "Exchange-Administrator", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 6, "RiskLevel": "High", "PermissionType": "Direct"}], "PermissionSummary": {"contoso.com -> subsidiary.contoso.com": {"Count": 1, "AdminDomain": "contoso.com", "MailboxDomain": "subsidiary.contoso.com", "HighRiskCount": 1, "AdminTypes": {"Exchange-Administrator": 1}}}, "RiskAssessment": {"TotalPermissionRelationships": 47, "CrossDomainRelationships": 1, "HighRiskRelationships": 1, "CrossDomainPercentage": 2.13, "RiskDistribution": {"High": 1, "Medium": 0, "Low": 0}}}}, "MBX_3_1_AuditLogging": {"ControlID": "MBX-3.1", "ControlName": "Mailbox Audit Logging Configuration", "RiskLevel": "Medium", "CVSSScore": 6.8, "AdminAuditLogEnabled": true, "MailboxAuditBypassUsers": [{"Name": "svc-exchange-backup", "AuditBypassEnabled": true}, {"Name": "svc-monitoring", "AuditBypassEnabled": true}], "AuditBypassCount": 2, "SampleMailboxAuditSettings": [{"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Chief Executive Officer", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Finance Department", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}], "AuditEnabledMailboxes": 48, "SampleSize": 50, "CurrentValue": "Admin audit: True, Mailbox audit enabled: 48 of 50 sampled", "BaselineValue": "Admin and mailbox audit logging enabled organization-wide", "ComplianceStatus": "Compliant", "ComplianceScore": 100, "Finding": "Admin audit logging enabled", "Recommendation": "Ensure mailbox audit is enabled for all mailboxes", "AssessmentDate": "2025-09-11 14:30:15"}, "MBX_4_1_SendAsPermissions": {"Error": "Failed to assess Send-As permissions: Access denied when querying AD permissions. Insufficient privileges to read Active Directory extended rights."}, "MBX_5_1_SendOnBehalfPermissions": {"ControlID": "MBX-5.1", "ControlName": "Send-On-Behalf Permissions", "RiskLevel": "Medium", "CVSSScore": 6.2, "SampleSize": 500, "TotalSendOnBehalfPermissions": 23, "SendOnBehalfPermissions": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "<EMAIL>", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "<EMAIL>", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Human Resources", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "<EMAIL>", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "<EMAIL>", "PermissionType": "Send-On-Behalf"}], "UniqueUsersWithSendOnBehalf": 4, "CurrentValue": "23 Send-On-Behalf permissions found across 500 mailboxes", "BaselineValue": "Minimal Send-On-Behalf permissions with business justification", "ComplianceStatus": "Compliant", "ComplianceScore": 90, "Finding": "Send-On-Behalf permissions appear reasonable", "Recommendation": "Regularly review Send-On-Behalf permissions and ensure they align with current business needs", "AssessmentDate": "2025-09-11 14:30:15"}, "AdministratorDiscovery": {"RoleAssignments": [{"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "Organization Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "Server Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "Recipient Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Mailbox-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "View-Only Organization Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "subsidiary.contoso.com", "AssigneeType": "User", "Role": "Recipient Management", "RoleType": "Management", "Scope": {"Type": "Custom-Scope", "AffectedDomains": ["subsidiary.contoso.com"], "IsOrganizationWide": false}, "IsEnabled": true, "AdminClassification": "Mailbox-Administrator", "AssignmentMethod": "Direct"}, {"RoleAssignee": "CONTOSO\\svc-exchange-backup", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "ApplicationImpersonation", "RoleType": "Application", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "Direct"}, {"RoleAssignee": "CONTOSO\\svc-monitoring", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "View-Only Organization Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "Discovery Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "contoso.com", "AssigneeType": "User", "Role": "Security Administrator", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "<EMAIL>", "AssigneeDomain": "partner.com", "AssigneeType": "User", "Role": "View-Only Organization Management", "RoleType": "Management", "Scope": {"Type": "Custom-Scope", "AffectedDomains": ["contoso.com"], "IsOrganizationWide": false}, "IsEnabled": false, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "Direct"}], "AdminDomainMap": {"contoso.com": [{"RoleAssignee": "<EMAIL>", "AdminClassification": "Exchange-Administrator"}, {"RoleAssignee": "<EMAIL>", "AdminClassification": "Exchange-Administrator"}, {"RoleAssignee": "<EMAIL>", "AdminClassification": "Mailbox-Administrator"}, {"RoleAssignee": "<EMAIL>", "AdminClassification": "Read-Only-Administrator"}, {"RoleAssignee": "CONTOSO\\svc-exchange-backup", "AdminClassification": "Exchange-Administrator"}, {"RoleAssignee": "CONTOSO\\svc-monitoring", "AdminClassification": "Read-Only-Administrator"}, {"RoleAssignee": "<EMAIL>", "AdminClassification": "Read-Only-Administrator"}, {"RoleAssignee": "<EMAIL>", "AdminClassification": "Exchange-Administrator"}], "subsidiary.contoso.com": [{"RoleAssignee": "<EMAIL>", "AdminClassification": "Mailbox-Administrator"}], "partner.com": [{"RoleAssignee": "<EMAIL>", "AdminClassification": "Read-Only-Administrator"}]}, "CrossDomainRelationships": [{"AdminDomain": "partner.com", "TargetDomain": "contoso.com", "RelationshipType": "External-Consultant", "RiskLevel": "Medium", "IsEnabled": false}], "AdminClassification": {"<EMAIL>": "Read-Only-Administrator", "CONTOSO\\svc-exchange-backup": "Exchange-Administrator", "CONTOSO\\svc-monitoring": "Read-Only-Administrator"}}}