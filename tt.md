# IT & Cybersecurity Audit Governance - Hand-off Document

## 1. Objective

This document summarizes a discussion on establishing effective IT and Information Security (IS) audit and governance controls. It covers two primary scenarios: a centralized model for IT standardization and a decentralized/federated model for subsidiary oversight.

---

## 2. Foundational Framework: CISA Domains

Our discussion began by referencing the domains of a Certified Information Systems Auditor (CISA) as a comprehensive framework for IT/IS audit activities:

1.  The Process of Auditing Information Systems
2.  Governance and Management of IT
3.  Information Systems Acquisition, Development, and Implementation
4.  Information Systems Operations and Business Resilience
5.  Protection of Information Assets

---

## 3. Scenario 1: Controls for Centralized IT Standardization

**Objective:** To make the following committee charter clause effective and traceable: *"The Committee is responsible for developing an approved list of software, hardware, and services that XYZ units must first consider..."*

To achieve this, a system of controls and documentation is required across five key areas:

*   **Foundational Governance:**
    *   **Controls:** Formally approved charter, a high-level IT standardization policy, and regular committee meetings.
    *   **Documentation:** Signed charter, meeting minutes, and the policy document.

*   **List Management Process:**
    *   **Controls:** A Standard Operating Procedure (SOP) for the lifecycle of list items, clear evaluation criteria (technical, security, cost), and segregation of duties.
    *   **Documentation:** The approved lists (version controlled), the SOP itself, evaluation checklists, and assessment reports for each item.

*   **Enforcement and Compliance:**
    *   **Controls:** Integration with the procurement process, automated asset discovery/inventory tools, and regular compliance audits.
    *   **Documentation:** Updated procurement procedures, purchase orders showing compliance, inventory reports, and audit findings.

*   **Exception Handling:**
    *   **Controls:** A formal, documented process for requesting, approving, and time-bounding exceptions, including formal risk acceptance.
    *   **Documentation:** Exception request forms and an exception register/log.

*   **Measuring Effectiveness:**
    *   **Controls:** A benefits realization plan and regular reporting to management.
    *   **Documentation:** Cost-benefit analyses, financial reports showing savings, and Key Performance Indicators (KPIs) tracking standardization progress.

---

## 4. Scenario 2: Minimum Controls for Lean Subsidiary Oversight

**Objective:** To provide effective IT/IS oversight from a head office with a single manager, without getting involved in the day-to-day operations of the subsidiaries.

The recommended approach is a lean, federated governance model based on three pillars:

*   **1. Mandate a Common Governance Framework:**
    *   **Control:** Require each subsidiary to formally adopt a recognized IT control framework (e.g., NIST CSF, CIS Controls, ISO 27001).
    *   **Control:** Establish a high-level, group-wide policy suite covering non-negotiable principles (e.g., requirement for annual risk assessments, incident response plans).

*   **2. Implement Standardized Reporting & Attestation:**
    *   **Control:** Require a standardized **Quarterly IT Risk & Compliance Report** from each subsidiary using a common template.
    *   **Control:** Define a minimum set of KPIs and Key Risk Indicators (KRIs) to be included in the report for at-a-glance oversight.
    *   **Control:** Require an annual written attestation from subsidiary leadership, confirming compliance and the accuracy of reports.

*   **3. Require Independent Assurance:**
    *   **Control:** Mandate periodic independent IT audits (internal or external) for each subsidiary.
    *   **Control:** Grant the head office manager the right to receive and review the full audit reports and any associated findings.
    *   **Control:** Require and track remediation plans for all high-risk findings.

---

## 5. Appendix: Sample Quarterly IT Risk & Compliance Report Template

This template is a practical example for implementing the "Standardized Reporting" control in the lean governance model.

| Metric                                | Target      | This Quarter | Trend (vs. last qtr) | Notes                               |
| ------------------------------------- | ----------- | ------------ | -------------------- | ----------------------------------- |
| **Security Incidents**                |             |              |                      |                                     |
| \- Number of Major Incidents          | < 1         |              | ▲ / ▼ / –            | [Brief note on any major incidents] |
| \- Mean Time to Resolve (Hours)       | < 24 hrs    |              | ▲ / ▼ / –            |                                     |
| **Vulnerability Management**          |             |              |                      |                                     |
| \- Critical Vulns Patched < 30 Days   | > 95%       |              | ▲ / ▼ / –            |                                     |
| \- High-Risk Vulns Older than 90 Days | 0           |              | ▲ / ▼ / –            |                                     |
| **Audit & Compliance**                |             |              |                      |                                     |
| \- Open High-Risk Audit Findings      | < 3         |              | ▲ / ▼ / –            |                                     |
| \- Remediation Plans Past Due         | 0           |              | ▲ / ▼ / –            |                                     |
| **Business Continuity**               |             |              |                      |                                     |
| \- Last DR Test Date & Result         | > 12 months |              | N/A                  | [e.g., "Oct 2025 - Successful"]     |