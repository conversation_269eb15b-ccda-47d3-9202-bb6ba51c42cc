{"metadata": {"version": "1.0", "cisControlsVersion": "v8.1", "sqlServerVersion": "2019", "lastUpdated": "2025-08-17", "description": "CIS Controls v8 baseline configuration values for SQL Server 2019 security assessment"}, "authenticationAccessControl": {"CIS_4_1_AuthenticationMode": {"controlId": "CIS-4.1", "controlName": "SQL Server Authentication Mode", "baselineValue": 1, "baselineDescription": "Windows Authentication Mode Only", "riskLevel": "High", "cvssScore": 7.5, "query": "SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') AS AuthMode", "complianceCheck": "value = 1", "remediation": "Use SQL Server Configuration Manager to set Authentication Mode to Windows Authentication only"}, "CIS_4_2_DefaultAccounts": {"controlId": "CIS-4.2", "controlName": "Default Database User Accounts", "baselineValue": {"saAccountDisabled": true, "guestAccountDisabled": true}, "baselineDescription": "Default 'sa' and 'guest' accounts must be disabled", "riskLevel": "Critical", "cvssScore": 9.1, "query": "SELECT name, is_disabled FROM sys.sql_logins WHERE name IN ('sa', 'guest')", "complianceCheck": "all accounts disabled = 1", "remediation": "ALTER LOGIN [sa] DISABLE; REVOKE CONNECT FROM [guest]"}, "CIS_4_3_ServiceAccounts": {"controlId": "CIS-4.3", "controlName": "SQL Server Service Account Configuration", "baselineValue": "DedicatedServiceAccount", "baselineDescription": "SQL Server services must run under dedicated service accounts", "riskLevel": "High", "cvssScore": 6.8, "query": "Registry and service configuration check", "complianceCheck": "service account != LocalSystem AND service account != Administrator", "remediation": "Configure dedicated service accounts with minimal privileges using SQL Server Configuration Manager"}, "CIS_4_4_PasswordPolicy": {"controlId": "CIS-4.4", "controlName": "Password Policy Enforcement", "baselineValue": {"policyChecked": true, "expirationChecked": true}, "baselineDescription": "Password policy and expiration must be enforced for SQL logins", "riskLevel": "Medium", "cvssScore": 5.3, "query": "SELECT name, is_policy_checked, is_expiration_checked FROM sys.sql_logins WHERE type = 'S'", "complianceCheck": "is_policy_checked = 1 AND is_expiration_checked = 1", "remediation": "ALTER LOGIN [<PERSON>gin<PERSON><PERSON>] WITH CHECK_POLICY = ON, CHECK_EXPIRATION = ON"}, "CIS_4_5_LoginAuditing": {"controlId": "CIS-4.5", "controlName": "Login Auditing Configuration", "baselineValue": 2, "baselineDescription": "Failed logins must be audited (minimum requirement)", "riskLevel": "Medium", "cvssScore": 4.7, "query": "EXEC xp_loginconfig 'audit level'", "complianceCheck": "value >= 2", "remediation": "Use SQL Server Management Studio to enable 'Failed logins only' or 'Both failed and successful logins'"}}, "encryptionDataProtection": {"CIS_3_1_TransparentDataEncryption": {"controlId": "CIS-3.1", "controlName": "Transparent Data Encryption (TDE)", "baselineValue": 3, "baselineDescription": "Sensitive databases must be encrypted using TDE", "riskLevel": "High", "cvssScore": 7.2, "query": "SELECT db.name, ISNULL(dek.encryption_state, 0) AS encryption_state FROM sys.databases db LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id WHERE db.name NOT IN ('master', 'model', 'msdb', 'tempdb')", "complianceCheck": "encryption_state = 3 for sensitive databases", "remediation": "CREATE DATABASE ENCRYPTION KEY WITH ALGORITHM = AES_256; ALTER DATABASE [DatabaseName] SET ENCRYPTION ON"}, "CIS_3_2_ConnectionEncryption": {"controlId": "CIS-3.2", "controlName": "Connection Encryption Configuration", "baselineValue": 1, "baselineDescription": "Force encryption must be enabled for all client connections", "riskLevel": "High", "cvssScore": 6.9, "query": "Registry check: HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Microsoft SQL Server\\MSSQL15.MSSQLSERVER\\MSSQLServer\\SuperSocketNetLib\\ForceEncryption", "complianceCheck": "ForceEncryption = 1", "remediation": "Use SQL Server Configuration Manager to enable 'Force Encryption' option"}, "CIS_3_3_BackupEncryption": {"controlId": "CIS-3.3", "controlName": "Backup Encryption Settings", "baselineValue": true, "baselineDescription": "Database backups must be encrypted", "riskLevel": "Medium", "cvssScore": 5.8, "query": "SELECT TOP 10 database_name, is_encrypted, backup_start_date FROM msdb.dbo.backupset WHERE backup_start_date > DATEADD(day, -30, GETDATE()) ORDER BY backup_start_date DESC", "complianceCheck": "is_encrypted = 1 for recent backups", "remediation": "BACKUP DATABASE [DatabaseName] TO DISK = 'path' WITH ENCRYPTION (ALGORITHM = AES_256, SERVER CERTIFICATE = [CertificateName])"}, "CIS_3_4_CertificateManagement": {"controlId": "CIS-3.4", "controlName": "Certificate Management", "baselineValue": "ValidCertificates", "baselineDescription": "Encryption certificates must be valid and properly managed", "riskLevel": "Medium", "cvssScore": 4.9, "query": "SELECT name, subject, expiry_date, CASE WHEN expiry_date > GETDATE() THEN 'Valid' ELSE 'Expired' END AS status FROM sys.certificates", "complianceCheck": "expiry_date > GETDATE() for all certificates", "remediation": "Renew expired certificates and update encryption configurations"}, "CIS_3_5_ColumnEncryption": {"controlId": "CIS-3.5", "controlName": "Column-Level Encryption Assessment", "baselineValue": "EncryptedSensitiveColumns", "baselineDescription": "Sensitive data columns should be encrypted using Always Encrypted or similar", "riskLevel": "Medium", "cvssScore": 5.1, "query": "SELECT SCHEMA_NAME(t.schema_id) AS schema_name, t.name AS table_name, c.name AS column_name, c.encryption_type, c.encryption_type_desc FROM sys.tables t INNER JOIN sys.columns c ON t.object_id = c.object_id WHERE c.encryption_type IS NOT NULL", "complianceCheck": "sensitive columns have encryption_type IS NOT NULL", "remediation": "Implement Always Encrypted or column-level encryption for sensitive data"}}, "auditMonitoring": {"CIS_8_1_ServerAudit": {"controlId": "CIS-8.1", "controlName": "SQL Server Audit Configuration", "baselineValue": true, "baselineDescription": "At least one server audit must be enabled and configured", "riskLevel": "High", "cvssScore": 6.7, "query": "SELECT name, is_state_enabled, type_desc FROM sys.server_audits", "complianceCheck": "at least one audit with is_state_enabled = 1", "remediation": "CREATE SERVER AUDIT [AuditName] TO FILE (FILEPATH = 'C:\\AuditLogs\\'); ALTER SERVER AUDIT [AuditName] WITH (STATE = ON)"}, "CIS_8_2_LoginFailureAuditing": {"controlId": "CIS-8.2", "controlName": "Login Failure Auditing", "baselineValue": true, "baselineDescription": "Failed login attempts must be audited", "riskLevel": "Medium", "cvssScore": 5.4, "query": "SELECT sas.name AS audit_specification_name, sasd.audit_action_name, sasd.is_state_enabled FROM sys.server_audit_specifications sas INNER JOIN sys.server_audit_specifications_details sasd ON sas.server_specification_id = sasd.server_specification_id WHERE sasd.audit_action_name LIKE '%LOGIN%'", "complianceCheck": "LOGIN_FAILED audit action enabled", "remediation": "CREATE SERVER AUDIT SPECIFICATION [LoginAuditSpec] FOR SERVER AUDIT [AuditName] ADD (FAILED_LOGIN_GROUP)"}, "CIS_8_3_SchemaChangeAuditing": {"controlId": "CIS-8.3", "controlName": "Database Schema Change Auditing", "baselineValue": true, "baselineDescription": "Database schema changes must be audited", "riskLevel": "Medium", "cvssScore": 4.8, "query": "SELECT das.name AS audit_specification_name, dasd.audit_action_name, dasd.is_state_enabled FROM sys.database_audit_specifications das INNER JOIN sys.database_audit_specifications_details dasd ON das.database_specification_id = dasd.database_specification_id WHERE dasd.audit_action_name LIKE '%SCHEMA%'", "complianceCheck": "SCHEMA_OBJECT_CHANGE_GROUP audit action enabled", "remediation": "CREATE DATABASE AUDIT SPECIFICATION [SchemaAuditSpec] FOR SERVER AUDIT [AuditName] ADD (SCHEMA_OBJECT_CHANGE_GROUP)"}, "CIS_8_4_DataAccessAuditing": {"controlId": "CIS-8.4", "controlName": "Data Access Auditing", "baselineValue": "SensitiveDataAudited", "baselineDescription": "Access to sensitive data must be audited", "riskLevel": "Medium", "cvssScore": 5.2, "query": "SELECT das.name AS audit_specification_name, dasd.audit_action_name, dasd.class_desc, dasd.major_id FROM sys.database_audit_specifications das INNER JOIN sys.database_audit_specifications_details dasd ON das.database_specification_id = dasd.database_specification_id WHERE dasd.audit_action_name IN ('SELECT', 'INSERT', 'UPDATE', 'DELETE')", "complianceCheck": "sensitive tables have SELECT/INSERT/UPDATE/DELETE audit actions", "remediation": "CREATE DATABASE AUDIT SPECIFICATION [DataAccessAuditSpec] FOR SERVER AUDIT [AuditName] ADD (SELECT, INSERT, UPDATE, DELETE ON [SensitiveTable] BY [public])"}, "CIS_8_5_AuditLogSecurity": {"controlId": "CIS-8.5", "controlName": "Audit Log Security and Retention", "baselineValue": "SecureAuditFiles", "baselineDescription": "Audit log files must be secured and retained appropriately", "riskLevel": "Medium", "cvssScore": 4.6, "query": "File system permissions check on audit log directory", "complianceCheck": "audit files accessible only to SQL Server service account and administrators", "remediation": "Configure appropriate NTFS permissions on audit log directory and implement retention policy"}}, "networkSecurity": {"CIS_12_1_NetworkProtocols": {"controlId": "CIS-12.1", "controlName": "Network Protocol Configuration", "baselineValue": "MinimalProtocols", "baselineDescription": "Only necessary network protocols should be enabled", "riskLevel": "Medium", "cvssScore": 4.3, "query": "Registry check for enabled protocols in SQL Server Network Configuration", "complianceCheck": "only TCP/IP enabled, Named Pipes disabled unless required", "remediation": "Use SQL Server Configuration Manager to disable unnecessary protocols"}, "CIS_12_2_PortSecurity": {"controlId": "CIS-12.2", "controlName": "Port Security Configuration", "baselineValue": "NonDefaultPort", "baselineDescription": "SQL Server should not use default port 1433", "riskLevel": "Low", "cvssScore": 3.1, "query": "SELECT DISTINCT local_tcp_port FROM sys.dm_exec_connections WHERE session_id = @@SPID", "complianceCheck": "local_tcp_port != 1433", "remediation": "Configure SQL Server to use a non-default port through SQL Server Configuration Manager"}, "CIS_12_3_RemoteAccess": {"controlId": "CIS-12.3", "controlName": "Remote Access Security", "baselineValue": {"remoteAccess": 0, "remoteAdminConnections": 1}, "baselineDescription": "Remote access should be disabled unless required; DAC should be enabled", "riskLevel": "Medium", "cvssScore": 5.0, "query": "SELECT name, value_in_use FROM sys.configurations WHERE name IN ('remote access', 'remote admin connections')", "complianceCheck": "remote access = 0 (unless required), remote admin connections = 1", "remediation": "EXEC sp_configure 'remote access', 0; EXEC sp_configure 'remote admin connections', 1; RECONFIGURE"}}, "systemConfiguration": {"CIS_5_1_FilePermissions": {"controlId": "CIS-5.1", "controlName": "Database File Security Permissions", "baselineValue": "RestrictedAccess", "baselineDescription": "Database files must have restricted file system permissions", "riskLevel": "High", "cvssScore": 7.8, "query": "File system ACL check on database files (.mdf, .ldf, .ndf)", "complianceCheck": "only SQL Server service account and administrators have access", "remediation": "Configure NTFS permissions to restrict access to SQL Server service account and administrators only"}, "CIS_5_2_ConfigurationSecurity": {"controlId": "CIS-5.2", "controlName": "SQL Server Configuration Security", "baselineValue": {"xpCmdshell": 0, "oleAutomation": 0, "sqlMailXPs": 0, "databaseMailXPs": 0, "adHocDistributedQueries": 0}, "baselineDescription": "Potentially dangerous SQL Server features must be disabled", "riskLevel": "High", "cvssScore": 8.2, "query": "SELECT name, value_in_use FROM sys.configurations WHERE name IN ('xp_cmdshell', 'Ole Automation Procedures', 'SQL Mail XPs', 'Database Mail XPs', 'Ad Hoc Distributed Queries')", "complianceCheck": "all dangerous features = 0", "remediation": "EXEC sp_configure 'xp_cmdshell', 0; EXEC sp_configure 'Ole Automation Procedures', 0; RECONFIGURE"}}, "reportingSettings": {"complianceThresholds": {"compliant": 100, "partiallyCompliant": 75, "nonCompliant": 0}, "riskSeverityMapping": {"critical": {"cvssRange": [9.0, 10.0], "color": "#DC3545", "priority": 1}, "high": {"cvssRange": [7.0, 8.9], "color": "#FD7E14", "priority": 2}, "medium": {"cvssRange": [4.0, 6.9], "color": "#FFC107", "priority": 3}, "low": {"cvssRange": [0.1, 3.9], "color": "#28A745", "priority": 4}}, "outputFormats": {"csv": {"enabled": true, "filename": "CIS-Compliance-Report-{timestamp}.csv"}, "html": {"enabled": true, "filename": "CIS-Compliance-Report-{timestamp}.html", "template": "templates/compliance-report.html"}, "json": {"enabled": true, "filename": "CIS-Compliance-Report-{timestamp}.json"}}}}