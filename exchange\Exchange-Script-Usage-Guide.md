# Exchange Mailbox Audit Script - Usage Guide

## 📋 Prerequisites

### System Requirements
- **Exchange Server:** 2016 (on-premises)
- **PowerShell Version:** 3.0 or later
- **Windows Version:** Windows Server 2012 R2 or later
- **Memory:** Minimum 4GB RAM (8GB+ recommended for large environments)

### Required PowerShell Modules
```powershell
# Check if Exchange Management Shell is available
Get-Command Get-Mailbox -ErrorAction SilentlyContinue

# For remote connections, ensure WinRM is configured
Get-Service WinRM
```

### Required Permissions
The account running the script must have one of the following:

**Option 1: Built-in Roles**
- **View-Only Organization Management** (minimum required)
- **Recipient Management** (read-only operations)

**Option 2: Custom Role Assignment**
```powershell
# Create custom role for auditing (run as Exchange admin)
New-ManagementRole -Name "Mailbox Auditor" -Parent "View-Only Recipients"
New-RoleGroup -Name "Mailbox Audit Team" -Roles "Mailbox Auditor"
Add-RoleGroupMember -Identity "Mailbox Audit Team" -Member "AuditUser"
```

**Required Cmdlet Permissions:**
- `Get-Mailbox`
- `Get-MailboxStatistics`
- `Get-MailboxDatabase`
- `Get-User`
- `Get-Recipient`

### PowerShell Execution Policy
```powershell
# Check current execution policy
Get-ExecutionPolicy

# Set execution policy if needed (run as Administrator)
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🚀 Usage Examples

### Example 1: Basic Local Execution
```powershell
# Run from Exchange Management Shell on Exchange server
.\Exchange-Mailbox-Audit-Script.ps1 -OutputPath "C:\ExchangeAudit"
```

### Example 2: Domain-Specific Audit
```powershell
# Audit only mailboxes from specific domain
.\Exchange-Mailbox-Audit-Script.ps1 -DomainFilter "*@contoso.com" -OutputPath "C:\Audit\Contoso"
```

### Example 3: Include Disabled Mailboxes
```powershell
# Include disabled accounts in the audit
.\Exchange-Mailbox-Audit-Script.ps1 -IncludeDisabled -OutputPath "C:\Audit\Complete"
```

### Example 4: Organizational Unit Filter
```powershell
# Audit specific OU only
.\Exchange-Mailbox-Audit-Script.ps1 -OrganizationalUnit "contoso.com/Users/<USER>" -OutputPath "C:\Audit\Sales"
```

### Example 5: Remote PowerShell Connection
```powershell
# Connect to remote Exchange server
$cred = Get-Credential
.\Exchange-Mailbox-Audit-Script.ps1 -ExchangeServer "exchange01.contoso.com" -UseRemoteSession -Credential $cred -OutputPath "C:\RemoteAudit"
```

### Example 6: Limited Results for Testing
```powershell
# Process only first 100 mailboxes for testing
.\Exchange-Mailbox-Audit-Script.ps1 -MaxResults 100 -OutputPath "C:\TestAudit"
```

### Example 7: Comprehensive Enterprise Audit
```powershell
# Full enterprise audit with all options
.\Exchange-Mailbox-Audit-Script.ps1 -IncludeDisabled -DomainFilter "*@company.com" -OutputPath "D:\ExchangeAudit\$(Get-Date -Format 'yyyy-MM')" -MaxResults 0
```

## 📊 Sample CSV Output

### Column Headers
The script generates a CSV file with the following columns:

```csv
DisplayName,UserPrincipalName,SamAccountName,PrimarySmtpAddress,EmailAliases,AccountStatus,MailboxStatus,LastLogonDate,LastEmailActivity,LastItemReceived,AccountCreationDate,AccountDisabledDate,MailboxCreationDate,MailboxSizeMB,MailboxSizeFormatted,ItemCount,DatabaseName,ServerLocation,MailboxType,OrganizationalUnit,LastModified,ExchangeVersion,AuditEnabled,AuditDate
```

### Sample Data Rows
```csv
"John Smith","<EMAIL>","jsmith","<EMAIL>","<EMAIL>, <EMAIL>","Active","Enabled","8/9/2024 2:30:22 PM","8/9/2024 2:25:15 PM","8/9/2024 2:25:15 PM","1/15/2023 9:00:00 AM","N/A","1/15/2023 10:30:00 AM","2048.50","2.00 GB","15420","MDB01","EXCHANGE01","UserMailbox","contoso.com/Users","8/9/2024 2:30:22 PM","15.1.2375.7","True","8/10/2024 1:20:53 AM"

"Conference Room A","<EMAIL>","room-a","<EMAIL>","<EMAIL>","Active","Enabled","Never","Never","Never","3/10/2023 2:00:00 PM","N/A","3/10/2023 2:15:00 PM","0.25","256.00 KB","5","MDB02","EXCHANGE02","RoomMailbox","contoso.com/Resources","3/10/2023 2:15:00 PM","15.1.2375.7","False","8/10/2024 1:20:53 AM"
```

## 📁 Output Files

### Generated Files
The script creates three output files:

1. **CSV Report:** `Exchange-Mailbox-Audit-Report-YYYYMMDD-HHMMSS.csv`
   - Detailed mailbox data in CSV format
   - UTF-8 encoding for international characters
   - Ready for Excel or database import

2. **Summary Report:** `Exchange-Mailbox-Audit-Summary-YYYYMMDD-HHMMSS.txt`
   - Executive summary with statistics
   - Error summary and recommendations
   - Audit metadata and settings

3. **Log File:** `Exchange-Mailbox-Audit-Log-YYYYMMDD-HHMMSS.txt`
   - Detailed execution log
   - Error messages and warnings
   - Progress tracking information

### File Locations
```
OutputPath/
├── Exchange-Mailbox-Audit-Report-20240810-132045.csv
├── Exchange-Mailbox-Audit-Summary-20240810-132045.txt
└── Exchange-Mailbox-Audit-Log-20240810-132045.txt
```

## ⚙️ Script Parameters

### Required Parameters
None - all parameters are optional with sensible defaults.

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `ExchangeServer` | String | None | FQDN of Exchange server for remote connection |
| `OutputPath` | String | Current Directory | Directory for output files |
| `DomainFilter` | String | None | Filter by email domain (e.g., '*@contoso.com') |
| `OrganizationalUnit` | String | None | Filter by specific OU |
| `IncludeDisabled` | Switch | False | Include disabled mailboxes |
| `MaxResults` | Integer | 0 (Unlimited) | Maximum mailboxes to process |
| `UseRemoteSession` | Switch | False | Use remote PowerShell session |
| `Credential` | PSCredential | None | Credentials for remote connection |

### Parameter Validation
- `OutputPath`: Must be accessible and writable
- `DomainFilter`: Must use wildcard format (*@domain.com)
- `MaxResults`: Must be positive integer or 0 for unlimited
- `ExchangeServer`: Required when using `-UseRemoteSession`

## 🔧 Performance Considerations

### Large Environment Optimization
For environments with 1000+ mailboxes:

```powershell
# Use batch processing with MaxResults
.\Exchange-Mailbox-Audit-Script.ps1 -MaxResults 500 -OutputPath "C:\Audit\Batch1"
.\Exchange-Mailbox-Audit-Script.ps1 -MaxResults 500 -Skip 500 -OutputPath "C:\Audit\Batch2"
```

### Memory Management
- Script processes mailboxes one at a time to minimize memory usage
- Progress indicators every 50 mailboxes to track performance
- Automatic garbage collection for large datasets

### Network Considerations
- Remote sessions may be slower than local execution
- Consider running during off-peak hours for large audits
- Monitor network bandwidth usage during remote operations

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Issue: "Exchange Management Shell not detected"
**Solution:**
```powershell
# Option 1: Run from Exchange Management Shell
# Start → Microsoft Exchange Server 2016 → Exchange Management Shell

# Option 2: Use remote session
.\Exchange-Mailbox-Audit-Script.ps1 -UseRemoteSession -ExchangeServer "exchange01.contoso.com"
```

#### Issue: "Access Denied" or Permission Errors
**Solution:**
```powershell
# Check current permissions
Get-ManagementRoleAssignment -RoleAssignee (whoami)

# Verify Exchange connectivity
Get-Mailbox -ResultSize 1
```

#### Issue: "Cannot connect to remote server"
**Solution:**
```powershell
# Test WinRM connectivity
Test-WSMan exchange01.contoso.com

# Check PowerShell remoting
Test-NetConnection exchange01.contoso.com -Port 5985
```

#### Issue: Script runs slowly with large mailbox counts
**Solution:**
```powershell
# Use MaxResults to limit scope
.\Exchange-Mailbox-Audit-Script.ps1 -MaxResults 1000

# Filter by domain or OU
.\Exchange-Mailbox-Audit-Script.ps1 -DomainFilter "*@contoso.com"
```

#### Issue: CSV file contains garbled characters
**Solution:**
- Script uses UTF-8 encoding by default
- Open CSV in Excel using Data → From Text/CSV
- Specify UTF-8 encoding when importing

### Debug Mode
```powershell
# Run with verbose output for troubleshooting
.\Exchange-Mailbox-Audit-Script.ps1 -Verbose -Debug
```

### Log Analysis
Check the log file for detailed error information:
```powershell
# View recent errors
Get-Content "Exchange-Mailbox-Audit-Log-*.txt" | Where-Object {$_ -like "*ERROR*"}

# View warnings
Get-Content "Exchange-Mailbox-Audit-Log-*.txt" | Where-Object {$_ -like "*WARNING*"}
```

## 📈 Best Practices

### Scheduling Regular Audits
```powershell
# Create scheduled task for monthly audits
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\Exchange-Mailbox-Audit-Script.ps1 -OutputPath C:\Audits\Monthly"
$trigger = New-ScheduledTaskTrigger -Monthly -At "02:00AM" -DaysOfMonth 1
Register-ScheduledTask -TaskName "Exchange Mailbox Audit" -Action $action -Trigger $trigger
```

### Data Retention
- Keep audit reports for compliance requirements (typically 7 years)
- Archive old reports to reduce storage usage
- Implement automated cleanup of old log files

### Security Considerations
- Use dedicated service account with minimal required permissions
- Store credentials securely (avoid hardcoding)
- Regularly review and rotate service account passwords
- Monitor script execution logs for unauthorized usage

### Performance Monitoring
- Track script execution time trends
- Monitor Exchange server performance during audits
- Adjust MaxResults parameter based on server capacity
- Schedule audits during maintenance windows for large environments

---

**Document Version:** 1.0  
**Last Updated:** August 10, 2024  
**Script Version:** 1.0  

*This guide should be updated whenever the script is modified or Exchange environment changes.*
