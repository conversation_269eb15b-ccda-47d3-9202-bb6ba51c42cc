#Requires -Version 5.1
<#
================================================================================
Windows Server 2019 Security Audit Script
================================================================================
Description: Focused audit script for Windows Server 2019 security configuration assessment
Version: 1.0 (Windows Server 2019 Compatible)
Created: August 17, 2025

INSTRUCTIONS FOR ADMIN:
1. Copy this file to the Windows Server 2019 system
2. Run PowerShell as Administrator and execute:
   
   .\Windows-Server-2019-Security-Audit.ps1
   
   OR with custom output location:
   .\Windows-Server-2019-Security-Audit.ps1 -OutputPath "C:\Temp\Windows-Security-Results.json"

3. Send the generated JSON file back for analysis

CRITICAL: This script is 100% READ-ONLY and safe for production environments
All operations use only Get-* cmdlets, WMI queries, and read-only registry operations
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\Windows-Server-2019-Security-Results.json"
)

# Initialize audit session
$AuditStartTime = Get-Date
$AuditID = [System.Guid]::NewGuid()
$AuditResults = @{}

# Constants for thresholds and intervals
$CRITICAL_SERVICES = @("Windows Update", "Windows Firewall", "Security Center", "Windows Event Log")
$CRITICAL_DIRECTORIES = @("C:\Windows\System32", "C:\Windows\SysWOW64", "C:\Program Files", "C:\Program Files (x86)")

# Compliance thresholds
$COMPLIANCE_THRESHOLDS = @{
    CriticalServices = 0.8
    RegistrySettings = 0.8
    MinPasswordLength = 8
}

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Windows Server 2019 Security Audit" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
Write-Host "Started: $($AuditStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "Output: $OutputPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# Helper functions for safer operations
function Get-SafeProperty {
    param(
        [object]$Object,
        [string]$PropertyName,
        [object]$DefaultValue = "Unknown"
    )

    if ($Object -and $Object.PSObject.Properties[$PropertyName]) {
        return $Object.$PropertyName
    } else {
        return $DefaultValue
    }
}

function Get-SafeWmiObject {
    param(
        [string]$ClassName,
        [string]$Namespace = "root\cimv2"
    )

    try {
        # Check if WMI service is running
        $WmiService = Get-Service -Name "Winmgmt" -ErrorAction SilentlyContinue
        if ($WmiService -and $WmiService.Status -eq "Running") {
            return Get-WmiObject -Class $ClassName -Namespace $Namespace -ErrorAction Stop
        } else {
            Write-Host "[WARNING] WMI service not available for $ClassName query" -ForegroundColor Yellow
            return $null
        }
    } catch {
        Write-Host "[WARNING] WMI query failed for $ClassName : $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

function Get-SafePasswordPolicy {
    $PasswordPolicy = @{}

    try {
        # Try net accounts first
        $NetAccounts = & net accounts 2>$null
        if ($LASTEXITCODE -eq 0 -and $NetAccounts) {
            foreach ($line in $NetAccounts) {
                # Use more flexible regex patterns
                if ($line -match "Minimum password length[:\s]+(\d+)") {
                    $PasswordPolicy.MinPasswordLength = $matches[1]
                }
                if ($line -match "Maximum password age[:\s\(]+(\d+|Never)") {
                    $PasswordPolicy.MaxPasswordAge = $matches[1]
                }
                if ($line -match "Minimum password age[:\s\(]+(\d+)") {
                    $PasswordPolicy.MinPasswordAge = $matches[1]
                }
                if ($line -match "Password history[:\s]+(\d+)") {
                    $PasswordPolicy.PasswordHistory = $matches[1]
                }
                if ($line -match "Lockout threshold[:\s]+(\d+|Never)") {
                    $PasswordPolicy.LockoutThreshold = $matches[1]
                }
            }
        } else {
            $PasswordPolicy.Error = "net accounts command failed or not available"
        }
    } catch {
        $PasswordPolicy.Error = "Exception accessing password policy: $($_.Exception.Message)"
    }

    # Fallback: Try registry approach for critical settings
    if (-not $PasswordPolicy.MinPasswordLength) {
        try {
            $RegValue = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Netlogon\Parameters" -Name "MinimumPasswordLength" -ErrorAction SilentlyContinue
            if ($RegValue) {
                $PasswordPolicy.MinPasswordLength = $RegValue.MinimumPasswordLength
                $PasswordPolicy.Source = "Registry"
            }
        } catch {
            # Silent fallback
        }
    }

    return $PasswordPolicy
}

function Get-SafeAuditPolicy {
    $AuditPolicy = @{}

    try {
        # Check if auditpol is available
        $AuditPolPath = Get-Command auditpol.exe -ErrorAction SilentlyContinue
        if ($AuditPolPath) {
            $AuditPolicyOutput = & auditpol /get /category:* 2>$null
            if ($LASTEXITCODE -eq 0 -and $AuditPolicyOutput) {
                $AuditPolicy.Available = $true
                $AuditPolicy.Output = $AuditPolicyOutput | Select-Object -First 20
            } else {
                $AuditPolicy.Available = $false
                $AuditPolicy.Error = "auditpol command failed"
            }
        } else {
            $AuditPolicy.Available = $false
            $AuditPolicy.Error = "auditpol command not found"
        }
    } catch {
        $AuditPolicy.Available = $false
        $AuditPolicy.Error = "Exception accessing audit policy: $($_.Exception.Message)"
    }

    return $AuditPolicy
}

function Test-Prerequisites {
    $PreflightResults = @{
        IsAdmin = $false
        WmiAvailable = $false
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        Warnings = @()
    }

    # Check Administrator privileges
    try {
        $CurrentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
        $PreflightResults.IsAdmin = $CurrentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } catch {
        $PreflightResults.Warnings += "Cannot verify Administrator status"
    }

    # Check WMI availability
    try {
        $WmiService = Get-Service -Name "Winmgmt" -ErrorAction SilentlyContinue
        $PreflightResults.WmiAvailable = ($WmiService -and $WmiService.Status -eq "Running")
    } catch {
        $PreflightResults.Warnings += "Cannot verify WMI service status"
    }

    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $PreflightResults.Warnings += "PowerShell version may be too old for full compatibility"
    }

    return $PreflightResults
}

try {
    # =============================================================================
    # AUDIT METADATA AND ENVIRONMENT INFORMATION
    # =============================================================================
    
    Write-Host "Step 1/8: Collecting Windows Server environment metadata..." -ForegroundColor Yellow
    
    try {
        $ComputerInfo = Get-ComputerInfo -ErrorAction Stop
        $OSInfo = Get-SafeWmiObject -ClassName "Win32_OperatingSystem"
        $SystemInfo = Get-SafeWmiObject -ClassName "Win32_ComputerSystem"
        
        $AuditMetadata = @{
            AuditID = $AuditID
            AuditStartTime = $AuditStartTime
            ComputerName = $env:COMPUTERNAME
            WindowsVersion = Get-SafeProperty -Object $ComputerInfo -PropertyName "WindowsVersion"
            WindowsBuildNumber = Get-SafeProperty -Object $ComputerInfo -PropertyName "WindowsBuildLabEx"
            OSCaption = Get-SafeProperty -Object $OSInfo -PropertyName "Caption"
            OSArchitecture = Get-SafeProperty -Object $OSInfo -PropertyName "OSArchitecture"
            TotalPhysicalMemory = if ($ComputerInfo -and $ComputerInfo.TotalPhysicalMemory) { [math]::Round($ComputerInfo.TotalPhysicalMemory / 1GB, 2) } else { "Unknown" }
            Domain = Get-SafeProperty -Object $SystemInfo -PropertyName "Domain"
            Workgroup = Get-SafeProperty -Object $SystemInfo -PropertyName "Workgroup"
            AssessmentScope = "8 Critical Windows Server Security Controls"
            AuditUser = $env:USERNAME
            UserDomain = $env:USERDOMAIN
        }
        
        $AuditResults.Add("AuditMetadata", $AuditMetadata)
        Write-Host "[SUCCESS] Environment metadata collected successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to collect environment metadata: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AuditMetadata", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # ENHANCED PRE-FLIGHT VALIDATION
    # =============================================================================

    Write-Host "Performing enhanced pre-flight validation..." -ForegroundColor Yellow

    $PreflightResults = Test-Prerequisites
    if ($PreflightResults.Warnings.Count -gt 0) {
        Write-Host "[WARNING] Pre-flight checks found issues:" -ForegroundColor Yellow
        foreach ($Warning in $PreflightResults.Warnings) {
            Write-Host "  - $Warning" -ForegroundColor Yellow
        }
    }

    # =============================================================================
    # PRE-FLIGHT VALIDATION
    # =============================================================================
    
    Write-Host "Performing pre-flight validation..." -ForegroundColor Yellow
    
    # Check if running as Administrator
    try {
        $CurrentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
        $IsAdmin = $CurrentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
        
        if ($IsAdmin) {
            Write-Host "[SUCCESS] Running with Administrator privileges" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Not running as Administrator - some checks may be limited" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[ERROR] Cannot verify Administrator status: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Check Windows Server version
    try {
        $OSVersionInfo = Get-SafeWmiObject -ClassName "Win32_OperatingSystem"
        $OSVersion = Get-SafeProperty -Object $OSVersionInfo -PropertyName "Caption"
        if ($OSVersion -like "*Server 2019*" -or $OSVersion -like "*Server 2016*" -or $OSVersion -like "*Server 2022*") {
            Write-Host "[SUCCESS] Windows Server version compatible: $OSVersion" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Unexpected OS version: $OSVersion" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[ERROR] Cannot verify OS version: $($_.Exception.Message)" -ForegroundColor Red
    }

    # =============================================================================
    # CONTROL 1: WINDOWS UPDATE AND PATCH MANAGEMENT
    # =============================================================================
    
    Write-Host "Step 2/8: Assessing Windows Update and patch management..." -ForegroundColor Yellow
    
    try {
        # Get Windows Update service status
        $UpdateService = Get-Service -Name "wuauserv" -ErrorAction SilentlyContinue
        
        # Get recent updates (last 30 days)
        $RecentUpdates = Get-HotFix | Where-Object { $_.InstalledOn -gt (Get-Date).AddDays(-30) } | Sort-Object InstalledOn -Descending
        
        # Get Windows Update settings from registry
        $UpdateSettings = @{}
        try {
            $UpdateSettings.AutoUpdateEnabled = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -ErrorAction SilentlyContinue).NoAutoUpdate -eq 0
            $UpdateSettings.AutoInstallMinorUpdates = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AutoInstallMinorUpdates" -ErrorAction SilentlyContinue).AutoInstallMinorUpdates
        } catch {
            $UpdateSettings.RegistryAccessError = $_.Exception.Message
        }
        
        $WindowsUpdateControl = @{
            ControlID = "WIN-1.1"
            ControlName = "Windows Update and Patch Management"
            RiskLevel = "Critical"
            CVSSScore = 9.8
            UpdateServiceStatus = if ($UpdateService) { $UpdateService.Status } else { "Not Found" }
            UpdateServiceStartType = if ($UpdateService) { $UpdateService.StartType } else { "Unknown" }
            RecentUpdatesCount = $RecentUpdates.Count
            RecentUpdates = $RecentUpdates | Select-Object HotFixID, Description, InstalledBy, InstalledOn -First 10
            UpdateSettings = $UpdateSettings
            LastUpdateInstalled = if ($RecentUpdates) { $RecentUpdates[0].InstalledOn } else { "No recent updates found" }
            CurrentValue = "Service: $($UpdateService.Status), Recent updates: $($RecentUpdates.Count)"
            BaselineValue = "Windows Update service running, regular updates installed"
            ComplianceStatus = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { 100 } elseif ($UpdateService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { "Windows Update service active with recent updates" } else { "Windows Update service or recent updates missing" }
            Recommendation = "Ensure Windows Update service is running and system receives regular security updates"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("WIN_1_1_WindowsUpdate", $WindowsUpdateControl)
        Write-Host "[SUCCESS] Windows Update and patch management assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Windows Update: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_1_1_WindowsUpdate", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 2: USER ACCOUNT AND PASSWORD POLICIES
    # =============================================================================
    
    Write-Host "Step 3/8: Assessing user account and password policies..." -ForegroundColor Yellow
    
    try {
        # Get local users
        $LocalUsers = Get-LocalUser -ErrorAction SilentlyContinue
        
        # Get password policy using safer approach
        $PasswordPolicy = Get-SafePasswordPolicy
        
        # Check for disabled default accounts
        $AdminAccount = $LocalUsers | Where-Object { $_.Name -eq "Administrator" }
        $GuestAccount = $LocalUsers | Where-Object { $_.Name -eq "Guest" }

        # Safe integer conversion for password length
        $MinPasswordLengthInt = 0
        if ($PasswordPolicy.MinPasswordLength -and [int]::TryParse($PasswordPolicy.MinPasswordLength, [ref]$MinPasswordLengthInt)) {
            $PasswordLengthCompliant = $MinPasswordLengthInt -ge $COMPLIANCE_THRESHOLDS.MinPasswordLength
        } else {
            $PasswordLengthCompliant = $false
            Write-Host "[WARNING] Could not parse minimum password length: $($PasswordPolicy.MinPasswordLength)" -ForegroundColor Yellow
        }
        
        $UserAccountControl = @{
            ControlID = "WIN-2.1"
            ControlName = "User Account and Password Policies"
            RiskLevel = "High"
            CVSSScore = 8.2
            TotalLocalUsers = $LocalUsers.Count
            LocalUsers = $LocalUsers | Select-Object Name, Enabled, LastLogon, PasswordRequired, PasswordExpires -First 20
            PasswordPolicy = $PasswordPolicy
            AdminAccountEnabled = if ($AdminAccount) { $AdminAccount.Enabled } else { $false }
            GuestAccountEnabled = if ($GuestAccount) { $GuestAccount.Enabled } else { $false }
            CurrentValue = "Min password length: $($PasswordPolicy.MinPasswordLength), Admin enabled: $($AdminAccount.Enabled), Guest enabled: $($GuestAccount.Enabled)"
            BaselineValue = "Strong password policy, Administrator disabled, Guest disabled"
            ComplianceStatus = if ($PasswordLengthCompliant -and -not $AdminAccount.Enabled -and -not $GuestAccount.Enabled) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($PasswordLengthCompliant -and -not $AdminAccount.Enabled -and -not $GuestAccount.Enabled) { 100 } else { 50 }
            Finding = if ($PasswordLengthCompliant) { "Password policy appears adequate" } else { "Weak password policy detected" }
            Recommendation = "Implement strong password policy, disable default Administrator and Guest accounts"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("WIN_2_1_UserAccounts", $UserAccountControl)
        Write-Host "[SUCCESS] User account and password policies assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess user accounts: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_2_1_UserAccounts", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 3: WINDOWS FIREWALL CONFIGURATION
    # =============================================================================

    Write-Host "Step 4/8: Assessing Windows Firewall configuration..." -ForegroundColor Yellow

    try {
        # Get Windows Firewall profiles
        $FirewallProfiles = Get-NetFirewallProfile -ErrorAction Stop

        # Get Windows Firewall service status
        $FirewallService = Get-Service -Name "MpsSvc" -ErrorAction SilentlyContinue

        # Get firewall rules (sample)
        $FirewallRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq $true } | Select-Object DisplayName, Direction, Action, Profile -First 50

        $FirewallControl = @{
            ControlID = "WIN-3.1"
            ControlName = "Windows Firewall Configuration"
            RiskLevel = "High"
            CVSSScore = 7.8
            FirewallServiceStatus = if ($FirewallService) { $FirewallService.Status } else { "Not Found" }
            FirewallProfiles = $FirewallProfiles | Select-Object Name, Enabled, DefaultInboundAction, DefaultOutboundAction, LogFileName
            EnabledProfiles = ($FirewallProfiles | Where-Object { $_.Enabled }).Count
            TotalProfiles = $FirewallProfiles.Count
            ActiveRulesCount = $FirewallRules.Count
            SampleRules = $FirewallRules | Select-Object -First 20
            CurrentValue = "Service: $($FirewallService.Status), Enabled profiles: $($EnabledProfiles)/$($FirewallProfiles.Count)"
            BaselineValue = "Windows Firewall service running, all profiles enabled"
            ComplianceStatus = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { 100 } elseif ($FirewallService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { "Windows Firewall properly configured and enabled" } else { "Windows Firewall configuration issues detected" }
            Recommendation = "Ensure Windows Firewall service is running and all profiles are enabled with appropriate rules"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_3_1_WindowsFirewall", $FirewallControl)
        Write-Host "[SUCCESS] Windows Firewall configuration assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Windows Firewall: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_3_1_WindowsFirewall", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 4: AUDIT LOGGING AND EVENT LOG SETTINGS
    # =============================================================================

    Write-Host "Step 5/8: Assessing audit logging and event log settings..." -ForegroundColor Yellow

    try {
        # Get Event Log service status
        $EventLogService = Get-Service -Name "EventLog" -ErrorAction SilentlyContinue

        # Get critical event logs
        $EventLogs = @("System", "Security", "Application")
        $EventLogInfo = [System.Collections.ArrayList]@()

        foreach ($LogName in $EventLogs) {
            try {
                $Log = Get-WinEvent -ListLog $LogName -ErrorAction SilentlyContinue
                if ($Log) {
                    [void]$EventLogInfo.Add(@{
                        LogName = $Log.LogName
                        IsEnabled = $Log.IsEnabled
                        MaximumSizeInBytes = $Log.MaximumSizeInBytes
                        RecordCount = $Log.RecordCount
                        LogMode = $Log.LogMode
                    })
                }
            } catch {
                [void]$EventLogInfo.Add(@{
                    LogName = $LogName
                    Error = $_.Exception.Message
                })
            }
        }

        # Check audit policy settings using safer approach
        $AuditPolicy = Get-SafeAuditPolicy

        $AuditLoggingControl = @{
            ControlID = "WIN-4.1"
            ControlName = "Audit Logging and Event Log Settings"
            RiskLevel = "Medium"
            CVSSScore = 6.5
            EventLogServiceStatus = if ($EventLogService) { $EventLogService.Status } else { "Not Found" }
            EventLogs = $EventLogInfo
            EnabledEventLogs = ($EventLogInfo | Where-Object { $_.IsEnabled }).Count
            TotalEventLogs = $EventLogInfo.Count
            AuditPolicy = $AuditPolicy
            CurrentValue = "Event Log service: $($EventLogService.Status), Enabled logs: $($EnabledEventLogs)/$($EventLogInfo.Count)"
            BaselineValue = "Event Log service running, critical logs enabled with adequate retention"
            ComplianceStatus = if ($EventLogService.Status -eq "Running" -and $EnabledEventLogs -eq $EventLogInfo.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($EventLogService.Status -eq "Running" -and $EnabledEventLogs -eq $EventLogInfo.Count) { 100 } elseif ($EventLogService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($EventLogService.Status -eq "Running") { "Event logging service active" } else { "Event logging service issues detected" }
            Recommendation = "Ensure Event Log service is running and critical event logs are enabled with appropriate retention policies"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_4_1_AuditLogging", $AuditLoggingControl)
        Write-Host "[SUCCESS] Audit logging and event log settings assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess audit logging: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_4_1_AuditLogging", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 5: SERVICE CONFIGURATIONS AND SECURITY
    # =============================================================================

    Write-Host "Step 6/8: Assessing service configurations and security..." -ForegroundColor Yellow

    try {
        # Get all services
        $AllServices = Get-Service -ErrorAction Stop

        # Focus on critical security services
        $CriticalServices = [System.Collections.ArrayList]@()
        $SecurityServiceNames = @("wuauserv", "MpsSvc", "EventLog", "Winmgmt", "RpcSs", "BITS", "CryptSvc", "TrustedInstaller")

        foreach ($ServiceName in $SecurityServiceNames) {
            $Service = $AllServices | Where-Object { $_.Name -eq $ServiceName }
            if ($Service) {
                [void]$CriticalServices.Add(@{
                    Name = $Service.Name
                    DisplayName = $Service.DisplayName
                    Status = $Service.Status
                    StartType = $Service.StartType
                })
            }
        }

        # Get services running as system accounts using safer WMI approach
        $SystemServicesWmi = Get-SafeWmiObject -ClassName "Win32_Service"
        $SystemServices = if ($SystemServicesWmi) {
            $SystemServicesWmi | Where-Object { $_.StartName -like "*System*" -or $_.StartName -like "*LocalService*" -or $_.StartName -like "*NetworkService*" } | Select-Object Name, StartName, State -First 20
        } else {
            @()
        }

        # Count running services
        $RunningServices = ($AllServices | Where-Object { $_.Status -eq "Running" }).Count
        $StoppedServices = ($AllServices | Where-Object { $_.Status -eq "Stopped" }).Count

        $ServiceControl = @{
            ControlID = "WIN-5.1"
            ControlName = "Service Configurations and Security"
            RiskLevel = "Medium"
            CVSSScore = 6.8
            TotalServices = $AllServices.Count
            RunningServices = $RunningServices
            StoppedServices = $StoppedServices
            CriticalServices = $CriticalServices
            CriticalServicesRunning = ($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count
            SystemServices = $SystemServices
            CurrentValue = "Total services: $($AllServices.Count), Running: $RunningServices, Critical running: $($CriticalServices | Where-Object { $_.Status -eq 'Running' } | Measure-Object).Count"
            BaselineValue = "Critical security services running, unnecessary services disabled"
            ComplianceStatus = if (($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count -ge ($CriticalServices.Count * $COMPLIANCE_THRESHOLDS.CriticalServices)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($CriticalServices.Count -gt 0) {
                $RunningCriticalServices = ($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count
                [math]::Round(($RunningCriticalServices / $CriticalServices.Count) * 100, 0)
            } else {
                0
            }
            Finding = if (($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count -ge ($CriticalServices.Count * $COMPLIANCE_THRESHOLDS.CriticalServices)) { "Critical security services appear to be running" } else { "Some critical security services are not running" }
            Recommendation = "Ensure critical security services are running and unnecessary services are disabled"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_5_1_ServiceSecurity", $ServiceControl)
        Write-Host "[SUCCESS] Service configurations and security assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess service security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_5_1_ServiceSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 6: REGISTRY SECURITY SETTINGS
    # =============================================================================

    Write-Host "Step 7/8: Assessing registry security settings..." -ForegroundColor Yellow

    try {
        # Check critical registry security settings
        $RegistrySettings = [System.Collections.ArrayList]@()

        # Security-related registry keys to check
        $SecurityKeys = @(
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"; Name = "LmCompatibilityLevel"; Description = "LM Authentication Level" },
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"; Name = "NoLMHash"; Description = "Disable LM Hash Storage" },
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Services\Netlogon\Parameters"; Name = "RequireSignOrSeal"; Description = "Netlogon Signing Required" },
            @{ Path = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"; Name = "EnableLUA"; Description = "User Account Control Enabled" },
            @{ Path = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"; Name = "ConsentPromptBehaviorAdmin"; Description = "UAC Admin Consent Behavior" }
        )

        foreach ($Key in $SecurityKeys) {
            try {
                $Value = Get-ItemProperty -Path $Key.Path -Name $Key.Name -ErrorAction SilentlyContinue
                [void]$RegistrySettings.Add(@{
                    Path = $Key.Path
                    Name = $Key.Name
                    Description = $Key.Description
                    Value = if ($Value) { $Value.($Key.Name) } else { "Not Found" }
                    Status = if ($Value) { "Found" } else { "Missing" }
                })
            } catch {
                [void]$RegistrySettings.Add(@{
                    Path = $Key.Path
                    Name = $Key.Name
                    Description = $Key.Description
                    Value = "Error"
                    Status = "Access Denied"
                    Error = $_.Exception.Message
                })
            }
        }

        # Count secure settings
        $SecureSettings = ($RegistrySettings | Where-Object { $_.Status -eq "Found" }).Count

        $RegistryControl = @{
            ControlID = "WIN-6.1"
            ControlName = "Registry Security Settings"
            RiskLevel = "Medium"
            CVSSScore = 6.2
            TotalSecurityKeys = $SecurityKeys.Count
            FoundSecurityKeys = $SecureSettings
            RegistrySettings = $RegistrySettings
            CurrentValue = "Security registry keys found: $SecureSettings/$($SecurityKeys.Count)"
            BaselineValue = "All critical security registry settings properly configured"
            ComplianceStatus = if ($SecureSettings -ge ($SecurityKeys.Count * $COMPLIANCE_THRESHOLDS.RegistrySettings)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($SecurityKeys.Count -gt 0) { [math]::Round(($SecureSettings / $SecurityKeys.Count) * 100, 0) } else { 0 }
            Finding = if ($SecureSettings -ge ($SecurityKeys.Count * $COMPLIANCE_THRESHOLDS.RegistrySettings)) { "Most critical registry security settings are present" } else { "Some critical registry security settings are missing" }
            Recommendation = "Review and configure critical security registry settings according to security baselines"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_6_1_RegistrySecurity", $RegistryControl)
        Write-Host "[SUCCESS] Registry security settings assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess registry security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_6_1_RegistrySecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 7: FILE SYSTEM PERMISSIONS ON CRITICAL DIRECTORIES
    # =============================================================================

    Write-Host "Step 8/8: Assessing file system permissions on critical directories..." -ForegroundColor Yellow

    try {
        $DirectoryPermissions = [System.Collections.ArrayList]@()

        foreach ($Directory in $CRITICAL_DIRECTORIES) {
            try {
                if (Test-Path $Directory) {
                    $Acl = Get-Acl $Directory -ErrorAction SilentlyContinue
                    $OwnerInfo = if ($Acl) { $Acl.Owner } else { "Unknown" }
                    $AccessRules = if ($Acl) { $Acl.Access | Select-Object IdentityReference, FileSystemRights, AccessControlType -First 10 } else { @() }

                    [void]$DirectoryPermissions.Add(@{
                        Path = $Directory
                        Exists = $true
                        Owner = $OwnerInfo
                        AccessRulesCount = $AccessRules.Count
                        SampleAccessRules = $AccessRules
                    })
                } else {
                    [void]$DirectoryPermissions.Add(@{
                        Path = $Directory
                        Exists = $false
                        Owner = "N/A"
                        AccessRulesCount = 0
                        SampleAccessRules = @()
                    })
                }
            } catch {
                [void]$DirectoryPermissions.Add(@{
                    Path = $Directory
                    Exists = "Unknown"
                    Owner = "Error"
                    Error = $_.Exception.Message
                })
            }
        }

        $ExistingDirectories = ($DirectoryPermissions | Where-Object { $_.Exists -eq $true }).Count

        $FileSystemControl = @{
            ControlID = "WIN-7.1"
            ControlName = "File System Permissions on Critical Directories"
            RiskLevel = "Medium"
            CVSSScore = 5.8
            TotalCriticalDirectories = $CRITICAL_DIRECTORIES.Count
            ExistingDirectories = $ExistingDirectories
            DirectoryPermissions = $DirectoryPermissions
            CurrentValue = "Critical directories found: $ExistingDirectories/$($CRITICAL_DIRECTORIES.Count)"
            BaselineValue = "All critical directories exist with proper permissions"
            ComplianceStatus = if ($ExistingDirectories -eq $CRITICAL_DIRECTORIES.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($CRITICAL_DIRECTORIES.Count -gt 0) { [math]::Round(($ExistingDirectories / $CRITICAL_DIRECTORIES.Count) * 100, 0) } else { 100 }
            Finding = if ($ExistingDirectories -eq $CRITICAL_DIRECTORIES.Count) { "All critical directories exist and are accessible" } else { "Some critical directories are missing or inaccessible" }
            Recommendation = "Verify critical system directories exist with appropriate permissions and ownership"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_7_1_FileSystemSecurity", $FileSystemControl)
        Write-Host "[SUCCESS] File system permissions assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess file system permissions: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_7_1_FileSystemSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # COMPLIANCE SUMMARY AND FINAL PROCESSING
    # =============================================================================

    Write-Host ""
    Write-Host "Generating compliance summary..." -ForegroundColor Yellow

    $AuditEndTime = Get-Date
    $AuditDuration = $AuditEndTime - $AuditStartTime

    # Calculate overall compliance metrics
    $TotalControls = 7
    $SuccessfulAssessments = ($AuditResults.Keys | Where-Object {
        $_ -ne "AuditMetadata" -and $_ -ne "ComplianceSummary" -and
        (-not $AuditResults[$_].ContainsKey("Error") -or $AuditResults[$_].Error -eq $null)
    }).Count
    $FailedAssessments = $TotalControls - $SuccessfulAssessments

    $ComplianceSummary = @{
        AssessmentType = "Windows Server 2019 Security Controls Audit"
        TotalControlsAssessed = $TotalControls
        SuccessfulAssessments = $SuccessfulAssessments
        FailedAssessments = $FailedAssessments
        AuditStartTime = $AuditStartTime
        AuditEndTime = $AuditEndTime
        AuditDurationSeconds = [math]::Round($AuditDuration.TotalSeconds, 2)
        AuditID = $AuditID
        FrameworkVersion = "Windows Server Security Controls v1.0"
        AuditType = "Read-Only Assessment - Production Safe"
        AuditExecutedBy = $env:USERNAME
        ComputerName = $env:COMPUTERNAME
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    }

    $AuditResults.Add("ComplianceSummary", $ComplianceSummary)

} catch {
    Write-Host "[ERROR] Critical error during audit execution: $($_.Exception.Message)" -ForegroundColor Red
    $AuditResults.Add("CriticalError", @{
        Message = $_.Exception.Message
        StackTrace = $_.Exception.StackTrace
        Timestamp = Get-Date
    })
} finally {
    # =============================================================================
    # OUTPUT GENERATION AND COMPLETION
    # =============================================================================

    Write-Host ""
    Write-Host "Generating JSON output..." -ForegroundColor Yellow

    try {
        # Convert results to JSON and save to file
        $JsonOutput = $AuditResults | ConvertTo-Json -Depth 10 -ErrorAction Stop
        $JsonOutput | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop

        Write-Host ""
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Windows Server 2019 Security Audit Completed" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
        Write-Host "End Time: $((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Duration: $([math]::Round(((Get-Date) - $AuditStartTime).TotalSeconds, 2)) seconds" -ForegroundColor Cyan
        Write-Host "Controls Assessed: 7" -ForegroundColor Cyan
        Write-Host "Output File: $OutputPath" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "[SUCCESS] JSON audit results saved successfully" -ForegroundColor Green
        Write-Host "[SUCCESS] Send the file '$OutputPath' for compliance analysis" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green

        # Display summary statistics
        if ($AuditResults.ContainsKey("ComplianceSummary")) {
            $Summary = $AuditResults["ComplianceSummary"]
            Write-Host ""
            Write-Host "AUDIT SUMMARY:" -ForegroundColor Yellow
            Write-Host "- Total Controls: $($Summary.TotalControlsAssessed)" -ForegroundColor White
            Write-Host "- Successful Assessments: $($Summary.SuccessfulAssessments)" -ForegroundColor Green
            Write-Host "- Failed Assessments: $($Summary.FailedAssessments)" -ForegroundColor Red
            Write-Host "- Success Rate: $([math]::Round(($Summary.SuccessfulAssessments / $Summary.TotalControlsAssessed) * 100, 1))%" -ForegroundColor Cyan
        }

    } catch {
        Write-Host "[ERROR] Failed to save JSON output: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Attempting to save to alternative location..." -ForegroundColor Yellow

        try {
            $AlternativePath = ".\Windows-Server-2019-Security-Results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
            $JsonOutput | Out-File -FilePath $AlternativePath -Encoding UTF8 -ErrorAction Stop
            Write-Host "[SUCCESS] JSON output saved to alternative location: $AlternativePath" -ForegroundColor Green
        } catch {
            Write-Host "[ERROR] Failed to save to alternative location. Displaying results:" -ForegroundColor Red
            Write-Host $JsonOutput
        }
    }
}

<#
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN (Windows Server 2019):
================================================================================

1. WINDOWS SERVER LOCAL EXECUTION (Recommended):
   .\Windows-Server-2019-Security-Audit.ps1

2. WITH CUSTOM OUTPUT PATH:
   .\Windows-Server-2019-Security-Audit.ps1 -OutputPath "C:\Temp\Windows-Security-Results.json"

3. REMOTE EXECUTION (PowerShell Remoting):
   # Connect to remote server first, then run the script
   $Session = New-PSSession -ComputerName "ServerName" -Credential (Get-Credential)
   Invoke-Command -Session $Session -FilePath ".\Windows-Server-2019-Security-Audit.ps1"

PREREQUISITES:
- Windows Server 2019, 2016, or 2022
- PowerShell 5.1 or later
- Administrator privileges (recommended for complete assessment)
- Local or remote access to target Windows Server

IMPORTANT NOTES:
- This script is 100% READ-ONLY and safe for production
- No Windows configuration modifications are performed
- Output is in JSON format for easy processing
- Script assesses 7 critical Windows Server security controls:
  1. Windows Update and Patch Management
  2. User Account and Password Policies
  3. Windows Firewall Configuration
  4. Audit Logging and Event Log Settings
  5. Service Configurations and Security
  6. Registry Security Settings
  7. File System Permissions on Critical Directories
- Compatible with Windows Server 2016, 2019, and 2022
- All operations use only Get-* cmdlets, WMI queries, and read-only registry operations

SEND BACK: The generated Windows-Server-2019-Security-Results.json file

SECURITY VERIFICATION:
- Only Get-* cmdlets used (Get-Service, Get-ComputerInfo, Get-LocalUser, etc.)
- Only WMI read operations (Get-WmiObject for system information)
- Only registry read operations (Get-ItemProperty for security settings)
- No Set-*, New-*, Remove-*, or Enable-* cmdlets
- No configuration changes or system modifications
- Read-only access to Windows configuration and security settings
- Safe for production environments

ASSESSMENT FOCUS:
This script focuses on critical Windows Server security configurations that
directly impact system security posture:
- Patch management and update status
- Account security and password policies
- Network security through Windows Firewall
- Audit and logging capabilities
- Critical service security
- Registry-based security settings
- File system security on critical directories

================================================================================
#>
