#Requires -Version 5.1
<#
================================================================================
Audit Results HTML Converter
================================================================================
Description: Converts deobfuscated Windows Server security audit results to HTML
Version: 1.0
Created: January 9, 2025

USAGE:
.\Convert-AuditResults-ToHTML.ps1 -JsonPath "Deobfuscated-Audit-Results.json" -OutputPath "Audit-Report.html"
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$JsonPath,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "Windows-Server-Audit-Report.html"
)

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit Results HTML Converter" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Input JSON: $JsonPath" -ForegroundColor Cyan
Write-Host "Output HTML: $OutputPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Load the deobfuscated audit results
try {
    Write-Host "Loading deobfuscated audit results..." -ForegroundColor Yellow
    $AuditData = Get-Content -Path $JsonPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "[SUCCESS] Audit results loaded" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to load audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Helper function to convert compliance status to color
function Get-ComplianceColor {
    param($Status)
    switch ($Status) {
        "Compliant" { return "success" }
        "Review Required" { return "warning" }
        "Non-Compliant" { return "danger" }
        default { return "secondary" }
    }
}

# Helper function to convert risk level to color
function Get-RiskColor {
    param($RiskLevel)
    switch ($RiskLevel) {
        "Critical" { return "danger" }
        "High" { return "warning" }
        "Medium" { return "info" }
        "Low" { return "success" }
        default { return "secondary" }
    }
}

# Generate HTML content
$HtmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows Server Security Audit Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .audit-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .control-card { margin-bottom: 1rem; border-left: 4px solid #007bff; }
        .hardware-fingerprint { font-family: 'Courier New', monospace; font-size: 0.9em; }
        .compliance-badge { font-size: 0.9em; }
        .risk-badge { font-size: 0.8em; }
        .section-header { border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; margin-bottom: 1rem; }
        .data-table { font-size: 0.9em; }
        .integrity-hash { font-family: 'Courier New', monospace; font-size: 0.8em; word-break: break-all; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="audit-header p-4 mb-4 rounded">
                    <h1 class="display-4"><i class="fas fa-shield-alt"></i> Windows Server Security Audit Report</h1>
"@

# Add audit metadata if available
if ($AuditData.AuditMetadata) {
    $metadata = $AuditData.AuditMetadata
    $HtmlContent += @"
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h5><i class="fas fa-server"></i> System Information</h5>
                            <p><strong>Computer:</strong> $($metadata.ComputerName)</p>
                            <p><strong>OS:</strong> $($metadata.OSCaption)</p>
                            <p><strong>Domain:</strong> $($metadata.Domain)</p>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-calendar"></i> Audit Information</h5>
                            <p><strong>Audit ID:</strong> $($metadata.AuditID)</p>
                            <p><strong>Executed By:</strong> $($metadata.AuditUser)</p>
                            <p><strong>Date:</strong> $($metadata.AuditStartTime)</p>
                        </div>
                    </div>
"@
}

$HtmlContent += @"
                </div>
            </div>
        </div>
"@

# Add compliance summary if available
if ($AuditData.ComplianceSummary) {
    $summary = $AuditData.ComplianceSummary
    $successRate = if ($summary.SuccessfulAssessments -and $summary.SuccessfulAssessments -gt 0) {
        [math]::Round(($summary.SuccessfulAssessments / ($summary.SuccessfulAssessments + $summary.FailedAssessments)) * 100, 1)
    } else { 0 }
    
    $HtmlContent += @"
        <!-- Compliance Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-chart-pie"></i> Compliance Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h2 class="text-success">$($summary.SuccessfulAssessments)</h2>
                                    <p class="text-muted">Successful Assessments</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h2 class="text-danger">$($summary.FailedAssessments)</h2>
                                    <p class="text-muted">Failed Assessments</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h2 class="text-info">$successRate%</h2>
                                    <p class="text-muted">Success Rate</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h2 class="text-secondary">$($summary.AuditDurationSeconds)s</h2>
                                    <p class="text-muted">Audit Duration</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
"@
}

# Add security controls section
$SecurityControls = $AuditData.PSObject.Properties | Where-Object { $_.Name -like "WIN_*" } | Sort-Object Name

if ($SecurityControls) {
    $HtmlContent += @"
        <!-- Security Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="section-header"><i class="fas fa-cogs"></i> Security Controls Assessment</h2>
            </div>
        </div>
        <div class="row">
"@

    foreach ($Control in $SecurityControls) {
        $ControlData = $Control.Value
        $ControlName = $Control.Name
        
        # Extract control information
        $DisplayName = if ($ControlData.ControlName) { $ControlData.ControlName } else { $ControlName }
        $ComplianceStatus = if ($ControlData.ComplianceStatus) { $ControlData.ComplianceStatus } else { "Unknown" }
        $RiskLevel = if ($ControlData.RiskLevel) { $ControlData.RiskLevel } else { "Unknown" }
        $CVSSScore = if ($ControlData.CVSSScore) { $ControlData.CVSSScore } else { "N/A" }
        $Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "No finding available" }
        $Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "No recommendation available" }
        
        $ComplianceColor = Get-ComplianceColor -Status $ComplianceStatus
        $RiskColor = Get-RiskColor -RiskLevel $RiskLevel
        
        $HtmlContent += @"
            <div class="col-md-6 mb-3">
                <div class="card control-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">$DisplayName</h5>
                        <div>
                            <span class="badge bg-$ComplianceColor compliance-badge">$ComplianceStatus</span>
                            <span class="badge bg-$RiskColor risk-badge">$RiskLevel Risk</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-6"><strong>Control ID:</strong> $ControlName</div>
                            <div class="col-6"><strong>CVSS Score:</strong> $CVSSScore</div>
                        </div>
                        <div class="mb-2">
                            <strong>Finding:</strong>
                            <p class="text-muted mb-1">$Finding</p>
                        </div>
                        <div>
                            <strong>Recommendation:</strong>
                            <p class="text-muted mb-0">$Recommendation</p>
                        </div>
                    </div>
                </div>
            </div>
"@
    }
    
    $HtmlContent += @"
        </div>
"@
}

# Add System Identity section
if ($AuditData.SystemIdentity) {
    $SystemId = $AuditData.SystemIdentity
    $HtmlContent += @"
        <!-- System Identity -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="section-header"><i class="fas fa-fingerprint"></i> System Identity & Hardware Fingerprint</h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-server"></i> System Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm data-table">
                            <tr><td><strong>Computer Name:</strong></td><td>$($SystemId.ComputerName)</td></tr>
                            <tr><td><strong>FQDN:</strong></td><td>$($SystemId.FQDN)</td></tr>
                            <tr><td><strong>Domain:</strong></td><td>$($SystemId.Domain)</td></tr>
                            <tr><td><strong>Workgroup:</strong></td><td>$($SystemId.Workgroup)</td></tr>
                            <tr><td><strong>Domain Role:</strong></td><td>$($SystemId.DomainRole)</td></tr>
                            <tr><td><strong>Machine GUID:</strong></td><td class="hardware-fingerprint">$($SystemId.MachineGUID)</td></tr>
                            <tr><td><strong>System UUID:</strong></td><td class="hardware-fingerprint">$($SystemId.SystemUUID)</td></tr>
                            <tr><td><strong>Serial Number:</strong></td><td class="hardware-fingerprint">$($SystemId.SerialNumber)</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-shield-alt"></i> Hardware Fingerprint</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Composite Hardware Fingerprint:</strong>
                            <div class="integrity-hash bg-light p-2 rounded">$($SystemId.CompositeHardwareFingerprint)</div>
                        </div>
"@

    # Add CPU information if available
    if ($SystemId.HardwareFingerprint -and $SystemId.HardwareFingerprint.CPU) {
        $CPU = $SystemId.HardwareFingerprint.CPU
        $HtmlContent += @"
                        <div class="mb-2">
                            <strong>CPU Information:</strong>
                            <ul class="list-unstyled ms-3">
                                <li><strong>Processor ID:</strong> <span class="hardware-fingerprint">$($CPU.ProcessorId)</span></li>
                                <li><strong>Manufacturer:</strong> $($CPU.Manufacturer)</li>
                                <li><strong>Cores:</strong> $($CPU.NumberOfCores)</li>
                                <li><strong>Logical Processors:</strong> $($CPU.NumberOfLogicalProcessors)</li>
                            </ul>
                        </div>
"@
    }

    $HtmlContent += @"
                    </div>
                </div>
            </div>
        </div>
"@
}

# Add Audit Signature section
if ($AuditData.AuditSignature) {
    $AuditSig = $AuditData.AuditSignature
    $HtmlContent += @"
        <!-- Audit Signature & Integrity -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="section-header"><i class="fas fa-certificate"></i> Audit Signature & Integrity</h2>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-lock"></i> Tamper Detection & Integrity Verification</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm data-table">
                                    <tr><td><strong>Signature Timestamp:</strong></td><td>$($AuditSig.SignatureTimestamp)</td></tr>
                                    <tr><td><strong>Signed By:</strong></td><td>$($AuditSig.SignedBy)</td></tr>
                                    <tr><td><strong>Audit Version:</strong></td><td>$($AuditSig.AuditVersion)</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
"@

    if ($AuditSig.AuditIntegrityHashes) {
        $Hashes = $AuditSig.AuditIntegrityHashes
        $HtmlContent += @"
                                <strong>Integrity Hashes:</strong>
                                <div class="mt-2">
                                    <div class="mb-1"><strong>SHA256:</strong> <div class="integrity-hash bg-light p-1 rounded">$($Hashes.SHA256)</div></div>
                                    <div class="mb-1"><strong>MD5:</strong> <div class="integrity-hash bg-light p-1 rounded">$($Hashes.MD5)</div></div>
                                    <div class="mb-1"><strong>Data Length:</strong> $($Hashes.DataLength) bytes</div>
                                </div>
"@
    }

    $HtmlContent += @"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
"@
}

$HtmlContent += @"
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"@

# Save HTML file
try {
    Write-Host "Generating HTML report..." -ForegroundColor Yellow
    $HtmlContent | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "[SUCCESS] HTML report generated: $OutputPath" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to save HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "HTML Conversion Complete" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
