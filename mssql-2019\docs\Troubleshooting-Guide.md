# SQL Server 2019 CIS Controls v8 Audit Framework - Troubleshooting Guide

## 📋 Overview

This comprehensive troubleshooting guide addresses common issues encountered during installation, configuration, and execution of the SQL Server 2019 CIS Controls v8 audit framework. Solutions are organized by category with step-by-step resolution procedures.

## 🔧 PowerShell and Module Issues

### **Issue: SqlServer Module Not Found**

**Symptoms:**
```
Import-Module : The specified module 'SqlServer' was not loaded because no valid module file was found
```

**Resolution:**
```powershell
# Check if module is installed
Get-Module -Name SqlServer -ListAvailable

# Install module if missing
Install-Module -Name SqlServer -Force -AllowClobber

# Alternative installation for restricted environments
Install-Module -Name SqlServer -Scope CurrentUser -Force

# Verify installation
Import-Module SqlServer
Get-Command -Module SqlServer | Select-Object Name | Head -10
```

### **Issue: PowerShell Execution Policy Restrictions**

**Symptoms:**
```
Execution of scripts is disabled on this system
```

**Resolution:**
```powershell
# Check current execution policy
Get-ExecutionPolicy -List

# Set execution policy for current user
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Temporary bypass for single execution
PowerShell.exe -ExecutionPolicy Bypass -File ".\Invoke-CISAudit.ps1"

# Unblock downloaded scripts
Get-ChildItem -Path ".\scripts\" -Recurse | Unblock-File
```

### **Issue: PowerShell Version Compatibility**

**Symptoms:**
```
The term 'ConvertFrom-Json' is not recognized as the name of a cmdlet
```

**Resolution:**
```powershell
# Check PowerShell version
$PSVersionTable.PSVersion

# Upgrade to PowerShell 5.1 or later if needed
# Download from: https://docs.microsoft.com/en-us/powershell/scripting/install/installing-windows-powershell

# Alternative: Use PowerShell Core 7+
pwsh -Version
```

## 🔌 SQL Server Connectivity Issues

### **Issue: Cannot Connect to SQL Server Instance**

**Symptoms:**
```
A network-related or instance-specific error occurred while establishing a connection to SQL Server
```

**Resolution:**
```powershell
# Test basic network connectivity
Test-NetConnection -ComputerName "ServerName" -Port 1433

# Verify SQL Server service is running
Get-Service -Name "MSSQL*" -ComputerName "ServerName"

# Test with different connection methods
# Method 1: Windows Authentication
Invoke-Sqlcmd -ServerInstance "ServerName" -Query "SELECT @@SERVERNAME"

# Method 2: SQL Authentication
$Credential = Get-Credential
Invoke-Sqlcmd -ServerInstance "ServerName" -Credential $Credential -Query "SELECT @@SERVERNAME"

# Method 3: Connection string
$ConnectionString = "Server=ServerName;Database=master;Integrated Security=true;"
Invoke-Sqlcmd -ConnectionString $ConnectionString -Query "SELECT @@SERVERNAME"
```

### **Issue: Authentication Failures**

**Symptoms:**
```
Login failed for user 'DOMAIN\Username'
```

**Resolution:**
```sql
-- Verify user exists and has proper permissions
SELECT 
    sp.name AS PrincipalName,
    sp.type_desc AS PrincipalType,
    sp.is_disabled,
    sp.create_date,
    sp.modify_date
FROM sys.server_principals sp
WHERE sp.name = 'DOMAIN\Username';

-- Check server-level permissions
SELECT 
    p.permission_name,
    p.state_desc,
    pr.name AS principal_name
FROM sys.server_permissions p
INNER JOIN sys.server_principals pr ON p.grantee_principal_id = pr.principal_id
WHERE pr.name = 'DOMAIN\Username';

-- Grant required permissions if missing
GRANT VIEW SERVER STATE TO [DOMAIN\Username];
GRANT VIEW ANY DEFINITION TO [DOMAIN\Username];
```

### **Issue: Timeout Errors During Execution**

**Symptoms:**
```
Timeout expired. The timeout period elapsed prior to completion of the operation
```

**Resolution:**
```powershell
# Increase timeout values in script parameters
.\Invoke-CISAudit.ps1 -ServerInstance "ServerName" -ConnectionTimeout 60 -QueryTimeout 600

# Test individual queries with extended timeout
Invoke-Sqlcmd -ServerInstance "ServerName" -Query "SELECT COUNT(*) FROM sys.databases" -QueryTimeout 300

# Monitor SQL Server performance during execution
# Check for blocking processes, high CPU, or memory pressure
```

## 🔐 Permission and Security Issues

### **Issue: Insufficient Permissions for System Views**

**Symptoms:**
```
The SELECT permission was denied on the object 'sys.server_audits'
```

**Resolution:**
```sql
-- Check current user permissions
SELECT 
    USER_NAME() AS CurrentUser,
    IS_SRVROLEMEMBER('sysadmin') AS IsSysAdmin,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW SERVER STATE') AS HasViewServerState,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW ANY DEFINITION') AS HasViewAnyDefinition;

-- Grant minimum required permissions
USE master;
GRANT VIEW SERVER STATE TO [AuditUser];
GRANT VIEW ANY DEFINITION TO [AuditUser];

-- Alternative: Add to server roles (higher privilege)
ALTER SERVER ROLE [securityadmin] ADD MEMBER [AuditUser];
```

### **Issue: Registry Access Denied**

**Symptoms:**
```
Access to the registry key 'HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Microsoft SQL Server' is denied
```

**Resolution:**
```powershell
# Check current user permissions
whoami /groups | findstr "Administrators"

# Run PowerShell as Administrator for registry access
Start-Process PowerShell -Verb RunAs

# Alternative: Use WMI for configuration queries
Get-WmiObject -Class Win32_Service | Where-Object {$_.Name -like "MSSQL*"}

# Grant specific registry permissions (if needed)
# Use regedit.exe to modify permissions on specific keys
```

### **Issue: File System Access Denied**

**Symptoms:**
```
Access to the path 'C:\Program Files\Microsoft SQL Server' is denied
```

**Resolution:**
```powershell
# Check file system permissions
Get-Acl "C:\Program Files\Microsoft SQL Server" | Format-List

# Test with alternative paths
$SQLDataPath = (Invoke-Sqlcmd -Query "SELECT SERVERPROPERTY('InstanceDefaultDataPath') AS DataPath").DataPath
Test-Path $SQLDataPath

# Use SQL Server queries instead of file system access where possible
Invoke-Sqlcmd -Query "SELECT name, physical_name FROM sys.master_files"
```

## 📊 Report Generation Issues

### **Issue: HTML Report Template Not Found**

**Symptoms:**
```
Cannot find path 'templates\compliance-report.html' because it does not exist
```

**Resolution:**
```powershell
# Verify template file exists
$TemplatePath = ".\output\templates\compliance-report.html"
Test-Path $TemplatePath

# Create missing template directory
New-Item -Path ".\output\templates" -ItemType Directory -Force

# Copy template from framework installation
Copy-Item -Path ".\templates\*" -Destination ".\output\templates\" -Recurse -Force

# Verify template structure
Get-Content $TemplatePath | Select-String "{{.*}}" | Select-Object -First 5
```

### **Issue: JSON Parsing Errors**

**Symptoms:**
```
ConvertFrom-Json : Invalid JSON primitive
```

**Resolution:**
```powershell
# Validate JSON configuration file
$ConfigPath = ".\config\CIS-Baseline-Config.json"
try {
    $Config = Get-Content $ConfigPath | ConvertFrom-Json
    Write-Host "✓ JSON configuration valid" -ForegroundColor Green
}
catch {
    Write-Host "✗ JSON configuration invalid: $($_.Exception.Message)" -ForegroundColor Red
    
    # Use online JSON validator or PowerShell validation
    $JsonContent = Get-Content $ConfigPath -Raw
    Test-Json $JsonContent
}

# Fix common JSON issues
# - Remove trailing commas
# - Escape backslashes in strings
# - Ensure proper quote matching
```

### **Issue: CSV Export Formatting Problems**

**Symptoms:**
```
Export-Csv : Cannot bind argument to parameter 'InputObject' because it is null
```

**Resolution:**
```powershell
# Check if audit results contain data
if ($AuditResults -and $AuditResults.Count -gt 0) {
    $AuditResults | Export-Csv -Path "output.csv" -NoTypeInformation
} else {
    Write-Warning "No audit results to export"
}

# Alternative export method with error handling
try {
    $Results = @()
    foreach ($Result in $AuditResults) {
        if ($Result -and $Result.Results) {
            $Results += $Result.Results
        }
    }
    
    if ($Results.Count -gt 0) {
        $Results | Export-Csv -Path "output.csv" -NoTypeInformation -Encoding UTF8
    }
}
catch {
    Write-Error "CSV export failed: $($_.Exception.Message)"
}
```

## 🔍 Audit Execution Issues

### **Issue: Individual Control Scripts Fail**

**Symptoms:**
```
Invoke-Sqlcmd : Incorrect syntax near 'FOR JSON PATH'
```

**Resolution:**
```sql
-- Check SQL Server version compatibility
SELECT 
    @@VERSION AS SQLVersion,
    SERVERPROPERTY('ProductVersion') AS ProductVersion,
    CASE 
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) >= 13 THEN 'JSON Supported'
        ELSE 'JSON Not Supported - Upgrade Required'
    END AS JSONSupport;

-- Alternative query without JSON (for older versions)
SELECT 
    'CIS-4.1' AS ControlID,
    'SQL Server Authentication Mode' AS ControlName,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus;
```

### **Issue: Performance Degradation During Audit**

**Symptoms:**
- Slow query execution
- High CPU usage
- Memory pressure

**Resolution:**
```sql
-- Monitor active sessions during audit
SELECT 
    session_id,
    status,
    command,
    cpu_time,
    reads,
    writes,
    wait_type,
    wait_time,
    blocking_session_id
FROM sys.dm_exec_requests
WHERE session_id > 50;

-- Check for blocking
SELECT 
    blocked.session_id AS BlockedSessionID,
    blocking.session_id AS BlockingSessionID,
    blocked.wait_type,
    blocked.wait_resource,
    blocked.command AS BlockedCommand,
    blocking.command AS BlockingCommand
FROM sys.dm_exec_requests blocked
INNER JOIN sys.dm_exec_requests blocking ON blocked.blocking_session_id = blocking.session_id;

-- Optimize audit execution
-- 1. Run during low-activity periods
-- 2. Increase query timeout values
-- 3. Execute controls individually if needed
```

### **Issue: Incomplete Audit Results**

**Symptoms:**
- Missing control assessments
- Partial data in reports
- Error count higher than expected

**Resolution:**
```powershell
# Enable verbose logging for detailed troubleshooting
.\Invoke-CISAudit.ps1 -ServerInstance "ServerName" -Verbose

# Check audit log for specific errors
$LogFile = Get-ChildItem -Path ".\output\" -Filter "*Audit-Log*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
Get-Content $LogFile.FullName | Select-String "ERROR"

# Execute individual controls for debugging
.\scripts\individual\CIS-01-Authentication.sql | Out-File "debug-output.txt"

# Verify all required scripts exist
$RequiredScripts = @(
    "CIS-01-Authentication.sql",
    "CIS-02-Access-Control.sql",
    "CIS-03-Encryption-TDE.sql"
)

foreach ($Script in $RequiredScripts) {
    $ScriptPath = ".\scripts\individual\$Script"
    if (Test-Path $ScriptPath) {
        Write-Host "✓ $Script found" -ForegroundColor Green
    } else {
        Write-Host "✗ $Script missing" -ForegroundColor Red
    }
}
```

## 🛠️ Configuration Issues

### **Issue: Custom Configuration Not Loading**

**Symptoms:**
```
Using built-in defaults - custom configuration file not found
```

**Resolution:**
```powershell
# Verify configuration file path and format
$ConfigFile = ".\config\Custom-Audit-Settings.json"
if (Test-Path $ConfigFile) {
    # Test JSON validity
    try {
        $Config = Get-Content $ConfigFile | ConvertFrom-Json
        Write-Host "✓ Configuration file valid" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Configuration file invalid: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Configuration file not found: $ConfigFile" -ForegroundColor Red
    
    # Copy default configuration
    Copy-Item ".\config\CIS-Baseline-Config.json" $ConfigFile
}

# Specify configuration file explicitly
.\Invoke-CISAudit.ps1 -ServerInstance "ServerName" -ConfigFile $ConfigFile
```

## 📞 Getting Additional Help

### **Diagnostic Information Collection**

```powershell
# Collect comprehensive diagnostic information
$DiagInfo = @{
    PowerShellVersion = $PSVersionTable.PSVersion
    SqlServerModule = Get-Module -Name SqlServer -ListAvailable | Select-Object Version
    ExecutionPolicy = Get-ExecutionPolicy
    CurrentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    OSVersion = [System.Environment]::OSVersion
    FrameworkPath = Get-Location
    ConfigFiles = Get-ChildItem -Path ".\config\" -Filter "*.json"
    ScriptFiles = Get-ChildItem -Path ".\scripts\" -Recurse -Filter "*.sql"
}

$DiagInfo | ConvertTo-Json -Depth 3 | Out-File "diagnostic-info.json"
```

### **Support Contacts**

- **Internal IT Support:** Contact your organization's IT helpdesk
- **Security Team:** Reach out to the IT Security & Compliance team
- **Documentation:** Review additional guides in the `docs` folder
- **Community:** SQL Server and PowerShell community forums

### **Log File Locations**

- **Audit Logs:** `.\output\CIS-Audit-Log-*.txt`
- **PowerShell Logs:** Windows Event Log (PowerShell/Operational)
- **SQL Server Logs:** SQL Server Error Log via SSMS or T-SQL
- **Windows Logs:** Event Viewer (System and Application logs)

---

**Remember:** This framework performs only read-only operations and is safe for production environments. Most issues are related to permissions, connectivity, or configuration rather than framework functionality.
