<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Server 2019 CIS Controls v8 Compliance Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .metadata {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .metadata-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .metadata-label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .compliance-dashboard {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .dashboard-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        
        .compliance-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 5px solid #3498db;
        }
        
        .summary-card.compliant {
            border-left-color: #27ae60;
        }
        
        .summary-card.non-compliant {
            border-left-color: #e74c3c;
        }
        
        .summary-card.warning {
            border-left-color: #f39c12;
        }
        
        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 0.9em;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .compliance-chart {
            text-align: center;
            margin: 30px 0;
        }
        
        .chart-container {
            display: inline-block;
            position: relative;
            width: 200px;
            height: 200px;
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring__circle {
            stroke: #e6e6e6;
            stroke-width: 10;
            fill: transparent;
            r: 90;
            cx: 100;
            cy: 100;
        }
        
        .progress-ring__progress {
            stroke: #27ae60;
            stroke-width: 10;
            stroke-linecap: round;
            fill: transparent;
            r: 90;
            cx: 100;
            cy: 100;
            stroke-dasharray: 565.48;
            stroke-dashoffset: 565.48;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }
        
        .chart-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .controls-section {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .control-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .control-header {
            background: #f8f9fa;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .control-header:hover {
            background: #e9ecef;
        }
        
        .control-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .control-id {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        
        .control-name {
            color: #666;
        }
        
        .risk-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .risk-critical {
            background: #e74c3c;
            color: white;
        }
        
        .risk-high {
            background: #fd7e14;
            color: white;
        }
        
        .risk-medium {
            background: #ffc107;
            color: #333;
        }
        
        .risk-low {
            background: #28a745;
            color: white;
        }
        
        .compliance-badge {
            padding: 6px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .compliance-compliant {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .compliance-non-compliant {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .compliance-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .control-details {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: none;
        }
        
        .control-details.active {
            display: block;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .detail-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .detail-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .detail-content {
            color: #666;
            line-height: 1.5;
        }
        
        .remediation-code {
            background: #2c3e50;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin-top: 10px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 30px;
        }
        
        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .authority-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .metadata-grid {
                grid-template-columns: 1fr;
            }
            
            .compliance-summary {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .footer-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>SQL Server 2019 CIS Controls v8 Compliance Report</h1>
            <div class="subtitle">Comprehensive Security Configuration Assessment</div>
        </div>
        
        <!-- Metadata Section -->
        <div class="metadata">
            <div class="metadata-grid">
                <div class="metadata-item">
                    <span class="metadata-label">Server Instance:</span>
                    <span>{{ServerInstance}}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Assessment Date:</span>
                    <span>{{AssessmentDate}}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">SQL Server Version:</span>
                    <span>{{SQLServerVersion}}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">CIS Controls Version:</span>
                    <span>v8.1</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Assessment Duration:</span>
                    <span>{{AssessmentDuration}}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Audit ID:</span>
                    <span>{{AuditID}}</span>
                </div>
            </div>
        </div>
        
        <!-- Compliance Dashboard -->
        <div class="compliance-dashboard">
            <h2 class="dashboard-title">Compliance Overview</h2>
            
            <div class="compliance-summary">
                <div class="summary-card compliant">
                    <div class="summary-number">{{CompliantCount}}</div>
                    <div class="summary-label">Compliant</div>
                </div>
                <div class="summary-card non-compliant">
                    <div class="summary-number">{{NonCompliantCount}}</div>
                    <div class="summary-label">Non-Compliant</div>
                </div>
                <div class="summary-card warning">
                    <div class="summary-number">{{WarningCount}}</div>
                    <div class="summary-label">Warnings</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{{TotalControls}}</div>
                    <div class="summary-label">Total Controls</div>
                </div>
            </div>
            
            <div class="compliance-chart">
                <div class="chart-container">
                    <svg class="progress-ring" width="200" height="200">
                        <circle class="progress-ring__circle" stroke="#e6e6e6" stroke-width="10" fill="transparent" r="90" cx="100" cy="100"/>
                        <circle class="progress-ring__progress" stroke="#27ae60" stroke-width="10" stroke-linecap="round" fill="transparent" r="90" cx="100" cy="100" style="stroke-dashoffset: {{ChartOffset}};"/>
                    </svg>
                    <div class="chart-label">{{CompliancePercentage}}%</div>
                </div>
                <div style="margin-top: 15px; font-size: 1.2em; color: #2c3e50;">
                    Overall Compliance Score
                </div>
            </div>
        </div>
        
        <!-- Controls Assessment Results -->
        <div class="controls-section">
            <h2 class="section-title">CIS Controls Assessment Results</h2>
            
            {{#each Controls}}
            <div class="control-item">
                <div class="control-header" onclick="toggleDetails('{{ControlID}}')">
                    <div class="control-info">
                        <span class="control-id">{{ControlID}}</span>
                        <span class="control-name">{{ControlName}}</span>
                        <span class="risk-badge risk-{{RiskLevel}}">{{RiskLevel}} Risk</span>
                    </div>
                    <div class="compliance-badge compliance-{{ComplianceStatus}}">
                        {{ComplianceStatus}}
                    </div>
                </div>
                <div class="control-details" id="details-{{ControlID}}">
                    <div class="detail-grid">
                        <div class="detail-section">
                            <div class="detail-title">Current Configuration</div>
                            <div class="detail-content">{{CurrentValue}}</div>
                        </div>
                        <div class="detail-section">
                            <div class="detail-title">Baseline Requirement</div>
                            <div class="detail-content">{{BaselineValue}}</div>
                        </div>
                        <div class="detail-section">
                            <div class="detail-title">Security Finding</div>
                            <div class="detail-content">{{Finding}}</div>
                        </div>
                        <div class="detail-section">
                            <div class="detail-title">CVSS Score</div>
                            <div class="detail-content">{{CVSSScore}} / 10.0</div>
                        </div>
                    </div>
                    {{#if Recommendation}}
                    <div class="detail-section" style="margin-top: 20px;">
                        <div class="detail-title">Remediation Recommendation</div>
                        <div class="detail-content">{{Recommendation}}</div>
                        {{#if RemediationCode}}
                        <div class="remediation-code">{{RemediationCode}}</div>
                        {{/if}}
                    </div>
                    {{/if}}
                </div>
            </div>
            {{/each}}
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div>
                    <strong>Microsoft SQL Server 2019 CIS Controls v8 Compliance Assessment</strong><br>
                    Generated: {{ReportGenerationDate}}
                </div>
                <div class="authority-section">
                    <span>🏛️ <strong>Authority:</strong> Internal IT Security & Compliance Team</span>
                </div>
                <div class="authority-section">
                    <span>🧑‍⚖️ <strong>Final Verdict:</strong> Approved for Production Audit Purposes</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleDetails(controlId) {
            const details = document.getElementById('details-' + controlId);
            if (details.classList.contains('active')) {
                details.classList.remove('active');
            } else {
                details.classList.add('active');
            }
        }
        
        // Initialize compliance chart
        document.addEventListener('DOMContentLoaded', function() {
            const circle = document.querySelector('.progress-ring__progress');
            const radius = circle.r.baseVal.value;
            const circumference = radius * 2 * Math.PI;
            const percentage = {{CompliancePercentage}};
            const offset = circumference - (percentage / 100) * circumference;
            
            circle.style.strokeDasharray = circumference + ' ' + circumference;
            circle.style.strokeDashoffset = offset;
        });
    </script>
</body>
</html>
