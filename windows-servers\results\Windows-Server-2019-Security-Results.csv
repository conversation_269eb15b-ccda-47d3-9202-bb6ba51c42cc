ActiveRulesCount,AdminAccountEnabled,AssessmentDate,AssessmentScope,AssessmentType,AuditDurationSeconds,AuditEndTime,AuditExecutedBy,AuditID,AuditPolicy.Available,AuditPolicy.Error,AuditStartTime,AuditType,AuditUser,BaselineValue,CVSSScore,ComplianceScore,ComplianceStatus,ComputerName,ControlID,ControlKey,ControlName,CriticalServicesRunning,CriticalServices[0].DisplayName,CriticalServices[0].Name,CriticalServices[0].StartType,CriticalServices[0].Status,CriticalServices[1].DisplayName,CriticalServices[1].Name,CriticalServices[1].StartType,CriticalServices[1].Status,CriticalServices[2].DisplayName,CriticalServices[2].Name,CriticalServices[2].StartType,CriticalServices[2].Status,CriticalServices[3].DisplayName,CriticalServices[3].Name,CriticalServices[3].StartType,CriticalServices[3].Status,CriticalServices[4].DisplayName,CriticalServices[4].Name,CriticalServices[4].StartType,CriticalServices[4].Status,CriticalServices[5].DisplayName,CriticalServices[5].Name,CriticalServices[5].StartType,CriticalServices[5].Status,CriticalServices[6].DisplayName,CriticalServices[6].Name,CriticalServices[6].StartType,CriticalServices[6].Status,CriticalServices[7].DisplayName,CriticalServices[7].Name,CriticalServices[7].StartType,CriticalServices[7].Status,CurrentValue,DirectoryPermissions[0].AccessRulesCount,DirectoryPermissions[0].Exists,DirectoryPermissions[0].Owner,DirectoryPermissions[0].Path,DirectoryPermissions[0].SampleAccessRules[0].AccessControlType,DirectoryPermissions[0].SampleAccessRules[0].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[0].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[1].AccessControlType,DirectoryPermissions[0].SampleAccessRules[1].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[1].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[2].AccessControlType,DirectoryPermissions[0].SampleAccessRules[2].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[2].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[3].AccessControlType,DirectoryPermissions[0].SampleAccessRules[3].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[3].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[4].AccessControlType,DirectoryPermissions[0].SampleAccessRules[4].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[4].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[5].AccessControlType,DirectoryPermissions[0].SampleAccessRules[5].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[5].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[6].AccessControlType,DirectoryPermissions[0].SampleAccessRules[6].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[6].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[7].AccessControlType,DirectoryPermissions[0].SampleAccessRules[7].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[7].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[8].AccessControlType,DirectoryPermissions[0].SampleAccessRules[8].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[8].IdentityReference.Value,DirectoryPermissions[0].SampleAccessRules[9].AccessControlType,DirectoryPermissions[0].SampleAccessRules[9].FileSystemRights,DirectoryPermissions[0].SampleAccessRules[9].IdentityReference.Value,DirectoryPermissions[1].AccessRulesCount,DirectoryPermissions[1].Exists,DirectoryPermissions[1].Owner,DirectoryPermissions[1].Path,DirectoryPermissions[1].SampleAccessRules[0].AccessControlType,DirectoryPermissions[1].SampleAccessRules[0].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[0].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[1].AccessControlType,DirectoryPermissions[1].SampleAccessRules[1].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[1].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[2].AccessControlType,DirectoryPermissions[1].SampleAccessRules[2].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[2].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[3].AccessControlType,DirectoryPermissions[1].SampleAccessRules[3].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[3].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[4].AccessControlType,DirectoryPermissions[1].SampleAccessRules[4].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[4].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[5].AccessControlType,DirectoryPermissions[1].SampleAccessRules[5].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[5].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[6].AccessControlType,DirectoryPermissions[1].SampleAccessRules[6].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[6].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[7].AccessControlType,DirectoryPermissions[1].SampleAccessRules[7].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[7].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[8].AccessControlType,DirectoryPermissions[1].SampleAccessRules[8].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[8].IdentityReference.Value,DirectoryPermissions[1].SampleAccessRules[9].AccessControlType,DirectoryPermissions[1].SampleAccessRules[9].FileSystemRights,DirectoryPermissions[1].SampleAccessRules[9].IdentityReference.Value,DirectoryPermissions[2].AccessRulesCount,DirectoryPermissions[2].Exists,DirectoryPermissions[2].Owner,DirectoryPermissions[2].Path,DirectoryPermissions[2].SampleAccessRules[0].AccessControlType,DirectoryPermissions[2].SampleAccessRules[0].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[0].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[1].AccessControlType,DirectoryPermissions[2].SampleAccessRules[1].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[1].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[2].AccessControlType,DirectoryPermissions[2].SampleAccessRules[2].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[2].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[3].AccessControlType,DirectoryPermissions[2].SampleAccessRules[3].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[3].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[4].AccessControlType,DirectoryPermissions[2].SampleAccessRules[4].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[4].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[5].AccessControlType,DirectoryPermissions[2].SampleAccessRules[5].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[5].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[6].AccessControlType,DirectoryPermissions[2].SampleAccessRules[6].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[6].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[7].AccessControlType,DirectoryPermissions[2].SampleAccessRules[7].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[7].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[8].AccessControlType,DirectoryPermissions[2].SampleAccessRules[8].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[8].IdentityReference.Value,DirectoryPermissions[2].SampleAccessRules[9].AccessControlType,DirectoryPermissions[2].SampleAccessRules[9].FileSystemRights,DirectoryPermissions[2].SampleAccessRules[9].IdentityReference.Value,DirectoryPermissions[3].AccessRulesCount,DirectoryPermissions[3].Exists,DirectoryPermissions[3].Owner,DirectoryPermissions[3].Path,DirectoryPermissions[3].SampleAccessRules[0].AccessControlType,DirectoryPermissions[3].SampleAccessRules[0].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[0].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[1].AccessControlType,DirectoryPermissions[3].SampleAccessRules[1].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[1].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[2].AccessControlType,DirectoryPermissions[3].SampleAccessRules[2].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[2].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[3].AccessControlType,DirectoryPermissions[3].SampleAccessRules[3].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[3].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[4].AccessControlType,DirectoryPermissions[3].SampleAccessRules[4].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[4].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[5].AccessControlType,DirectoryPermissions[3].SampleAccessRules[5].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[5].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[6].AccessControlType,DirectoryPermissions[3].SampleAccessRules[6].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[6].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[7].AccessControlType,DirectoryPermissions[3].SampleAccessRules[7].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[7].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[8].AccessControlType,DirectoryPermissions[3].SampleAccessRules[8].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[8].IdentityReference.Value,DirectoryPermissions[3].SampleAccessRules[9].AccessControlType,DirectoryPermissions[3].SampleAccessRules[9].FileSystemRights,DirectoryPermissions[3].SampleAccessRules[9].IdentityReference.Value,Domain,EnabledEventLogs,EnabledProfiles,EventLogServiceStatus,EventLogs[0].IsEnabled,EventLogs[0].LogMode,EventLogs[0].LogName,EventLogs[0].MaximumSizeInBytes,EventLogs[0].RecordCount,EventLogs[1].IsEnabled,EventLogs[1].LogMode,EventLogs[1].LogName,EventLogs[1].MaximumSizeInBytes,EventLogs[1].RecordCount,ExistingDirectories,FailedAssessments,Finding,FirewallProfiles[0].DefaultInboundAction,FirewallProfiles[0].DefaultOutboundAction,FirewallProfiles[0].Enabled,FirewallProfiles[0].LogFileName,FirewallProfiles[0].Name,FirewallProfiles[1].DefaultInboundAction,FirewallProfiles[1].DefaultOutboundAction,FirewallProfiles[1].Enabled,FirewallProfiles[1].LogFileName,FirewallProfiles[1].Name,FirewallProfiles[2].DefaultInboundAction,FirewallProfiles[2].DefaultOutboundAction,FirewallProfiles[2].Enabled,FirewallProfiles[2].LogFileName,FirewallProfiles[2].Name,FirewallServiceStatus,FoundSecurityKeys,FrameworkVersion,GuestAccountEnabled,LastUpdateInstalled,LocalUsers[0].Enabled,LocalUsers[0].LastLogon,LocalUsers[0].Name,LocalUsers[0].PasswordExpires,LocalUsers[0].PasswordRequired,LocalUsers[1].Enabled,LocalUsers[1].LastLogon,LocalUsers[1].Name,LocalUsers[1].PasswordExpires,LocalUsers[1].PasswordRequired,LocalUsers[2].Enabled,LocalUsers[2].LastLogon,LocalUsers[2].Name,LocalUsers[2].PasswordExpires,LocalUsers[2].PasswordRequired,LocalUsers[3].Enabled,LocalUsers[3].LastLogon,LocalUsers[3].Name,LocalUsers[3].PasswordExpires,LocalUsers[3].PasswordRequired,OSArchitecture,OSCaption,PasswordPolicy.LockoutThreshold,PasswordPolicy.MinPasswordLength,PowerShellVersion,RecentUpdatesCount,RecentUpdates[0].Description,RecentUpdates[0].HotFixID,RecentUpdates[0].InstalledBy,RecentUpdates[0].InstalledOn.DateTime,RecentUpdates[0].InstalledOn.value,RecentUpdates[1].Description,RecentUpdates[1].HotFixID,RecentUpdates[1].InstalledBy,RecentUpdates[1].InstalledOn.DateTime,RecentUpdates[1].InstalledOn.value,RecentUpdates[2].Description,RecentUpdates[2].HotFixID,RecentUpdates[2].InstalledBy,RecentUpdates[2].InstalledOn.DateTime,RecentUpdates[2].InstalledOn.value,Recommendation,RegistrySettings[0].Description,RegistrySettings[0].Name,RegistrySettings[0].Path,RegistrySettings[0].Status,RegistrySettings[0].Value,RegistrySettings[1].Description,RegistrySettings[1].Name,RegistrySettings[1].Path,RegistrySettings[1].Status,RegistrySettings[1].Value,RegistrySettings[2].Description,RegistrySettings[2].Name,RegistrySettings[2].Path,RegistrySettings[2].Status,RegistrySettings[2].Value,RegistrySettings[3].Description,RegistrySettings[3].Name,RegistrySettings[3].Path,RegistrySettings[3].Status,RegistrySettings[3].Value,RegistrySettings[4].Description,RegistrySettings[4].Name,RegistrySettings[4].Path,RegistrySettings[4].Status,RegistrySettings[4].Value,RiskLevel,RunningServices,SampleRules[0].Action,SampleRules[0].Direction,SampleRules[0].DisplayName,SampleRules[0].Profile,SampleRules[10].Action,SampleRules[10].Direction,SampleRules[10].DisplayName,SampleRules[10].Profile,SampleRules[11].Action,SampleRules[11].Direction,SampleRules[11].DisplayName,SampleRules[11].Profile,SampleRules[12].Action,SampleRules[12].Direction,SampleRules[12].DisplayName,SampleRules[12].Profile,SampleRules[13].Action,SampleRules[13].Direction,SampleRules[13].DisplayName,SampleRules[13].Profile,SampleRules[14].Action,SampleRules[14].Direction,SampleRules[14].DisplayName,SampleRules[14].Profile,SampleRules[15].Action,SampleRules[15].Direction,SampleRules[15].DisplayName,SampleRules[15].Profile,SampleRules[16].Action,SampleRules[16].Direction,SampleRules[16].DisplayName,SampleRules[16].Profile,SampleRules[17].Action,SampleRules[17].Direction,SampleRules[17].DisplayName,SampleRules[17].Profile,SampleRules[18].Action,SampleRules[18].Direction,SampleRules[18].DisplayName,SampleRules[18].Profile,SampleRules[19].Action,SampleRules[19].Direction,SampleRules[19].DisplayName,SampleRules[19].Profile,SampleRules[1].Action,SampleRules[1].Direction,SampleRules[1].DisplayName,SampleRules[1].Profile,SampleRules[2].Action,SampleRules[2].Direction,SampleRules[2].DisplayName,SampleRules[2].Profile,SampleRules[3].Action,SampleRules[3].Direction,SampleRules[3].DisplayName,SampleRules[3].Profile,SampleRules[4].Action,SampleRules[4].Direction,SampleRules[4].DisplayName,SampleRules[4].Profile,SampleRules[5].Action,SampleRules[5].Direction,SampleRules[5].DisplayName,SampleRules[5].Profile,SampleRules[6].Action,SampleRules[6].Direction,SampleRules[6].DisplayName,SampleRules[6].Profile,SampleRules[7].Action,SampleRules[7].Direction,SampleRules[7].DisplayName,SampleRules[7].Profile,SampleRules[8].Action,SampleRules[8].Direction,SampleRules[8].DisplayName,SampleRules[8].Profile,SampleRules[9].Action,SampleRules[9].Direction,SampleRules[9].DisplayName,SampleRules[9].Profile,StoppedServices,SuccessfulAssessments,SystemServices[0].Name,SystemServices[0].StartName,SystemServices[0].State,SystemServices[10].Name,SystemServices[10].StartName,SystemServices[10].State,SystemServices[11].Name,SystemServices[11].StartName,SystemServices[11].State,SystemServices[12].Name,SystemServices[12].StartName,SystemServices[12].State,SystemServices[13].Name,SystemServices[13].StartName,SystemServices[13].State,SystemServices[14].Name,SystemServices[14].StartName,SystemServices[14].State,SystemServices[15].Name,SystemServices[15].StartName,SystemServices[15].State,SystemServices[16].Name,SystemServices[16].StartName,SystemServices[16].State,SystemServices[17].Name,SystemServices[17].StartName,SystemServices[17].State,SystemServices[18].Name,SystemServices[18].StartName,SystemServices[18].State,SystemServices[19].Name,SystemServices[19].StartName,SystemServices[19].State,SystemServices[1].Name,SystemServices[1].StartName,SystemServices[1].State,SystemServices[2].Name,SystemServices[2].StartName,SystemServices[2].State,SystemServices[3].Name,SystemServices[3].StartName,SystemServices[3].State,SystemServices[4].Name,SystemServices[4].StartName,SystemServices[4].State,SystemServices[5].Name,SystemServices[5].StartName,SystemServices[5].State,SystemServices[6].Name,SystemServices[6].StartName,SystemServices[6].State,SystemServices[7].Name,SystemServices[7].StartName,SystemServices[7].State,SystemServices[8].Name,SystemServices[8].StartName,SystemServices[8].State,SystemServices[9].Name,SystemServices[9].StartName,SystemServices[9].State,TotalControlsAssessed,TotalCriticalDirectories,TotalEventLogs,TotalLocalUsers,TotalPhysicalMemory,TotalProfiles,TotalSecurityKeys,TotalServices,UpdateServiceStartType,UpdateServiceStatus,UpdateSettings.AutoInstallMinorUpdates,UpdateSettings.AutoUpdateEnabled,UserDomain,WindowsBuildNumber,WindowsVersion,Workgroup
,,2025-08-19 13:26:03,,,,,,,,,,,,All critical directories exist with proper permissions,5.8,100,Compliant,,WIN-7.1,WIN_7_1_FileSystemSecurity,File System Permissions on Critical Directories,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Critical directories found: 4/4,10,True,NT SERVICE\TrustedInstaller,C:\Windows\System32,0,268435456,CREATOR OWNER,0,268435456,NT AUTHORITY\SYSTEM,0,1245631,NT AUTHORITY\SYSTEM,0,268435456,BUILTIN\Administrators,0,1245631,BUILTIN\Administrators,0,-1610612736,BUILTIN\Users,0,1179817,BUILTIN\Users,0,268435456,NT SERVICE\TrustedInstaller,0,2032127,NT SERVICE\TrustedInstaller,0,1179817,APPLICATION PACKAGE AUTHORITY\ALL APPLICATION PACKAGES,10,True,NT SERVICE\TrustedInstaller,C:\Windows\SysWOW64,0,268435456,CREATOR OWNER,0,268435456,NT AUTHORITY\SYSTEM,0,1245631,NT AUTHORITY\SYSTEM,0,268435456,BUILTIN\Administrators,0,1245631,BUILTIN\Administrators,0,-1610612736,BUILTIN\Users,0,1179817,BUILTIN\Users,0,268435456,NT SERVICE\TrustedInstaller,0,2032127,NT SERVICE\TrustedInstaller,0,1179817,APPLICATION PACKAGE AUTHORITY\ALL APPLICATION PACKAGES,10,True,NT SERVICE\TrustedInstaller,C:\Program Files,0,268435456,CREATOR OWNER,0,268435456,NT AUTHORITY\SYSTEM,0,1245631,NT AUTHORITY\SYSTEM,0,268435456,BUILTIN\Administrators,0,1245631,BUILTIN\Administrators,0,-1610612736,BUILTIN\Users,0,1179817,BUILTIN\Users,0,268435456,NT SERVICE\TrustedInstaller,0,2032127,NT SERVICE\TrustedInstaller,0,1179817,APPLICATION PACKAGE AUTHORITY\ALL APPLICATION PACKAGES,10,True,NT SERVICE\TrustedInstaller,C:\Program Files (x86),0,268435456,CREATOR OWNER,0,268435456,NT AUTHORITY\SYSTEM,0,1245631,NT AUTHORITY\SYSTEM,0,268435456,BUILTIN\Administrators,0,1245631,BUILTIN\Administrators,0,-1610612736,BUILTIN\Users,0,1179817,BUILTIN\Users,0,268435456,NT SERVICE\TrustedInstaller,0,2032127,NT SERVICE\TrustedInstaller,0,1179817,APPLICATION PACKAGE AUTHORITY\ALL APPLICATION PACKAGES,,,,,,,,,,,,,,,4,,All critical directories exist and are accessible,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Verify critical system directories exist with appropriate permissions and ownership,,,,,,,,,,,,,,,,,,,,,,,,,,Medium,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,4,,,,,,,,,,,,,,
,,2025-08-19 13:26:03,,,,,,,,,,,,"Windows Update service running, regular updates installed",9.8,25,Review Required,,WIN-1.1,WIN_1_1_WindowsUpdate,Windows Update and Patch Management,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Service: Stopped, Recent updates: 3",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Windows Update service or recent updates missing,,,,,,,,,,,,,,,,,,,,2025-08-14 00:00:00,,,,,,,,,,,,,,,,,,,,,,,,,,3,Security Update,KB5063877,NT AUTHORITY\SYSTEM,"Thursday, August 14, 2025 12:00:00 AM",2025-08-14 00:00:00,Security Update,KB5062800,NT AUTHORITY\SYSTEM,"Wednesday, July 23, 2025 12:00:00 AM",2025-07-23 00:00:00,Update,KB5062068,NT AUTHORITY\SYSTEM,"Tuesday, July 22, 2025 12:00:00 AM",2025-07-22 00:00:00,Ensure Windows Update service is running and system receives regular security updates,,,,,,,,,,,,,,,,,,,,,,,,,,Critical,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2,1,None,True,,,,
,,2025-08-19 13:26:03,,,,,,,,,,,,All critical security registry settings properly configured,6.2,80,Compliant,,WIN-6.1,WIN_6_1_RegistrySecurity,Registry Security Settings,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Security registry keys found: 4/5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Most critical registry security settings are present,,,,,,,,,,,,,,,,,4,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Review and configure critical security registry settings according to security baselines,LM Authentication Level,LmCompatibilityLevel,HKLM:\SYSTEM\CurrentControlSet\Control\Lsa,Missing,Not Found,Disable LM Hash Storage,NoLMHash,HKLM:\SYSTEM\CurrentControlSet\Control\Lsa,Found,1,Netlogon Signing Required,RequireSignOrSeal,HKLM:\SYSTEM\CurrentControlSet\Services\Netlogon\Parameters,Found,1,User Account Control Enabled,EnableLUA,HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System,Found,1,UAC Admin Consent Behavior,ConsentPromptBehaviorAdmin,HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System,Found,0,Medium,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,5,,,,,,,,,
,False,2025-08-19 13:26:03,,,,,,,,,,,,"Strong password policy, Administrator disabled, Guest disabled",8.2,100,Compliant,,WIN-2.1,WIN_2_1_UserAccounts,User Account and Password Policies,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Min password length: 8, Admin enabled: , Guest enabled: False",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Password policy appears adequate,,,,,,,,,,,,,,,,,,,False,,True,2023-05-07 11:42:39,admlocal,None,True,False,None,DefaultAccount,None,False,False,None,Guest,None,False,False,None,WDAGUtilityAccount,2023-07-03 21:10:14,True,,,5,8,,,,,,,,,,,,,,,,,,"Implement strong password policy, disable default Administrator and Guest accounts",,,,,,,,,,,,,,,,,,,,,,,,,,High,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,4,,,,,,,,,,,,
,,,,Windows Server 2019 Security Controls Audit,10.76,2025-08-19 13:26:13,ad11bar,ce5bb813-41fa-4797-941d-fc729397aaa3,,,2025-08-19 13:26:03,Read-Only Assessment - Production Safe,,,,,,XXFinOrg01,,ComplianceSummary,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,Windows Server Security Controls v1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,5.1.17763.7553,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,7,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,7,,,,,,,,,,,,,,,
,,,8 Critical Windows Server Security Controls,,,,,ce5bb813-41fa-4797-941d-fc729397aaa3,,,2025-08-19 13:26:03,,ad11bar,,,,,XXFinOrg01,,AuditMetadata,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,XXFinOrg.com,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,64-bit,Microsoft Windows Server 2019 Standard,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Unknown,,,,,,,,XXFinOrg,17763.1.amd64fre.rs5_release.180914-1434,1809,None
50,,2025-08-19 13:26:03,,,,,,,,,,,,"Windows Firewall service running, all profiles enabled",7.8,75,Review Required,,WIN-3.1,WIN_3_1_WindowsFirewall,Windows Firewall Configuration,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Service: Running, Enabled profiles: /3",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,Windows Firewall configuration issues detected,0,0,0,%systemroot%\system32\LogFiles\Firewall\pfirewall.log,Domain,0,0,1,%systemroot%\system32\LogFiles\Firewall\pfirewall.log,Private,0,0,1,%systemroot%\system32\LogFiles\Firewall\pfirewall.log,Public,4,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Ensure Windows Firewall service is running and all profiles are enabled with appropriate rules,,,,,,,,,,,,,,,,,,,,,,,,,,High,,2,2,Connected User Experiences and Telemetry,0,2,1,Windows Remote Management (HTTP-In),3,2,1,Windows Remote Management (HTTP-In),4,2,1,Remote Desktop - User Mode (TCP-In),0,2,1,Remote Desktop - User Mode (UDP-In),0,2,1,Remote Desktop - Shadow (TCP-In),0,2,1,Cast to Device streaming server (HTTP-Streaming-In),1,2,1,Cast to Device streaming server (HTTP-Streaming-In),2,2,1,Cast to Device streaming server (HTTP-Streaming-In),4,2,1,Cast to Device streaming server (RTCP-Streaming-In),1,2,1,Cast to Device streaming server (RTCP-Streaming-In),2,2,1,Delivery Optimization (TCP-In),0,2,1,Delivery Optimization (UDP-In),0,2,1,AllJoyn Router (TCP-In),3,2,2,AllJoyn Router (TCP-Out),3,2,1,AllJoyn Router (UDP-In),3,2,2,AllJoyn Router (UDP-Out),3,2,1,DIAL protocol server (HTTP-In),1,2,1,DIAL protocol server (HTTP-In),2,2,2,Windows Device Management Enrollment Service (TCP out),0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,3,,,,,,,,,,
,,2025-08-19 13:26:03,,,,,,,False,auditpol command failed,,,,"Event Log service running, critical logs enabled with adequate retention",6.5,75,Review Required,,WIN-4.1,WIN_4_1_AuditLogging,Audit Logging and Event Log Settings,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Event Log service: Running, Enabled logs: /2",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2,,4,True,0,System,41943040,126812,True,0,Application,41943040,38256,,,Event logging service active,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Ensure Event Log service is running and critical event logs are enabled with appropriate retention policies,,,,,,,,,,,,,,,,,,,,,,,,,,Medium,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,
,,2025-08-19 13:26:03,,,,,,,,,,,,"Critical security services running, unnecessary services disabled",6.8,75,Review Required,,WIN-5.1,WIN_5_1_ServiceSecurity,Service Configurations and Security,6,Windows Update,wuauserv,2,1,Windows Defender Firewall,mpssvc,2,4,Windows Event Log,EventLog,2,4,Windows Management Instrumentation,Winmgmt,2,4,Remote Procedure Call (RPC),RpcSs,2,4,Background Intelligent Transfer Service,BITS,3,1,Cryptographic Services,CryptSvc,2,4,Windows Modules Installer,TrustedInstaller,2,4,"Total services: 255, Running: 109, Critical running: Microsoft.PowerShell.Commands.GenericMeasureInfo.Count",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Some critical security services are not running,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Ensure critical security services are running and unnecessary services are disabled,,,,,,,,,,,,,,,,,,,,,,,,,,Medium,109,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,146,,AEGASTService,LocalSystem,Running,AppVClient,LocalSystem,Stopped,AppXSvc,LocalSystem,Stopped,aspnet_state,NT AUTHORITY\NetworkService,Stopped,AudioEndpointBuilder,LocalSystem,Stopped,Audiosrv,NT AUTHORITY\LocalService,Stopped,AxInstSV,LocalSystem,Stopped,BFE,NT AUTHORITY\LocalService,Running,BITS,LocalSystem,Stopped,BrokerInfrastructure,LocalSystem,Running,BTAGService,NT AUTHORITY\LocalService,Stopped,AGRtuService,NT AUTHORITY\NetworkService,Running,AGWebCacheService,NT AUTHORITY\NetworkService,Running,AJRouter,NT AUTHORITY\LocalService,Stopped,ALG,NT AUTHORITY\LocalService,Stopped,AppHostSvc,localSystem,Running,AppIDSvc,NT Authority\LocalService,Stopped,Appinfo,LocalSystem,Running,AppMgmt,LocalSystem,Stopped,AppReadiness,LocalSystem,Stopped,,,,,,,,255,,,,,,,,
