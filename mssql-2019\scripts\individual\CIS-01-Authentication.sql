/*
================================================================================
CIS Controls v8 - Authentication Mode Assessment (CIS Control 4.1)
================================================================================
Description: Verifies SQL Server authentication mode configuration
Control ID: CIS-4.1
Risk Level: High
CVSS Score: 7.5

Baseline Requirement: Windows Authentication Mode Only
Security Impact: Mixed mode authentication increases attack surface by allowing 
                SQL authentication which may be subject to brute force attacks

Assessment Query: Checks if SQL Server is configured for Windows Authentication only
Compliance Check: SERVERPROPERTY('IsIntegratedSecurityOnly') = 1

Remediation: Use SQL Server Configuration Manager to set Authentication Mode 
            to "Windows Authentication mode" only
================================================================================
*/

-- CIS Control 4.1: SQL Server Authentication Mode Assessment
SELECT 
    'CIS-4.1' AS ControlID,
    'SQL Server Authentication Mode' AS ControlName,
    'High' AS RiskLevel,
    7.5 AS CVSSScore,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Windows Authentication Only'
        ELSE 'Mixed Mode Authentication'
    END AS CurrentValue,
    'Windows Authentication Only' AS BaselineValue,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 100
        ELSE 0
    END AS ComplianceScore,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Configuration meets CIS baseline requirements'
        ELSE 'Mixed mode authentication enabled - increases attack surface for brute force attacks'
    END AS Finding,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'No action required - Windows Authentication mode is properly configured'
        ELSE 'Change authentication mode to Windows Authentication only using SQL Server Configuration Manager'
    END AS Recommendation,
    GETDATE() AS AssessmentDate,
    @@SERVERNAME AS ServerName,
    SERVERPROPERTY('ProductVersion') AS SQLVersion,
    SERVERPROPERTY('Edition') AS SQLEdition
FOR JSON PATH, ROOT('AuthenticationModeAssessment');

-- Additional detail query for authentication mode configuration
SELECT 
    'Authentication Mode Details' AS AssessmentType,
    SERVERPROPERTY('IsIntegratedSecurityOnly') AS IsIntegratedSecurityOnly,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Windows Authentication Mode'
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 0 THEN 'Mixed Mode (SQL Server and Windows Authentication)'
        ELSE 'Unknown'
    END AS AuthenticationModeDescription,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Secure - Uses Windows security for authentication'
        ELSE 'Risk - Allows SQL Server authentication which may be vulnerable to attacks'
    END AS SecurityImplication,
    'Use SQL Server Configuration Manager to modify authentication mode' AS ConfigurationMethod
FOR JSON PATH, ROOT('AuthenticationModeDetails');

-- Query to check for SQL Server logins (only relevant in mixed mode)
SELECT 
    'SQL Server Logins Analysis' AS AssessmentType,
    COUNT(*) AS TotalSQLLogins,
    COUNT(CASE WHEN is_disabled = 0 THEN 1 END) AS EnabledSQLLogins,
    COUNT(CASE WHEN is_disabled = 1 THEN 1 END) AS DisabledSQLLogins,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'SQL logins not applicable in Windows Authentication mode'
        WHEN COUNT(CASE WHEN is_disabled = 0 THEN 1 END) = 0 THEN 'No enabled SQL logins found'
        ELSE CAST(COUNT(CASE WHEN is_disabled = 0 THEN 1 END) AS VARCHAR(10)) + ' enabled SQL login(s) present - review required'
    END AS LoginAnalysis
FROM sys.sql_logins
WHERE type = 'S' -- SQL Server authentication
FOR JSON PATH, ROOT('SQLLoginsAnalysis');

-- Security recommendations based on current configuration
SELECT 
    'Security Recommendations' AS AssessmentType,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Excellent - Windows Authentication provides stronger security'
        ELSE 'Critical - Consider migrating to Windows Authentication for improved security'
    END AS PrimaryRecommendation,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 0 THEN 'If mixed mode is required, ensure strong password policies and account lockout policies'
        ELSE 'Continue using Windows Authentication and leverage Active Directory security policies'
    END AS SecondaryRecommendation,
    'Regularly review authentication configuration as part of security assessments' AS OngoingRecommendation,
    'Monitor failed login attempts and implement alerting for suspicious activity' AS MonitoringRecommendation
FOR JSON PATH, ROOT('SecurityRecommendations');

/*
================================================================================
Remediation Steps for Non-Compliant Configuration:
================================================================================

1. Open SQL Server Configuration Manager
2. Navigate to SQL Server Services
3. Right-click on SQL Server instance
4. Select Properties
5. Go to Security tab
6. Select "Windows authentication mode"
7. Click OK
8. Restart SQL Server service for changes to take effect

Alternative T-SQL method (requires restart):
-- Note: This requires registry modification and service restart
-- Use Configuration Manager method instead for safety

Post-Remediation Verification:
SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') AS AuthMode;
-- Should return 1 for Windows Authentication mode

Security Benefits:
- Eliminates SQL Server authentication attack vectors
- Leverages Windows security policies and account management
- Provides centralized authentication through Active Directory
- Supports advanced authentication features (Kerberos, NTLM)
- Enables better audit trails through Windows security logs

================================================================================
*/
