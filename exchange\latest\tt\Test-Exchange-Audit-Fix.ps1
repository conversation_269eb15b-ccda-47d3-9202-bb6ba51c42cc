#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.0
# **Exchange Version:** Exchange Server 2016
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Server Mailbox Security Audit Script - Test Harness
================================================================================
Description: Test script to verify the fixes for JSON serialization and domain filtering issues

This script simulates Exchange cmdlets to test the audit script functionality
without requiring an actual Exchange server environment.
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$DomainFilter = "albaraka.com"
)

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Server Mailbox Security Audit Script - Test Harness" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Testing fixes for JSON serialization and domain filtering issues" -ForegroundColor White
Write-Host "Domain Filter: $DomainFilter" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# Mock Exchange cmdlets for testing
function Get-OrganizationConfig {
    return [PSCustomObject]@{
        Name = "Test Organization"
    }
}

function Get-ExchangeServer {
    return @(
        [PSCustomObject]@{
            Name = "TESTEXCH01"
            ServerRole = "Mailbox"
        }
    )
}

function Get-ManagementRoleAssignment {
    param(
        [string]$Role
    )
    
    if ($Role -eq "ApplicationImpersonation") {
        return @(
            [PSCustomObject]@{
                RoleAssignee = "TestAdmin@$DomainFilter"
                RoleAssigneeName = "Test Administrator"
                RoleAssigneeType = "User"
                AssignmentMethod = "Direct"
                IsValid = $true
                WhenCreated = Get-Date
                Role = "ApplicationImpersonation"
                RoleType = "ApplicationImpersonation"
                Enabled = $true
            }
        )
    } else {
        return @(
            [PSCustomObject]@{
                RoleAssignee = "ExchangeAdmin@$DomainFilter"
                RoleAssigneeName = "Exchange Administrator"
                RoleAssigneeType = "User"
                AssignmentMethod = "Direct"
                IsValid = $true
                WhenCreated = Get-Date
                Role = "Organization Management"
                RoleType = "Management"
                Enabled = $true
            },
            [PSCustomObject]@{
                RoleAssignee = "MailboxAdmin@$DomainFilter"
                RoleAssigneeName = "Mailbox Administrator"
                RoleAssigneeType = "User"
                AssignmentMethod = "Direct"
                IsValid = $true
                WhenCreated = Get-Date
                Role = "Recipient Management"
                RoleType = "Management"
                Enabled = $true
            }
        )
    }
}

function Get-Mailbox {
    param(
        [string]$Identity,
        [string]$Filter,
        [int]$ResultSize = 1000
    )
    
    # Create mock mailboxes for the specified domain
    $mockMailboxes = @()
    for ($i = 1; $i -le 5; $i++) {
        $mockMailboxes += [PSCustomObject]@{
            Identity = "TestUser$i@$DomainFilter"
            DisplayName = "Test User $i"
            PrimarySmtpAddress = "TestUser$i@$DomainFilter"
            DistinguishedName = "CN=TestUser$i,OU=Users,DC=test,DC=com"
            AuditEnabled = ($i % 2 -eq 0)  # Every other mailbox has audit enabled
            AuditLogAgeLimit = "90.00:00:00"
            GrantSendOnBehalfTo = if ($i -eq 1) { @("Delegate@$DomainFilter") } else { $null }
        }
    }
    
    if ($Filter) {
        # Simulate server-side filtering
        $domain = $Filter -replace ".*'.*@([^']+)'.*", '$1'
        return $mockMailboxes | Where-Object { $_.PrimarySmtpAddress -like "*@$domain" }
    } elseif ($Identity) {
        return $mockMailboxes | Where-Object { $_.Identity -eq $Identity } | Select-Object -First 1
    } else {
        return $mockMailboxes | Select-Object -First $ResultSize
    }
}

function Get-MailboxPermission {
    param(
        [string]$Identity
    )
    
    return @(
        [PSCustomObject]@{
            User = "TestDelegate@$DomainFilter"
            AccessRights = @("FullAccess")
            IsInherited = $false
            Deny = $false
        }
    )
}

function Get-ADPermission {
    param(
        [string]$Identity
    )
    
    return @(
        [PSCustomObject]@{
            User = "TestSender@$DomainFilter"
            ExtendedRights = @("Send-As")
            AccessControlType = "Allow"
            IsInherited = $false
        }
    )
}

function Get-AdminAuditLogConfig {
    return [PSCustomObject]@{
        AdminAuditLogEnabled = $true
    }
}

function Get-MailboxAuditBypassAssociation {
    return @(
        [PSCustomObject]@{
            Name = "ServiceAccount@$DomainFilter"
            AuditBypassEnabled = $true
        }
    )
}

# Test the main audit script
Write-Host "Running Exchange Mailbox Security Audit with test data..." -ForegroundColor Yellow
Write-Host ""

try {
    # Execute the main audit script with our mock functions
    & ".\Exchange-Mailbox-Security-Audit.ps1" -DomainFilter $DomainFilter -MaxMailboxSample 10
    
    Write-Host ""
    Write-Host "=================================================================================" -ForegroundColor Green
    Write-Host "Test completed successfully!" -ForegroundColor Green
    Write-Host "=================================================================================" -ForegroundColor Green
    
    # Check if output file was created
    $outputFiles = Get-ChildItem -Path "." -Filter "Exchange-Mailbox-Security-Results*.json"
    if ($outputFiles) {
        Write-Host "Output files created:" -ForegroundColor Cyan
        foreach ($file in $outputFiles) {
            Write-Host "  - $($file.Name) (Size: $($file.Length) bytes)" -ForegroundColor White
            
            # Validate JSON content
            try {
                $jsonContent = Get-Content $file.FullName -Raw | ConvertFrom-Json
                Write-Host "    JSON validation: PASSED" -ForegroundColor Green
                Write-Host "    Audit sections: $($jsonContent.PSObject.Properties.Count)" -ForegroundColor Gray
            } catch {
                Write-Host "    JSON validation: FAILED - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "No output files found - this may indicate an issue" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Test failed with error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test harness completed." -ForegroundColor White
