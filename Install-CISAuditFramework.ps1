#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    CIS Controls v8 Audit Framework Installation Script
    
.DESCRIPTION
    Installs and configures the CIS Controls v8 Audit Framework for Windows Server 2019.
    Sets up directory structure, validates prerequisites, and configures scheduled tasks.
    
.PARAMETER InstallPath
    Installation directory for the CIS Audit Framework (default: C:\CIS-Audit-Framework)
    
.PARAMETER CreateScheduledTask
    Create a scheduled task for automated monthly audits
    
.PARAMETER ConfigureEmailNotifications
    Configure email notification settings
    
.PARAMETER SMTPServer
    SMTP server for email notifications
    
.PARAMETER EmailFrom
    Email sender address
    
.PARAMETER EmailTo
    Email recipients for audit reports
    
.EXAMPLE
    .\Install-CISAuditFramework.ps1 -InstallPath "C:\CIS-Audit" -CreateScheduledTask
    
.EXAMPLE
    .\Install-CISAuditFramework.ps1 -ConfigureEmailNotifications -SMTPServer "smtp.company.com" -Email<PERSON>rom "<EMAIL>" -EmailTo "<EMAIL>"
#>

[CmdletBinding()]
param(
    [string]$InstallPath = "C:\CIS-Audit-Framework",
    [switch]$CreateScheduledTask,
    [switch]$ConfigureEmailNotifications,
    [string]$SMTPServer,
    [string]$EmailFrom,
    [string[]]$EmailTo
)

# Installation functions
function Write-InstallLog {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry -ForegroundColor $(
        switch ($Level) {
            "INFO" { "White" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
            "SUCCESS" { "Green" }
        }
    )
}

function Test-Prerequisites {
    Write-InstallLog "Checking prerequisites..." "INFO"
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        Write-InstallLog "PowerShell 5.1 or later is required. Current version: $($PSVersionTable.PSVersion)" "ERROR"
        return $false
    }
    Write-InstallLog "PowerShell version: $($PSVersionTable.PSVersion) - OK" "SUCCESS"
    
    # Check if running as Administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (!$currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        Write-InstallLog "Script must be run as Administrator" "ERROR"
        return $false
    }
    Write-InstallLog "Running as Administrator - OK" "SUCCESS"
    
    # Check Windows version
    $osVersion = [System.Environment]::OSVersion.Version
    if ($osVersion.Major -lt 10) {
        Write-InstallLog "Windows Server 2016 or later is recommended. Current version: $osVersion" "WARNING"
    } else {
        Write-InstallLog "Windows version: $osVersion - OK" "SUCCESS"
    }
    
    # Check available disk space (minimum 100MB)
    $drive = (Get-Item $InstallPath -ErrorAction SilentlyContinue)?.Root ?? (Split-Path $InstallPath -Qualifier)
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$drive'").FreeSpace
    if ($freeSpace -lt 100MB) {
        Write-InstallLog "Insufficient disk space. At least 100MB required." "ERROR"
        return $false
    }
    Write-InstallLog "Available disk space: $([math]::Round($freeSpace/1MB, 2)) MB - OK" "SUCCESS"
    
    return $true
}

function New-InstallationStructure {
    Write-InstallLog "Creating installation directory structure..." "INFO"
    
    try {
        # Create main installation directory
        if (!(Test-Path $InstallPath)) {
            New-Item -Path $InstallPath -ItemType Directory -Force | Out-Null
            Write-InstallLog "Created installation directory: $InstallPath" "SUCCESS"
        }
        
        # Create subdirectories
        $subdirs = @("Reports", "Logs", "Remediation", "Backups", "Examples", "Config")
        foreach ($subdir in $subdirs) {
            $subdirPath = Join-Path $InstallPath $subdir
            if (!(Test-Path $subdirPath)) {
                New-Item -Path $subdirPath -ItemType Directory -Force | Out-Null
                Write-InstallLog "Created directory: $subdirPath" "INFO"
            }
        }
        
        return $true
    }
    catch {
        Write-InstallLog "Failed to create directory structure: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Copy-FrameworkFiles {
    Write-InstallLog "Copying framework files..." "INFO"
    
    try {
        $sourceFiles = @(
            "CIS-Audit-Framework.ps1",
            "CIS-Report-Generator.ps1", 
            "CIS-Remediation-Generator.ps1",
            "CIS-Enterprise-Audit.ps1",
            "README.md"
        )
        
        foreach ($file in $sourceFiles) {
            if (Test-Path $file) {
                Copy-Item -Path $file -Destination $InstallPath -Force
                Write-InstallLog "Copied: $file" "SUCCESS"
            } else {
                Write-InstallLog "Source file not found: $file" "WARNING"
            }
        }
        
        # Copy examples if they exist
        if (Test-Path "examples") {
            Copy-Item -Path "examples\*" -Destination (Join-Path $InstallPath "Examples") -Recurse -Force
            Write-InstallLog "Copied example files" "SUCCESS"
        }
        
        return $true
    }
    catch {
        Write-InstallLog "Failed to copy framework files: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-ConfigurationFile {
    Write-InstallLog "Creating configuration file..." "INFO"
    
    $configPath = Join-Path $InstallPath "Config\CIS-Audit-Config.json"
    
    $config = @{
        InstallationInfo = @{
            InstallDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            InstallPath = $InstallPath
            Version = "1.0"
        }
        DefaultSettings = @{
            OutputPath = Join-Path $InstallPath "Reports"
            OutputFormat = "All"
            IncludeRemediation = $true
            MaxConcurrentJobs = 10
        }
        EmailSettings = @{
            Enabled = $ConfigureEmailNotifications
            SMTPServer = $SMTPServer
            EmailFrom = $EmailFrom
            EmailTo = $EmailTo
        }
        ScheduledTask = @{
            Enabled = $CreateScheduledTask
            Schedule = "Monthly"
            Time = "02:00AM"
        }
    }
    
    try {
        $config | ConvertTo-Json -Depth 10 | Out-File -FilePath $configPath -Encoding UTF8
        Write-InstallLog "Configuration file created: $configPath" "SUCCESS"
        return $true
    }
    catch {
        Write-InstallLog "Failed to create configuration file: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-ScheduledAuditTask {
    if (!$CreateScheduledTask) {
        return $true
    }
    
    Write-InstallLog "Creating scheduled audit task..." "INFO"
    
    try {
        $scriptPath = Join-Path $InstallPath "CIS-Audit-Framework.ps1"
        $outputPath = Join-Path $InstallPath "Reports\Scheduled"
        
        # Ensure scheduled reports directory exists
        if (!(Test-Path $outputPath)) {
            New-Item -Path $outputPath -ItemType Directory -Force | Out-Null
        }
        
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$scriptPath`" -OutputFormat All -OutputPath `"$outputPath`""
        $trigger = New-ScheduledTaskTrigger -Monthly -At "02:00AM" -DaysOfMonth 1
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -MultipleInstances IgnoreNew
        
        $task = Register-ScheduledTask -TaskName "CIS Controls v8 Monthly Audit" -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "Automated monthly CIS Controls v8 compliance audit for Windows Server 2019" -Force
        
        Write-InstallLog "Scheduled task created: CIS Controls v8 Monthly Audit" "SUCCESS"
        Write-InstallLog "Next run time: $($task.NextRunTime)" "INFO"
        
        return $true
    }
    catch {
        Write-InstallLog "Failed to create scheduled task: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-EmailConfiguration {
    if (!$ConfigureEmailNotifications -or !$SMTPServer -or !$EmailFrom -or !$EmailTo) {
        return $true
    }
    
    Write-InstallLog "Testing email configuration..." "INFO"
    
    try {
        $testSubject = "CIS Audit Framework - Installation Test"
        $testBody = @"
This is a test email from the CIS Controls v8 Audit Framework.

Installation Details:
- Install Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
- Install Path: $InstallPath
- SMTP Server: $SMTPServer
- Email From: $EmailFrom

If you receive this email, the email configuration is working correctly.
"@

        Send-MailMessage -To $EmailTo -From $EmailFrom -Subject $testSubject -Body $testBody -SmtpServer $SMTPServer
        Write-InstallLog "Test email sent successfully to: $($EmailTo -join ', ')" "SUCCESS"
        return $true
    }
    catch {
        Write-InstallLog "Email test failed: $($_.Exception.Message)" "WARNING"
        Write-InstallLog "Email notifications may not work properly" "WARNING"
        return $false
    }
}

function Show-InstallationSummary {
    Write-InstallLog "Installation Summary" "INFO"
    Write-Host "=" * 60 -ForegroundColor Green
    Write-Host "CIS Controls v8 Audit Framework Installation Complete" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Green
    
    Write-Host "`nInstallation Details:" -ForegroundColor Yellow
    Write-Host "  Install Path: $InstallPath" -ForegroundColor White
    Write-Host "  Install Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    
    Write-Host "`nFramework Components:" -ForegroundColor Yellow
    Write-Host "  ✓ CIS-Audit-Framework.ps1 - Main audit engine" -ForegroundColor Green
    Write-Host "  ✓ CIS-Report-Generator.ps1 - Report generation" -ForegroundColor Green
    Write-Host "  ✓ CIS-Remediation-Generator.ps1 - Remediation scripts" -ForegroundColor Green
    Write-Host "  ✓ CIS-Enterprise-Audit.ps1 - Enterprise auditing" -ForegroundColor Green
    
    Write-Host "`nDirectory Structure:" -ForegroundColor Yellow
    Write-Host "  $InstallPath\" -ForegroundColor White
    Write-Host "  ├── Reports\     (Audit reports)" -ForegroundColor White
    Write-Host "  ├── Logs\        (Audit logs)" -ForegroundColor White
    Write-Host "  ├── Remediation\ (Remediation scripts)" -ForegroundColor White
    Write-Host "  ├── Backups\     (Configuration backups)" -ForegroundColor White
    Write-Host "  ├── Examples\    (Usage examples)" -ForegroundColor White
    Write-Host "  └── Config\      (Configuration files)" -ForegroundColor White
    
    if ($CreateScheduledTask) {
        Write-Host "`nScheduled Task:" -ForegroundColor Yellow
        Write-Host "  ✓ Monthly audit task created" -ForegroundColor Green
        Write-Host "  ✓ Runs on 1st of each month at 2:00 AM" -ForegroundColor Green
    }
    
    if ($ConfigureEmailNotifications) {
        Write-Host "`nEmail Notifications:" -ForegroundColor Yellow
        Write-Host "  ✓ SMTP Server: $SMTPServer" -ForegroundColor Green
        Write-Host "  ✓ From: $EmailFrom" -ForegroundColor Green
        Write-Host "  ✓ To: $($EmailTo -join ', ')" -ForegroundColor Green
    }
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "  1. Review configuration in: $InstallPath\Config\CIS-Audit-Config.json" -ForegroundColor White
    Write-Host "  2. Run test audit: $InstallPath\CIS-Audit-Framework.ps1" -ForegroundColor White
    Write-Host "  3. Check examples: $InstallPath\Examples\Run-CIS-Audit-Examples.ps1" -ForegroundColor White
    Write-Host "  4. Review documentation: $InstallPath\README.md" -ForegroundColor White
    
    Write-Host "`nQuick Start Command:" -ForegroundColor Yellow
    Write-Host "  Set-Location '$InstallPath'" -ForegroundColor Cyan
    Write-Host "  .\CIS-Audit-Framework.ps1 -OutputFormat HTML -IncludeRemediation" -ForegroundColor Cyan
}

# Main installation process
Write-Host "CIS Controls v8 Audit Framework Installer" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Validate prerequisites
if (!(Test-Prerequisites)) {
    Write-InstallLog "Prerequisites check failed. Installation aborted." "ERROR"
    exit 1
}

# Create installation structure
if (!(New-InstallationStructure)) {
    Write-InstallLog "Failed to create installation structure. Installation aborted." "ERROR"
    exit 1
}

# Copy framework files
if (!(Copy-FrameworkFiles)) {
    Write-InstallLog "Failed to copy framework files. Installation aborted." "ERROR"
    exit 1
}

# Create configuration file
if (!(New-ConfigurationFile)) {
    Write-InstallLog "Failed to create configuration file. Installation may be incomplete." "WARNING"
}

# Create scheduled task
if (!(New-ScheduledAuditTask)) {
    Write-InstallLog "Failed to create scheduled task. Manual scheduling may be required." "WARNING"
}

# Test email configuration
Test-EmailConfiguration | Out-Null

# Show installation summary
Show-InstallationSummary

Write-InstallLog "CIS Controls v8 Audit Framework installation completed successfully!" "SUCCESS"
