#Requires -Version 5.1
# #Requires -RunAsAdministrator  # Temporarily commented for testing

<#
.SYNOPSIS
    CIS Controls v8 Audit Framework for Windows Server 2019
    
.DESCRIPTION
    Comprehensive PowerShell script to audit Windows Server 2019 systems against
    the 20 most critical CIS Controls v8 technical configurations.
    
.PARAMETER ComputerName
    Target computer(s) to audit. Defaults to local computer.
    
.PARAMETER OutputPath
    Directory path for audit reports. Defaults to current directory.
    
.PARAMETER OutputFormat
    Report format: CSV, HTML, JSON, or All. Defaults to All.
    
.PARAMETER IncludeRemediation
    Include remediation commands in the report.
    
.PARAMETER Credential
    Credentials for remote computer access.
    
.EXAMPLE
    .\CIS-Audit-Framework.ps1 -OutputFormat HTML -IncludeRemediation
    
.EXAMPLE
    .\CIS-Audit-Framework.ps1 -ComputerName "Server01","Server02" -OutputPath "C:\Audits"
#>

[CmdletBinding()]
param(
    [string[]]$ComputerName = @($env:COMPUTERNAME),
    [string]$OutputPath = (Get-Location).Path,
    [ValidateSet("CSV", "HTML", "JSON", "All")]
    [string]$OutputFormat = "All",
    [switch]$IncludeRemediation,
    [PSCredential]$Credential
)

# Global Variables
$Script:AuditResults = @()
$Script:StartTime = Get-Date
$Script:LogFile = Join-Path $OutputPath "CIS-Audit-Log-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"

# Initialize logging
function Write-AuditLog {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry -ForegroundColor $(
        switch ($Level) {
            "INFO" { "White" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
            "SUCCESS" { "Green" }
        }
    )
    
    Add-Content -Path $Script:LogFile -Value $logEntry
}

# CIS Control Baseline Definitions
$Script:CISBaselines = @{
    "1.1" = @{
        Name = "Asset Inventory - Network Discovery"
        Type = "Service"
        Path = "HKLM:\SYSTEM\CurrentControlSet\Services\FDResPub"
        Setting = "Start"
        RecommendedValue = 4
        Description = "Function Discovery Resource Publication service should be disabled"
        RiskLevel = "Medium"
    }
    "1.2" = @{
        Name = "Asset Inventory - SSDP Discovery"
        Type = "Service"
        Path = "HKLM:\SYSTEM\CurrentControlSet\Services\SSDPSRV"
        Setting = "Start"
        RecommendedValue = 4
        Description = "SSDP Discovery service should be disabled"
        RiskLevel = "Medium"
    }
    "2.1" = @{
        Name = "Software Inventory - Windows Installer"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\Installer"
        Setting = "AlwaysInstallElevated"
        RecommendedValue = 0
        Description = "Always install with elevated privileges should be disabled"
        RiskLevel = "High"
    }
    "3.1" = @{
        Name = "Data Protection - BitLocker"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\FVE"
        Setting = "UseAdvancedStartup"
        RecommendedValue = 1
        Description = "BitLocker should require additional authentication at startup"
        RiskLevel = "High"
    }
    "4.1" = @{
        Name = "Secure Configuration - Guest Account"
        Type = "SecurityPolicy"
        Path = "Guest"
        Setting = "AccountDisabled"
        RecommendedValue = $true
        Description = "Guest account should be disabled"
        RiskLevel = "High"
    }
    "4.2" = @{
        Name = "Secure Configuration - Anonymous SID Enumeration"
        Type = "Registry"
        Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"
        Setting = "RestrictAnonymousSAM"
        RecommendedValue = 1
        Description = "Do not allow anonymous enumeration of SAM accounts"
        RiskLevel = "Medium"
    }
    "5.1" = @{
        Name = "Account Management - Password Policy"
        Type = "SecurityPolicy"
        Path = "PasswordPolicy"
        Setting = "MinimumPasswordLength"
        RecommendedValue = 14
        Description = "Minimum password length should be 14 characters"
        RiskLevel = "High"
    }
    "5.2" = @{
        Name = "Account Management - Account Lockout"
        Type = "SecurityPolicy"
        Path = "AccountLockout"
        Setting = "LockoutThreshold"
        RecommendedValue = 5
        Description = "Account lockout threshold should be 5 or fewer invalid attempts"
        RiskLevel = "Medium"
    }
    "6.1" = @{
        Name = "Access Control - User Rights Assignment"
        Type = "SecurityPolicy"
        Path = "UserRights"
        Setting = "SeNetworkLogonRight"
        RecommendedValue = @("Administrators", "Authenticated Users")
        Description = "Access this computer from the network user right assignment"
        RiskLevel = "High"
    }
    "7.1" = @{
        Name = "Vulnerability Management - Windows Update"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
        Setting = "NoAutoUpdate"
        RecommendedValue = 0
        Description = "Configure Automatic Updates should be enabled"
        RiskLevel = "High"
    }
    "8.1" = @{
        Name = "Audit Log Management - Success Logon"
        Type = "AuditPolicy"
        Path = "Logon/Logoff"
        Setting = "Logon"
        RecommendedValue = "Success"
        Description = "Audit logon events should be configured for Success"
        RiskLevel = "Medium"
    }
    "8.2" = @{
        Name = "Audit Log Management - Failure Logon"
        Type = "AuditPolicy"
        Path = "Logon/Logoff"
        Setting = "Logon"
        RecommendedValue = "Failure"
        Description = "Audit logon events should be configured for Failure"
        RiskLevel = "Medium"
    }
    "9.1" = @{
        Name = "Email/Web Protection - SmartScreen"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\System"
        Setting = "EnableSmartScreen"
        RecommendedValue = 1
        Description = "Windows Defender SmartScreen should be enabled"
        RiskLevel = "Medium"
    }
    "10.1" = @{
        Name = "Malware Defenses - Windows Defender"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender"
        Setting = "DisableAntiSpyware"
        RecommendedValue = 0
        Description = "Windows Defender Antivirus should be enabled"
        RiskLevel = "High"
    }
    "10.2" = @{
        Name = "Malware Defenses - Real-time Protection"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection"
        Setting = "DisableRealtimeMonitoring"
        RecommendedValue = 0
        Description = "Windows Defender real-time protection should be enabled"
        RiskLevel = "High"
    }
    "11.1" = @{
        Name = "Data Recovery - System Restore"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore"
        Setting = "DisableSR"
        RecommendedValue = 0
        Description = "System Restore should be enabled"
        RiskLevel = "Medium"
    }
    "12.1" = @{
        Name = "Network Infrastructure - Windows Firewall Domain"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\DomainProfile"
        Setting = "EnableFirewall"
        RecommendedValue = 1
        Description = "Windows Firewall Domain Profile should be enabled"
        RiskLevel = "High"
    }
    "12.2" = @{
        Name = "Network Infrastructure - Windows Firewall Private"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PrivateProfile"
        Setting = "EnableFirewall"
        RecommendedValue = 1
        Description = "Windows Firewall Private Profile should be enabled"
        RiskLevel = "High"
    }
    "12.3" = @{
        Name = "Network Infrastructure - Windows Firewall Public"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PublicProfile"
        Setting = "EnableFirewall"
        RecommendedValue = 1
        Description = "Windows Firewall Public Profile should be enabled"
        RiskLevel = "High"
    }
    "16.1" = @{
        Name = "Application Security - PowerShell Execution Policy"
        Type = "Registry"
        Path = "HKLM:\SOFTWARE\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell"
        Setting = "ExecutionPolicy"
        RecommendedValue = "RemoteSigned"
        Description = "PowerShell execution policy should be RemoteSigned or more restrictive"
        RiskLevel = "Medium"
    }
}

# Configuration Reading Functions
function Get-RegistryValue {
    param(
        [string]$ComputerName,
        [string]$Path,
        [string]$Setting,
        [PSCredential]$Credential
    )

    try {
        $scriptBlock = {
            param($Path, $Setting)
            if (Test-Path $Path) {
                $value = Get-ItemProperty -Path $Path -Name $Setting -ErrorAction SilentlyContinue
                if ($value) {
                    return $value.$Setting
                }
            }
            return $null
        }

        if ($ComputerName -eq $env:COMPUTERNAME) {
            return & $scriptBlock $Path $Setting
        } else {
            $params = @{
                ComputerName = $ComputerName
                ScriptBlock = $scriptBlock
                ArgumentList = @($Path, $Setting)
            }
            if ($Credential) { $params.Credential = $Credential }
            return Invoke-Command @params
        }
    }
    catch {
        Write-AuditLog "Error reading registry value $Path\$Setting on $ComputerName`: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Get-ServiceConfiguration {
    param(
        [string]$ComputerName,
        [string]$ServiceName,
        [PSCredential]$Credential
    )

    try {
        $scriptBlock = {
            param($ServiceName)
            $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
            if ($service) {
                return @{
                    Status = $service.Status
                    StartType = $service.StartType
                }
            }
            return $null
        }

        if ($ComputerName -eq $env:COMPUTERNAME) {
            return & $scriptBlock $ServiceName
        } else {
            $params = @{
                ComputerName = $ComputerName
                ScriptBlock = $scriptBlock
                ArgumentList = @($ServiceName)
            }
            if ($Credential) { $params.Credential = $Credential }
            return Invoke-Command @params
        }
    }
    catch {
        Write-AuditLog "Error reading service $ServiceName on $ComputerName`: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Get-SecurityPolicyValue {
    param(
        [string]$ComputerName,
        [string]$PolicyType,
        [string]$Setting,
        [PSCredential]$Credential
    )

    try {
        $scriptBlock = {
            param($PolicyType, $Setting)

            switch ($PolicyType) {
                "PasswordPolicy" {
                    $secpol = secedit /export /cfg "$env:TEMP\secpol.cfg" /quiet
                    $content = Get-Content "$env:TEMP\secpol.cfg"
                    Remove-Item "$env:TEMP\secpol.cfg" -Force

                    switch ($Setting) {
                        "MinimumPasswordLength" {
                            $line = $content | Where-Object { $_ -match "MinimumPasswordLength" }
                            if ($line) {
                                return [int]($line -split "=")[1].Trim()
                            }
                        }
                    }
                }
                "AccountLockout" {
                    $secpol = secedit /export /cfg "$env:TEMP\secpol.cfg" /quiet
                    $content = Get-Content "$env:TEMP\secpol.cfg"
                    Remove-Item "$env:TEMP\secpol.cfg" -Force

                    switch ($Setting) {
                        "LockoutThreshold" {
                            $line = $content | Where-Object { $_ -match "LockoutBadCount" }
                            if ($line) {
                                return [int]($line -split "=")[1].Trim()
                            }
                        }
                    }
                }
                "Guest" {
                    $user = Get-LocalUser -Name "Guest" -ErrorAction SilentlyContinue
                    if ($user) {
                        return !$user.Enabled
                    }
                }
            }
            return $null
        }

        if ($ComputerName -eq $env:COMPUTERNAME) {
            return & $scriptBlock $PolicyType $Setting
        } else {
            $params = @{
                ComputerName = $ComputerName
                ScriptBlock = $scriptBlock
                ArgumentList = @($PolicyType, $Setting)
            }
            if ($Credential) { $params.Credential = $Credential }
            return Invoke-Command @params
        }
    }
    catch {
        Write-AuditLog "Error reading security policy $PolicyType\$Setting on $ComputerName`: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Get-AuditPolicyValue {
    param(
        [string]$ComputerName,
        [string]$Category,
        [string]$Subcategory,
        [PSCredential]$Credential
    )

    try {
        $scriptBlock = {
            param($Category, $Subcategory)
            $auditpol = auditpol /get /category:"$Category" /r
            $csvData = $auditpol | ConvertFrom-Csv
            $setting = $csvData | Where-Object { $_."Subcategory" -eq $Subcategory }
            if ($setting) {
                return $setting."Inclusion Setting"
            }
            return $null
        }

        if ($ComputerName -eq $env:COMPUTERNAME) {
            return & $scriptBlock $Category $Subcategory
        } else {
            $params = @{
                ComputerName = $ComputerName
                ScriptBlock = $scriptBlock
                ArgumentList = @($Category, $Subcategory)
            }
            if ($Credential) { $params.Credential = $Credential }
            return Invoke-Command @params
        }
    }
    catch {
        Write-AuditLog "Error reading audit policy $Category\$Subcategory on $ComputerName`: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# Main Audit Function
function Invoke-CISControlAudit {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )

    Write-AuditLog "Starting audit for computer: $ComputerName" "INFO"
    $computerResults = @()

    foreach ($controlId in $Script:CISBaselines.Keys) {
        $control = $Script:CISBaselines[$controlId]
        Write-AuditLog "Auditing Control $controlId`: $($control.Name)" "INFO"

        $currentValue = $null
        $complianceStatus = "Not Configured"
        $remediation = ""

        try {
            switch ($control.Type) {
                "Registry" {
                    $currentValue = Get-RegistryValue -ComputerName $ComputerName -Path $control.Path -Setting $control.Setting -Credential $Credential
                    if ($null -ne $currentValue) {
                        $complianceStatus = if ($currentValue -eq $control.RecommendedValue) { "Compliant" } else { "Non-Compliant" }
                        if ($complianceStatus -eq "Non-Compliant") {
                            $remediation = "Set-ItemProperty -Path '$($control.Path)' -Name '$($control.Setting)' -Value $($control.RecommendedValue)"
                        }
                    } else {
                        $remediation = "New-Item -Path '$($control.Path)' -Force; Set-ItemProperty -Path '$($control.Path)' -Name '$($control.Setting)' -Value $($control.RecommendedValue)"
                    }
                }
                "Service" {
                    $serviceName = ($control.Path -split "\\")[-1]
                    $serviceConfig = Get-ServiceConfiguration -ComputerName $ComputerName -ServiceName $serviceName -Credential $Credential
                    if ($serviceConfig) {
                        $currentValue = $serviceConfig.StartType
                        $complianceStatus = if ($serviceConfig.StartType -eq $control.RecommendedValue) { "Compliant" } else { "Non-Compliant" }
                        if ($complianceStatus -eq "Non-Compliant") {
                            $startType = switch ($control.RecommendedValue) {
                                4 { "Disabled" }
                                3 { "Manual" }
                                2 { "Automatic" }
                                default { "Manual" }
                            }
                            $remediation = "Set-Service -Name '$serviceName' -StartupType $startType"
                        }
                    }
                }
                "SecurityPolicy" {
                    $currentValue = Get-SecurityPolicyValue -ComputerName $ComputerName -PolicyType $control.Path -Setting $control.Setting -Credential $Credential
                    if ($null -ne $currentValue) {
                        $complianceStatus = if ($currentValue -eq $control.RecommendedValue) { "Compliant" } else { "Non-Compliant" }
                        if ($complianceStatus -eq "Non-Compliant") {
                            switch ($control.Path) {
                                "PasswordPolicy" {
                                    $remediation = "Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Password Policy"
                                }
                                "AccountLockout" {
                                    $remediation = "Configure via Group Policy: Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Account Lockout Policy"
                                }
                                "Guest" {
                                    $remediation = "Disable-LocalUser -Name 'Guest'"
                                }
                            }
                        }
                    }
                }
                "AuditPolicy" {
                    $currentValue = Get-AuditPolicyValue -ComputerName $ComputerName -Category $control.Path -Subcategory $control.Setting -Credential $Credential
                    if ($null -ne $currentValue) {
                        $complianceStatus = if ($currentValue -match $control.RecommendedValue) { "Compliant" } else { "Non-Compliant" }
                        if ($complianceStatus -eq "Non-Compliant") {
                            $remediation = "auditpol /set /subcategory:'$($control.Setting)' /$($control.RecommendedValue.ToLower()):enable"
                        }
                    }
                }
            }
        }
        catch {
            Write-AuditLog "Error auditing control $controlId on $ComputerName`: $($_.Exception.Message)" "ERROR"
            $complianceStatus = "Error"
        }

        $result = [PSCustomObject]@{
            ComputerName = $ComputerName
            ControlID = $controlId
            ControlName = $control.Name
            Description = $control.Description
            Type = $control.Type
            CurrentValue = $currentValue
            RecommendedValue = $control.RecommendedValue
            ComplianceStatus = $complianceStatus
            RiskLevel = $control.RiskLevel
            Remediation = if ($IncludeRemediation) { $remediation } else { "" }
            AuditDate = $Script:StartTime
        }

        $computerResults += $result
    }

    Write-AuditLog "Completed audit for computer: $ComputerName" "SUCCESS"
    return $computerResults
}

# Comparison and Analysis Functions
function Get-ComplianceSummary {
    param([array]$Results)

    $summary = @{
        TotalControls = $Results.Count
        Compliant = ($Results | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
        NonCompliant = ($Results | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
        NotConfigured = ($Results | Where-Object { $_.ComplianceStatus -eq "Not Configured" }).Count
        Errors = ($Results | Where-Object { $_.ComplianceStatus -eq "Error" }).Count
        HighRiskIssues = ($Results | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "High" }).Count
        MediumRiskIssues = ($Results | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" -and $_.RiskLevel -eq "Medium" }).Count
        CompliancePercentage = [math]::Round((($Results | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count / $Results.Count) * 100, 2)
    }

    return $summary
}

# Load Report Generation Functions
. (Join-Path $PSScriptRoot "CIS-Report-Generator.ps1")

# Main Execution
Write-AuditLog "Starting CIS Controls v8 Audit for Windows Server 2019" "INFO"
Write-AuditLog "Target Systems: $($ComputerName -join ', ')" "INFO"
Write-AuditLog "Output Path: $OutputPath" "INFO"
Write-AuditLog "Output Format: $OutputFormat" "INFO"

# Validate output directory
if (!(Test-Path $OutputPath)) {
    try {
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
        Write-AuditLog "Created output directory: $OutputPath" "INFO"
    }
    catch {
        Write-AuditLog "Failed to create output directory: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Execute audit for each computer
foreach ($computer in $ComputerName) {
    try {
        Write-AuditLog "Testing connectivity to $computer" "INFO"

        # Test connectivity
        if ($computer -ne $env:COMPUTERNAME) {
            $testConnection = Test-Connection -ComputerName $computer -Count 1 -Quiet
            if (!$testConnection) {
                Write-AuditLog "Cannot reach computer $computer" "ERROR"
                continue
            }
        }

        # Run audit
        $computerResults = Invoke-CISControlAudit -ComputerName $computer -Credential $Credential
        $Script:AuditResults += $computerResults

        Write-AuditLog "Successfully audited $computer - $($computerResults.Count) controls evaluated" "SUCCESS"
    }
    catch {
        Write-AuditLog "Failed to audit computer $computer`: $($_.Exception.Message)" "ERROR"
    }
}

if ($Script:AuditResults.Count -eq 0) {
    Write-AuditLog "No audit results collected. Exiting." "ERROR"
    exit 1
}

# Generate compliance summary
$summary = Get-ComplianceSummary -Results $Script:AuditResults

Write-AuditLog "Audit completed. Generating reports..." "INFO"
Write-AuditLog "Overall compliance: $($summary.CompliancePercentage)%" "INFO"
Write-AuditLog "High risk issues: $($summary.HighRiskIssues)" "WARNING"

# Generate reports based on output format
$reportPaths = @()

switch ($OutputFormat) {
    "CSV" {
        $csvResult = Export-CISAuditToCSV -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary
        if ($csvResult) { $reportPaths += $csvResult.ReportPath, $csvResult.SummaryPath }
    }
    "HTML" {
        $htmlResult = Export-CISAuditToHTML -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary
        if ($htmlResult) { $reportPaths += $htmlResult }
    }
    "JSON" {
        $jsonResult = Export-CISAuditToJSON -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary
        if ($jsonResult) { $reportPaths += $jsonResult }
    }
    "All" {
        $csvResult = Export-CISAuditToCSV -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary
        $htmlResult = Export-CISAuditToHTML -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary
        $jsonResult = Export-CISAuditToJSON -Results $Script:AuditResults -OutputPath $OutputPath -Summary $summary

        if ($csvResult) { $reportPaths += $csvResult.ReportPath, $csvResult.SummaryPath }
        if ($htmlResult) { $reportPaths += $htmlResult }
        if ($jsonResult) { $reportPaths += $jsonResult }
    }
}

# Final summary
$endTime = Get-Date
$duration = $endTime - $Script:StartTime

Write-AuditLog "=== AUDIT SUMMARY ===" "INFO"
Write-AuditLog "Systems Audited: $(($Script:AuditResults | Select-Object -Unique ComputerName).Count)" "INFO"
Write-AuditLog "Controls Evaluated: $($summary.TotalControls)" "INFO"
Write-AuditLog "Compliant: $($summary.Compliant)" "SUCCESS"
Write-AuditLog "Non-Compliant: $($summary.NonCompliant)" "WARNING"
Write-AuditLog "Not Configured: $($summary.NotConfigured)" "WARNING"
Write-AuditLog "Errors: $($summary.Errors)" "ERROR"
Write-AuditLog "Overall Compliance: $($summary.CompliancePercentage)%" "INFO"
Write-AuditLog "High Risk Issues: $($summary.HighRiskIssues)" "WARNING"
Write-AuditLog "Medium Risk Issues: $($summary.MediumRiskIssues)" "WARNING"
Write-AuditLog "Audit Duration: $($duration.ToString('hh\:mm\:ss'))" "INFO"
Write-AuditLog "Reports Generated: $($reportPaths.Count)" "INFO"

foreach ($path in $reportPaths) {
    Write-AuditLog "Report: $path" "INFO"
}

Write-AuditLog "CIS Controls v8 audit completed successfully!" "SUCCESS"

# Return results for programmatic use
return @{
    Results = $Script:AuditResults
    Summary = $summary
    ReportPaths = $reportPaths
    Duration = $duration
}
