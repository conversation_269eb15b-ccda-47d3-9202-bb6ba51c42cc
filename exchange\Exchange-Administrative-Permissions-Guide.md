# Exchange Server 2016 Administrative Permissions and Mailbox Access Guide

## 🔐 Administrative Permission Types for Mailbox Access

### 1. Full Access Permission
**Description:** Allows complete access to another user's mailbox, including reading, sending, and managing all content.

**Technical Details:**
- **Cmdlet:** `Add-MailboxPermission -Identity <mailbox> -User <user> -AccessRights FullAccess`
- **Inheritance:** Applies to mailbox and all folders
- **Visibility:** Mailbox appears in Outlook/OWA of the delegate
- **Logging:** All actions logged if mailbox auditing enabled

**Security Implications:**
- **High Risk:** Complete mailbox access including sensitive data
- **Audit Trail:** Actions appear as the delegate user, not original owner
- **Data Exposure:** Access to all historical and future emails
- **Compliance Risk:** May violate data privacy regulations

### 2. Send As Permission
**Description:** Allows sending emails that appear to come from another user's mailbox.

**Technical Details:**
- **Cmdlet:** `Add-ADPermission -Identity <mailbox> -User <user> -ExtendedRights "Send As"`
- **Scope:** Sending emails only, no read access
- **Appearance:** Emails show original mailbox owner as sender
- **Authentication:** Requires explicit permission grant

**Security Implications:**
- **Medium Risk:** Potential for email impersonation
- **Reputation Risk:** Malicious emails could damage sender's reputation
- **Legal Risk:** Unauthorized communications on behalf of others
- **Audit Requirement:** Should be logged and monitored

### 3. Send on Behalf Permission
**Description:** Allows sending emails on behalf of another user with clear delegation indication.

**Technical Details:**
- **Cmdlet:** `Set-Mailbox -Identity <mailbox> -GrantSendOnBehalfTo <user>`
- **Scope:** Sending emails only, no read access
- **Appearance:** Shows "From: Delegate on behalf of Owner"
- **Transparency:** Clear indication of delegation

**Security Implications:**
- **Low Risk:** Transparent delegation with clear attribution
- **Accountability:** Clear audit trail of who sent what
- **Business Use:** Common for assistant/executive relationships
- **Compliance Friendly:** Maintains clear delegation records

### 4. Exchange Impersonation
**Description:** Application-level permission allowing programmatic access to any mailbox.

**Technical Details:**
- **Cmdlet:** `New-ManagementRoleAssignment -Role "ApplicationImpersonation" -User <user>`
- **Scope:** Typically organization-wide or specific database
- **Usage:** Primarily for applications and service accounts
- **Access Method:** Through Exchange Web Services (EWS) API

**Security Implications:**
- **Critical Risk:** Broad access to multiple/all mailboxes
- **Service Account Risk:** Often assigned to service accounts
- **Monitoring Required:** Must be heavily audited and monitored
- **Principle of Least Privilege:** Should be restricted to necessary applications only

## 🏢 Built-in Administrative Roles with Mailbox Access

### Organization Management
**Inherent Permissions:**
- Full Access to all mailboxes (via role assignment)
- Send As permissions for all mailboxes
- Exchange Impersonation rights
- Complete Exchange configuration control

**Members by Default:**
- Exchange installation account
- Domain Admins (inherited)
- Enterprise Admins (inherited)

**Security Considerations:**
- **Highest Risk:** Complete Exchange environment control
- **Audit Requirement:** All actions must be logged and monitored
- **Membership Control:** Strictly limit membership
- **Regular Review:** Quarterly access reviews recommended

### Recipient Management
**Inherent Permissions:**
- Create, modify, and delete mailboxes
- Manage mailbox permissions
- Access mailbox properties and settings
- Limited content access (configuration only)

**Security Considerations:**
- **High Risk:** Can grant themselves mailbox access
- **Indirect Access:** Can modify permissions to gain access
- **Monitoring Required:** Permission changes should be audited

### View-Only Organization Management
**Inherent Permissions:**
- Read-only access to Exchange configuration
- View mailbox properties and settings
- No content access to mailboxes
- No modification capabilities

**Security Considerations:**
- **Low Risk:** Read-only access to configuration
- **No Content Access:** Cannot read mailbox contents
- **Audit Friendly:** Good for compliance and monitoring roles

### Discovery Management
**Inherent Permissions:**
- In-Place eDiscovery and Hold capabilities
- Search across multiple mailboxes
- Export search results
- Legal hold management

**Security Considerations:**
- **High Risk:** Can search and export sensitive content
- **Legal Purpose:** Intended for legal and compliance use
- **Audit Critical:** All discovery actions must be logged
- **Scope Control:** Should be limited to specific legal personnel

## 📊 Permission Comparison Matrix

| Permission Type | Read Access | Send Access | Modify Settings | Audit Visibility | Risk Level |
|----------------|-------------|-------------|-----------------|------------------|------------|
| **Full Access** | ✅ Complete | ✅ Yes | ❌ No | 🔍 High | 🔴 High |
| **Send As** | ❌ No | ✅ Yes (as owner) | ❌ No | 🔍 Medium | 🟡 Medium |
| **Send on Behalf** | ❌ No | ✅ Yes (on behalf) | ❌ No | 🔍 High | 🟢 Low |
| **Impersonation** | ✅ Complete | ✅ Yes | ❌ No | 🔍 Critical | 🔴 Critical |
| **Organization Mgmt** | ✅ Complete | ✅ Yes | ✅ Yes | 🔍 Critical | 🔴 Critical |

## 🔍 Auditing and Monitoring Best Practices

### Enable Mailbox Auditing
```powershell
# Enable auditing for all mailboxes
Get-Mailbox -ResultSize Unlimited | Set-Mailbox -AuditEnabled $true

# Configure audit log age limit
Get-Mailbox -ResultSize Unlimited | Set-Mailbox -AuditLogAgeLimit 90.00:00:00

# Set audit actions to log
Get-Mailbox -ResultSize Unlimited | Set-Mailbox -AuditOwner Create,HardDelete,MailboxLogin,MoveToDeletedItems,SoftDelete,Update
```

### Monitor Administrative Access
```powershell
# Get all Full Access permissions
Get-Mailbox -ResultSize Unlimited | Get-MailboxPermission | Where-Object {$_.AccessRights -eq "FullAccess" -and $_.IsInherited -eq $false}

# Get all Send As permissions
Get-Mailbox -ResultSize Unlimited | Get-ADPermission | Where-Object {$_.ExtendedRights -like "*Send-As*"}

# Get all Send on Behalf permissions
Get-Mailbox -ResultSize Unlimited | Where-Object {$_.GrantSendOnBehalfTo -ne $null}
```

### Regular Access Reviews
**Monthly Reviews:**
- Full Access permission assignments
- Send As permission grants
- Administrative role membership changes
- Service account permissions

**Quarterly Reviews:**
- Complete permission inventory
- Business justification validation
- Removal of unnecessary permissions
- Compliance attestation

### Audit Log Analysis
**Key Events to Monitor:**
- **Event ID 4624:** Successful logon to mailbox
- **Event ID 4648:** Logon using explicit credentials
- **Exchange Audit Logs:** MailboxLogin, FolderBind, SendAs events
- **PowerShell Logs:** Administrative cmdlet execution

## 🛡️ Security Recommendations

### Principle of Least Privilege
1. **Grant minimum required permissions** for business function
2. **Use Send on Behalf** instead of Send As when possible
3. **Avoid Full Access** unless absolutely necessary
4. **Limit Organization Management** membership strictly

### Permission Management
1. **Document all permission grants** with business justification
2. **Set expiration dates** for temporary access
3. **Regular access reviews** and cleanup
4. **Automated monitoring** for permission changes

### Monitoring and Alerting
1. **Real-time alerts** for administrative permission changes
2. **Daily reports** of mailbox access by non-owners
3. **Weekly summaries** of administrative actions
4. **Monthly compliance** reports for management

### Compliance Considerations
1. **Data Privacy Laws:** GDPR, CCPA may restrict access
2. **Industry Regulations:** HIPAA, SOX, PCI DSS requirements
3. **Legal Hold:** Discovery permissions for litigation
4. **Audit Requirements:** Maintain detailed access logs

## 📋 Permission Audit Checklist

### Daily Monitoring
- [ ] Review mailbox access by non-owners
- [ ] Monitor administrative permission changes
- [ ] Check for new Full Access grants
- [ ] Validate service account activities

### Weekly Reviews
- [ ] Analyze audit logs for suspicious activities
- [ ] Review Send As permission usage
- [ ] Check for dormant administrative accounts
- [ ] Validate business justifications

### Monthly Assessments
- [ ] Complete permission inventory
- [ ] Remove unnecessary permissions
- [ ] Update documentation
- [ ] Management reporting

### Quarterly Audits
- [ ] Comprehensive access review
- [ ] Compliance attestation
- [ ] Policy updates
- [ ] Training requirements assessment

---

**Document Version:** 1.0  
**Last Updated:** August 10, 2024  
**Next Review:** November 10, 2024  

*This guide should be reviewed quarterly and updated based on organizational security policies and compliance requirements.*
