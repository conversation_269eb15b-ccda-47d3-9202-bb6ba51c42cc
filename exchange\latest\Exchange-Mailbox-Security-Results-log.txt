[PS] C:\TEMP>.\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "albaraka.com"
[INFO] PowerShell Version: 5.1.14393.8244 - Compatible
=================================================================================
Exchange Server Mailbox Security Audit Script
=================================================================================
Script Version: 1.6.6 (September 11, 2025)
Created By: E.Z. Consultancy
PowerShell Version: 5.1.14393.8244
PowerShell Edition: Desktop
Execution Host: BARAKAEXCH02
Execution User: ad21bar
Execution Time: 2025-09-11 14:50:16
Domain Filter: albaraka.com
Max Sample Size: 500 mailboxes
Output Location: .\Exchange-Mailbox-Security-Results.json
=================================================================================
Audit ID: d5a384e6-81de-4c22-a7d0-8515cc487b59
=================================================================================

Step 1/7: Collecting Exchange environment metadata...
  Domain albaraka.com : 0 mailboxes
  [WARNING] Server-side counting failed, using client-side counting (may be slower)
  Domain filter applied: 149 mailboxes match filter
[SUCCESS] Environment metadata collected successfully
  Metadata sections: 17
Step 2/7: Performing administrator discovery...
  Performing administrator discovery across all domains...
  [ERROR] Administrator discovery failed: You cannot call a method on a null-valued expression.
[SUCCESS] Administrator discovery completed
Performing pre-flight validation...
[SUCCESS] Exchange PowerShell module is available
[SUCCESS] Exchange connectivity verified
Step 3/7: Assessing mailbox impersonation rights...
[SUCCESS] Mailbox impersonation rights assessed
Step 4/7: Assessing mailbox full access permissions...
  Applying domain filter for: albaraka.com
  Found 0 mailboxes matching domain filter
  No mailboxes found matching the criteria
  [INFO] Cross-domain analysis skipped (insufficient data or administrator discovery failed)
[SUCCESS] Full access permissions assessed
Step 5/7: Assessing mailbox audit logging configuration...
WARNING: By default, only the first 1000 items are returned. Use the ResultSize parameter to specify the number of
items returned. To return all items, specify "-ResultSize Unlimited". Be aware that, depending on the actual number of
items, returning all items can take a long time and consume a large amount of memory. Also, we don't recommend storing
the results in a variable. Instead, pipe the results to another task or script to perform batch changes.
[SUCCESS] Audit logging configuration assessed
Step 6/7: Assessing Send-As permissions...
[SUCCESS] Send-As permissions assessed
Step 7/7: Assessing Send-On-Behalf permissions...
[SUCCESS] Send-On-Behalf permissions assessed

Generating compliance summary...

=== AUDIT DATA COLLECTION SUMMARY ===
Total audit sections collected: 8
  - MBX_4_1_SendAsPermissions : SUCCESS
  - MBX_1_1_ImpersonationRights : SUCCESS
  - AuditMetadata : SUCCESS
  - MBX_2_1_FullAccessPermissions : SUCCESS
  - MBX_3_1_AuditLogging : SUCCESS
  - ComplianceSummary : SUCCESS
  - AdministratorDiscovery : SUCCESS
  - MBX_5_1_SendOnBehalfPermissions : SUCCESS
=======================================

Generating JSON output...
  Converting 8 audit sections to JSON...
  Preparing objects for JSON serialization...
  JSON conversion successful. Output size: 84022 characters

=================================================================================
Exchange Server Mailbox Security Audit Completed
=================================================================================
Audit ID: d5a384e6-81de-4c22-a7d0-8515cc487b59
End Time: 2025-09-11 14:50:34
Duration: 17.89 seconds
Controls Assessed: 5 (with cross-domain analysis)
Domain Filter Applied: albaraka.com
Output File: .\Exchange-Mailbox-Security-Results.json

[SUCCESS] JSON audit results saved successfully
[SUCCESS] Send the file '.\Exchange-Mailbox-Security-Results.json' for compliance analysis
=================================================================================

AUDIT SUMMARY:
- Total Controls: 5
- Successful Assessments: 5
- Failed Assessments: 0
- Success Rate: 100%
[PS] C:\TEMP>