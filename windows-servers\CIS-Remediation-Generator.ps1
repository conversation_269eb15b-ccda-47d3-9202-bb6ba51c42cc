#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    CIS Controls v8 Remediation Script Generator for Windows Server 2019
    
.DESCRIPTION
    Generates PowerShell remediation scripts based on CIS audit results to bring
    Windows Server 2019 systems into compliance with CIS Controls v8.
    
.PARAMETER AuditResultsPath
    Path to the JSON audit results file
    
.PARAMETER ComputerName
    Target computer(s) for remediation. Defaults to local computer.
    
.PARAMETER OutputPath
    Directory path for remediation scripts. Defaults to current directory.
    
.PARAMETER GenerateGroupPolicyScript
    Generate Group Policy remediation commands
    
.PARAMETER CreateBackup
    Create backup of current settings before remediation
    
.EXAMPLE
    .\CIS-Remediation-Generator.ps1 -AuditResultsPath "C:\Audits\results.json" -CreateBackup
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory)]
    [string]$AuditResultsPath,
    [string[]]$ComputerName = @($env:COMPUTERNAME),
    [string]$OutputPath = (Get-Location).Path,
    [switch]$GenerateGroupPolicyScript,
    [switch]$CreateBackup,
    [PSCredential]$Credential
)

# Load audit results
if (!(Test-Path $AuditResultsPath)) {
    Write-Error "Audit results file not found: $AuditResultsPath"
    exit 1
}

try {
    $auditData = Get-Content $AuditResultsPath | ConvertFrom-Json
    $nonCompliantResults = $auditData.Results | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }
}
catch {
    Write-Error "Failed to load audit results: $($_.Exception.Message)"
    exit 1
}

if ($nonCompliantResults.Count -eq 0) {
    Write-Host "No non-compliant controls found. No remediation needed." -ForegroundColor Green
    exit 0
}

Write-Host "Found $($nonCompliantResults.Count) non-compliant controls requiring remediation" -ForegroundColor Yellow

# Remediation Templates
$RemediationTemplates = @{
    "Registry" = @{
        Template = "Set-ItemProperty -Path '{Path}' -Name '{Setting}' -Value {Value} -Force"
        BackupTemplate = "Get-ItemProperty -Path '{Path}' -Name '{Setting}' -ErrorAction SilentlyContinue"
        CreatePathTemplate = "if (!(Test-Path '{Path}')) { New-Item -Path '{Path}' -Force }"
    }
    "Service" = @{
        Template = "Set-Service -Name '{ServiceName}' -StartupType {StartupType}"
        BackupTemplate = "Get-Service -Name '{ServiceName}' | Select-Object Name, Status, StartType"
        StopTemplate = "Stop-Service -Name '{ServiceName}' -Force -ErrorAction SilentlyContinue"
    }
    "SecurityPolicy" = @{
        Template = "# Configure via Group Policy or secedit: {Description}"
        BackupTemplate = "secedit /export /cfg backup_{Setting}.cfg"
    }
    "AuditPolicy" = @{
        Template = "auditpol /set /subcategory:'{Subcategory}' /{Setting}:enable"
        BackupTemplate = "auditpol /get /subcategory:'{Subcategory}'"
    }
}

function New-RemediationScript {
    param(
        [array]$NonCompliantControls,
        [string]$TargetComputer,
        [string]$OutputPath,
        [switch]$IncludeBackup
    )
    
    $scriptPath = Join-Path $OutputPath "CIS-Remediation-$TargetComputer-$(Get-Date -Format 'yyyyMMdd-HHmmss').ps1"
    $backupPath = Join-Path $OutputPath "CIS-Backup-$TargetComputer-$(Get-Date -Format 'yyyyMMdd-HHmmss').ps1"
    
    $scriptContent = @"
#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    CIS Controls v8 Remediation Script for $TargetComputer
    
.DESCRIPTION
    Auto-generated remediation script to address non-compliant CIS controls
    Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    Target Computer: $TargetComputer
    Non-compliant Controls: $($NonCompliantControls.Count)
#>

Write-Host "Starting CIS Controls v8 remediation for $TargetComputer" -ForegroundColor Green
Write-Host "Remediation Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Green
Write-Host "Controls to remediate: $($NonCompliantControls.Count)" -ForegroundColor Yellow

`$remediationLog = @()
`$errorCount = 0
`$successCount = 0

function Write-RemediationLog {
    param([string]`$Message, [string]`$Level = "INFO")
    `$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    `$logEntry = "[`$timestamp] [`$Level] `$Message"
    Write-Host `$logEntry
    `$script:remediationLog += `$logEntry
}

"@

    $backupContent = @"
#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    CIS Controls v8 Backup Script for $TargetComputer
    
.DESCRIPTION
    Backup current settings before CIS remediation
    Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
#>

Write-Host "Creating backup of current CIS control settings for $TargetComputer" -ForegroundColor Green

"@

    foreach ($control in $NonCompliantControls) {
        $controlId = $control.ControlID
        $controlName = $control.ControlName
        $controlType = $control.Type
        
        $scriptContent += @"

# ===== Control $controlId`: $controlName =====
Write-RemediationLog "Remediating Control $controlId`: $controlName" "INFO"

try {
"@

        # Generate backup commands if requested
        if ($IncludeBackup) {
            $backupContent += @"

# Backup for Control $controlId`: $controlName
Write-Host "Backing up Control $controlId`: $controlName"
try {
"@
        }
        
        switch ($controlType) {
            "Registry" {
                $path = $control.Path -replace "HKLM:", "HKLM:"
                $setting = $control.Setting
                $value = $control.RecommendedValue
                
                if ($IncludeBackup) {
                    $backupContent += @"
    `$currentValue = Get-ItemProperty -Path '$path' -Name '$setting' -ErrorAction SilentlyContinue
    if (`$currentValue) {
        Write-Host "Current value for $setting`: `$(`$currentValue.$setting)"
        `$currentValue | Export-Clixml -Path "backup_$($controlId)_$setting.xml"
    }
"@
                }
                
                $scriptContent += @"
    # Ensure registry path exists
    if (!(Test-Path '$path')) {
        New-Item -Path '$path' -Force | Out-Null
        Write-RemediationLog "Created registry path: $path" "INFO"
    }
    
    # Set registry value
    Set-ItemProperty -Path '$path' -Name '$setting' -Value $value -Force
    Write-RemediationLog "Set $path\$setting to $value" "SUCCESS"
    `$script:successCount++
"@
            }
            "Service" {
                $serviceName = ($control.Path -split "\\")[-1]
                $startType = switch ($control.RecommendedValue) {
                    4 { "Disabled" }
                    3 { "Manual" }
                    2 { "Automatic" }
                    default { "Manual" }
                }
                
                if ($IncludeBackup) {
                    $backupContent += @"
    `$service = Get-Service -Name '$serviceName' -ErrorAction SilentlyContinue
    if (`$service) {
        `$service | Select-Object Name, Status, StartType | Export-Clixml -Path "backup_$($controlId)_$serviceName.xml"
        Write-Host "Backed up service $serviceName configuration"
    }
"@
                }
                
                $scriptContent += @"
    # Configure service
    `$service = Get-Service -Name '$serviceName' -ErrorAction SilentlyContinue
    if (`$service) {
        if (`$service.Status -eq 'Running' -and '$startType' -eq 'Disabled') {
            Stop-Service -Name '$serviceName' -Force
            Write-RemediationLog "Stopped service: $serviceName" "INFO"
        }
        Set-Service -Name '$serviceName' -StartupType $startType
        Write-RemediationLog "Set service $serviceName startup type to $startType" "SUCCESS"
        `$script:successCount++
    } else {
        Write-RemediationLog "Service $serviceName not found" "WARNING"
    }
"@
            }
            "SecurityPolicy" {
                $scriptContent += @"
    # Security Policy: $($control.Description)
    Write-RemediationLog "Manual configuration required for: $($control.Description)" "WARNING"
    Write-RemediationLog "Use Group Policy or Local Security Policy to configure this setting" "INFO"
"@
            }
            "AuditPolicy" {
                $subcategory = $control.Setting
                $setting = $control.RecommendedValue.ToLower()
                
                if ($IncludeBackup) {
                    $backupContent += @"
    `$currentAudit = auditpol /get /subcategory:'$subcategory' /r | ConvertFrom-Csv
    `$currentAudit | Export-Clixml -Path "backup_$($controlId)_audit.xml"
    Write-Host "Backed up audit policy for $subcategory"
"@
                }
                
                $scriptContent += @"
    # Configure audit policy
    `$result = auditpol /set /subcategory:'$subcategory' /$($setting):enable
    if (`$LASTEXITCODE -eq 0) {
        Write-RemediationLog "Configured audit policy for $subcategory`: $setting" "SUCCESS"
        `$script:successCount++
    } else {
        Write-RemediationLog "Failed to configure audit policy for $subcategory" "ERROR"
        `$script:errorCount++
    }
"@
            }
        }
        
        $scriptContent += @"
}
catch {
    Write-RemediationLog "Error remediating Control $controlId`: `$(`$_.Exception.Message)" "ERROR"
    `$script:errorCount++
}

"@

        if ($IncludeBackup) {
            $backupContent += @"
}
catch {
    Write-Host "Error backing up Control $controlId`: `$(`$_.Exception.Message)" -ForegroundColor Red
}

"@
        }
    }
    
    $scriptContent += @"

# ===== REMEDIATION SUMMARY =====
Write-Host "`n===== REMEDIATION SUMMARY =====" -ForegroundColor Green
Write-Host "Total Controls Processed: $($NonCompliantControls.Count)" -ForegroundColor White
Write-Host "Successful Remediations: `$successCount" -ForegroundColor Green
Write-Host "Errors Encountered: `$errorCount" -ForegroundColor Red
Write-Host "Completion Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White

# Save remediation log
`$logPath = "CIS-Remediation-Log-$TargetComputer-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
`$remediationLog | Out-File -FilePath `$logPath -Encoding UTF8
Write-Host "Remediation log saved to: `$logPath" -ForegroundColor Green

if (`$errorCount -eq 0) {
    Write-Host "All remediations completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Some remediations failed. Please review the log for details." -ForegroundColor Yellow
    exit 1
}
"@

    # Write scripts to files
    $scriptContent | Out-File -FilePath $scriptPath -Encoding UTF8
    Write-Host "Remediation script generated: $scriptPath" -ForegroundColor Green
    
    if ($IncludeBackup) {
        $backupContent += @"

Write-Host "Backup completed successfully!" -ForegroundColor Green
Write-Host "Backup files saved to current directory" -ForegroundColor Green
"@
        $backupContent | Out-File -FilePath $backupPath -Encoding UTF8
        Write-Host "Backup script generated: $backupPath" -ForegroundColor Green
    }
    
    return @{
        RemediationScript = $scriptPath
        BackupScript = if ($IncludeBackup) { $backupPath } else { $null }
    }
}

function New-GroupPolicyRemediationGuide {
    param(
        [array]$NonCompliantControls,
        [string]$OutputPath
    )

    $guidePath = Join-Path $OutputPath "CIS-GroupPolicy-Remediation-Guide-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"

    $guideContent = @"
CIS Controls v8 Group Policy Remediation Guide
Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
==============================================

This guide provides Group Policy paths and settings for CIS controls that require
Group Policy configuration rather than direct PowerShell remediation.

"@

    $gpControls = $NonCompliantControls | Where-Object { $_.Type -eq "SecurityPolicy" -or $_.Description -match "Group Policy" }

    if ($gpControls.Count -eq 0) {
        $guideContent += "`nNo Group Policy configurations required for the current non-compliant controls.`n"
    } else {
        foreach ($control in $gpControls) {
            $guideContent += @"

Control $($control.ControlID): $($control.ControlName)
$("-" * 50)
Description: $($control.Description)
Current Value: $($control.CurrentValue)
Recommended Value: $($control.RecommendedValue)
Risk Level: $($control.RiskLevel)

Group Policy Path:
"@

            switch ($control.ControlID) {
                "5.1" {
                    $guideContent += @"
Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Password Policy
Setting: Minimum password length
Value: $($control.RecommendedValue) characters
"@
                }
                "5.2" {
                    $guideContent += @"
Computer Configuration > Policies > Windows Settings > Security Settings > Account Policies > Account Lockout Policy
Setting: Account lockout threshold
Value: $($control.RecommendedValue) invalid logon attempts
"@
                }
                "6.1" {
                    $guideContent += @"
Computer Configuration > Policies > Windows Settings > Security Settings > Local Policies > User Rights Assignment
Setting: Access this computer from the network
Value: Remove unauthorized users/groups, keep only: $($control.RecommendedValue -join ", ")
"@
                }
                default {
                    $guideContent += @"
Manual configuration required - refer to CIS Windows Server 2019 Benchmark documentation
Setting: $($control.Setting)
Recommended Value: $($control.RecommendedValue)
"@
                }
            }

            $guideContent += "`n"
        }
    }

    $guideContent += @"

Additional Notes:
- Apply Group Policy settings through Group Policy Management Console (GPMC)
- Test changes in a non-production environment first
- Allow time for Group Policy replication in domain environments
- Use 'gpupdate /force' to immediately apply changes locally
- Verify settings with 'gpresult /r' or 'rsop.msc'

For detailed configuration steps, refer to the CIS Windows Server 2019 Benchmark documentation.
"@

    $guideContent | Out-File -FilePath $guidePath -Encoding UTF8
    Write-Host "Group Policy remediation guide generated: $guidePath" -ForegroundColor Green

    return $guidePath
}

# Main execution
Write-Host "CIS Controls v8 Remediation Generator" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Validate output directory
if (!(Test-Path $OutputPath)) {
    try {
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
        Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to create output directory: $($_.Exception.Message)"
        exit 1
    }
}

# Group non-compliant results by computer
$computerGroups = $nonCompliantResults | Group-Object ComputerName

$generatedScripts = @()

foreach ($computerGroup in $computerGroups) {
    $computer = $computerGroup.Name
    $controls = $computerGroup.Group

    Write-Host "`nGenerating remediation scripts for: $computer" -ForegroundColor Yellow
    Write-Host "Non-compliant controls: $($controls.Count)" -ForegroundColor Yellow

    # Generate remediation script
    $scriptResult = New-RemediationScript -NonCompliantControls $controls -TargetComputer $computer -OutputPath $OutputPath -IncludeBackup:$CreateBackup
    $generatedScripts += $scriptResult

    # Display control summary
    $highRiskControls = $controls | Where-Object { $_.RiskLevel -eq "High" }
    $mediumRiskControls = $controls | Where-Object { $_.RiskLevel -eq "Medium" }

    Write-Host "  High Risk Controls: $($highRiskControls.Count)" -ForegroundColor Red
    Write-Host "  Medium Risk Controls: $($mediumRiskControls.Count)" -ForegroundColor Yellow
}

# Generate Group Policy guide if requested
if ($GenerateGroupPolicyScript) {
    $gpGuide = New-GroupPolicyRemediationGuide -NonCompliantControls $nonCompliantResults -OutputPath $OutputPath
    $generatedScripts += @{ GroupPolicyGuide = $gpGuide }
}

# Summary
Write-Host "`n===== REMEDIATION GENERATION SUMMARY =====" -ForegroundColor Green
Write-Host "Computers processed: $($computerGroups.Count)" -ForegroundColor White
Write-Host "Total non-compliant controls: $($nonCompliantResults.Count)" -ForegroundColor White
Write-Host "High risk issues: $(($nonCompliantResults | Where-Object { $_.RiskLevel -eq 'High' }).Count)" -ForegroundColor Red
Write-Host "Medium risk issues: $(($nonCompliantResults | Where-Object { $_.RiskLevel -eq 'Medium' }).Count)" -ForegroundColor Yellow
Write-Host "Scripts generated: $($generatedScripts.Count)" -ForegroundColor Green

Write-Host "`nGenerated Files:" -ForegroundColor Green
foreach ($script in $generatedScripts) {
    if ($script.RemediationScript) {
        Write-Host "  Remediation: $($script.RemediationScript)" -ForegroundColor White
    }
    if ($script.BackupScript) {
        Write-Host "  Backup: $($script.BackupScript)" -ForegroundColor White
    }
    if ($script.GroupPolicyGuide) {
        Write-Host "  GP Guide: $($script.GroupPolicyGuide)" -ForegroundColor White
    }
}

Write-Host "`nIMPORTANT NOTES:" -ForegroundColor Yellow
Write-Host "- Review all generated scripts before execution" -ForegroundColor Yellow
Write-Host "- Test in non-production environment first" -ForegroundColor Yellow
Write-Host "- Run backup scripts before remediation if enabled" -ForegroundColor Yellow
Write-Host "- Some controls may require manual Group Policy configuration" -ForegroundColor Yellow
Write-Host "- Restart may be required for some changes to take effect" -ForegroundColor Yellow

Write-Host "`nRemediation scripts generated successfully!" -ForegroundColor Green
